<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>TaxiRapport</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.1</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	
	<!-- Allows network requests -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>

	<!-- Required for accessing the camera -->
	<key>NSCameraUsageDescription</key>
	<string>TaxiRapport uses the camera to let users take and upload a profile picture for their account.</string>

	
	<!-- Required for saving images/screenshots -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>TaxiRapport need access to your library to allow you to upload and save photos.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>TaxiRapport need access to your photo library to save and share screenshots.</string>
	<key>NSDocumentsDirectory</key>
	<string>Allow TaxiRapport to save reports</string>

	<!-- Optional: Required if you use location services -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>TaxiRapport need your location to provide accurate taxi reports.</string>

	<!-- Allows file storage in the Documents directory -->
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>

	<!-- Custom Fonts -->
	<key>UIAppFonts</key>
	<array>
		<string>AntDesign.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>Feather.ttf</string>
	</array>

	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
