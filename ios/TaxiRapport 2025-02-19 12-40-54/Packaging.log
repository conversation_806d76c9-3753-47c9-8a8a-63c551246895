2025-02-19 07:26:05 +0000  Initial pipeline context: <IDEDistributionProcessingPipelineContext: 0x17feda460; archive(resolved)="<IDEArchive: 0x60001b37c3c0>", distributionTask(resolved)="2", distributionDestination(resolved)="2", distributionMethod(resolved)="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team(resolved)="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	Chain (18, self inclusive):
	<IDEDistributionProcessingPipelineContext: 0x17feda460; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionProcessingPipelineContext: 0x17fede080; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x17a840dd0; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x2b7e39a30; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x2bdef57c0; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x2b7c3de60; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x17fed90c0; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x17e904650; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x17fee0c80; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x17d966bc0; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x17c6b4b20; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x2bdee1c20; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x17c4f2ae0; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x6000005a3240; teamID='9J6MH85TLG', teamName='Saud Khan', teamType='Individual', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x17c4f24b0; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="(null)">
	<IDEDistributionContext: 0x1243ea7c0; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x60005db4a110>", team="(null)">
	<IDEDistributionContext: 0x17c6db6b0; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="(null)", team="(null)">
	<IDEDistributionContext: 0x17c6d11e0; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="(null)", team="(null)">
	<IDEDistributionContext: 0x2bde3f8a0; archive = "<IDEArchive: 0x60001b37c3c0>", distributionMethod="(null)", team="(null)">
</IDEDistributionProcessingPipelineContext: 0x17feda460>
2025-02-19 07:26:05 +0000  Processing step: IDEDistributionCreateDestRootStep
2025-02-19 07:26:05 +0000  Processing step: IDEDistributionCopyItemStep
2025-02-19 07:26:05 +0000  Running /usr/bin/ditto '-V' '/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app'
2025-02-19 07:26:05 +0000  >>> Copying /Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app 
2025-02-19 07:26:05 +0000  copying file ./_CodeSignature/CodeResources ... 
2025-02-19 07:26:05 +0000  49026 bytes for ./_CodeSignature/CodeResources
2025-02-19 07:26:05 +0000  copying file ./Fontisto.ttf ... 
2025-02-19 07:26:05 +0000  313528 bytes for ./Fontisto.ttf
2025-02-19 07:26:05 +0000  copying file ./Octicons.ttf ... 
2025-02-19 07:26:05 +0000  49404 bytes for ./Octicons.ttf
2025-02-19 07:26:05 +0000  copying file ./Feather.ttf ... 
2025-02-19 07:26:05 +0000  56228 bytes for ./Feather.ttf
2025-02-19 07:26:05 +0000  copying file ./FontAwesome6_Regular.ttf ... 
2025-02-19 07:26:05 +0000  63348 bytes for ./FontAwesome6_Regular.ttf
2025-02-19 07:26:05 +0000  copying file ./Entypo.ttf ... 
2025-02-19 07:26:05 +0000  66200 bytes for ./Entypo.ttf
2025-02-19 07:26:05 +0000  copying file ./TaxiRapport ... 
2025-02-19 07:26:05 +0000  5184720 bytes for ./TaxiRapport
2025-02-19 07:26:05 +0000  copying file ./FontAwesome5_Brands.ttf ... 
2025-02-19 07:26:05 +0000  134040 bytes for ./FontAwesome5_Brands.ttf
2025-02-19 07:26:05 +0000  copying file ./MaterialCommunityIcons.ttf ... 
2025-02-19 07:26:05 +0000  1147844 bytes for ./MaterialCommunityIcons.ttf
2025-02-19 07:26:05 +0000  copying file ./AntDesign.ttf ... 
2025-02-19 07:26:05 +0000  70344 bytes for ./AntDesign.ttf
2025-02-19 07:26:05 +0000  copying file ./Foundation.ttf ... 
2025-02-19 07:26:05 +0000  56976 bytes for ./Foundation.ttf
2025-02-19 07:26:05 +0000  copying file ./Ionicons.ttf ... 
2025-02-19 07:26:05 +0000  442604 bytes for ./Ionicons.ttf
2025-02-19 07:26:05 +0000  copying file ./FontAwesome5_Solid.ttf ... 
2025-02-19 07:26:05 +0000  202744 bytes for ./FontAwesome5_Solid.ttf
2025-02-19 07:26:05 +0000  copying file ./FontAwesome5_Regular.ttf ... 
2025-02-19 07:26:05 +0000  33736 bytes for ./FontAwesome5_Regular.ttf
2025-02-19 07:26:05 +0000  copying file ./FontAwesome.ttf ... 
2025-02-19 07:26:05 +0000  165548 bytes for ./FontAwesome.ttf
2025-02-19 07:26:05 +0000  copying file ./Zocial.ttf ... 
2025-02-19 07:26:05 +0000  25788 bytes for ./Zocial.ttf
2025-02-19 07:26:05 +0000  copying file ./Assets.car ... 
2025-02-19 07:26:05 +0000  349272 bytes for ./Assets.car
2025-02-19 07:26:05 +0000  copying file ./EvilIcons.ttf ... 
2025-02-19 07:26:05 +0000  13456 bytes for ./EvilIcons.ttf
2025-02-19 07:26:05 +0000  copying file ./main.jsbundle ... 
2025-02-19 07:26:05 +0000  5229982 bytes for ./main.jsbundle
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/de.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/de.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1312 bytes for ./RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/he.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/he.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1444 bytes for ./RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/ar.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/ar.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1500 bytes for ./RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/el.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/el.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1600 bytes for ./RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1296 bytes for ./RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/ja.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/ja.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1436 bytes for ./RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/en.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/en.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1296 bytes for ./RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/uk.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/uk.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1564 bytes for ./RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/nb.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/nb.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1296 bytes for ./RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1268 bytes for ./RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/es.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/es.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1364 bytes for ./RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/da.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/da.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1300 bytes for ./RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/it.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/it.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1388 bytes for ./RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/sk.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/sk.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1356 bytes for ./RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1368 bytes for ./RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/ms.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/ms.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1292 bytes for ./RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/sv.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/sv.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1300 bytes for ./RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/cs.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/cs.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1368 bytes for ./RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/ko.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/ko.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1332 bytes for ./RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1308 bytes for ./RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/hu.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/hu.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1348 bytes for ./RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/tr.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/tr.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1360 bytes for ./RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/pl.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/pl.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1340 bytes for ./RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/vi.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/vi.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1352 bytes for ./RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/ru.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/ru.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1576 bytes for ./RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/fr.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/fr.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1360 bytes for ./RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/fi.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/fi.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1340 bytes for ./RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/id.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/id.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1300 bytes for ./RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/nl.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/nl.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1324 bytes for ./RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/th.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/th.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1784 bytes for ./RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/pt.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/pt.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1356 bytes for ./RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/zu.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/zu.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  2076 bytes for ./RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1372 bytes for ./RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/ro.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/ro.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1344 bytes for ./RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/Info.plist ... 
2025-02-19 07:26:05 +0000  777 bytes for ./RCTI18nStrings.bundle/Info.plist
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/hr.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/hr.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1352 bytes for ./RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/hi.lproj/Localizable.strings ... 
2025-02-19 07:26:05 +0000  64 bytes for ./RCTI18nStrings.bundle/hi.lproj/Localizable.strings
2025-02-19 07:26:05 +0000  copying file ./RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:05 +0000  1640 bytes for ./RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin
2025-02-19 07:26:05 +0000  copying file ./FontAwesome6_Solid.ttf ... 
2025-02-19 07:26:05 +0000  394668 bytes for ./FontAwesome6_Solid.ttf
2025-02-19 07:26:05 +0000  copying file ./PrivacyInfo.xcprivacy ... 
2025-02-19 07:26:05 +0000  986 bytes for ./PrivacyInfo.xcprivacy
2025-02-19 07:26:05 +0000  copying file ./LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib ... 
2025-02-19 07:26:05 +0000  3819 bytes for ./LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib
2025-02-19 07:26:05 +0000  copying file ./LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib ... 
2025-02-19 07:26:05 +0000  924 bytes for ./LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib
2025-02-19 07:26:05 +0000  copying file ./LaunchScreen.storyboardc/Info.plist ... 
2025-02-19 07:26:05 +0000  258 bytes for ./LaunchScreen.storyboardc/Info.plist
2025-02-19 07:26:05 +0000  copying file ./Frameworks/hermes.framework/_CodeSignature/CodeResources ... 
2025-02-19 07:26:05 +0000  1798 bytes for ./Frameworks/hermes.framework/_CodeSignature/CodeResources
2025-02-19 07:26:05 +0000  copying file ./Frameworks/hermes.framework/hermes ... 
2025-02-19 07:26:05 +0000  4444944 bytes for ./Frameworks/hermes.framework/hermes
2025-02-19 07:26:05 +0000  copying file ./Frameworks/hermes.framework/Info.plist ... 
2025-02-19 07:26:05 +0000  815 bytes for ./Frameworks/hermes.framework/Info.plist
2025-02-19 07:26:05 +0000  copying file ./SimpleLineIcons.ttf ... 
2025-02-19 07:26:05 +0000  54056 bytes for ./SimpleLineIcons.ttf
2025-02-19 07:26:05 +0000  copying file ./FontAwesome6_Brands.ttf ... 
2025-02-19 07:26:05 +0000  189684 bytes for ./FontAwesome6_Brands.ttf
2025-02-19 07:26:05 +0000  copying file ./embedded.mobileprovision ... 
2025-02-19 07:26:05 +0000  12219 bytes for ./embedded.mobileprovision
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-ratings/dist/images/bell.png ... 
2025-02-19 07:26:05 +0000  2989 bytes for ./assets/node_modules/react-native-ratings/dist/images/bell.png
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-ratings/dist/images/airbnb-star-selected.png ... 
2025-02-19 07:26:05 +0000  934 bytes for ./assets/node_modules/react-native-ratings/dist/images/airbnb-star-selected.png
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-ratings/dist/images/heart.png ... 
2025-02-19 07:26:05 +0000  1928 bytes for ./assets/node_modules/react-native-ratings/dist/images/heart.png
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-ratings/dist/images/airbnb-star.png ... 
2025-02-19 07:26:05 +0000  930 bytes for ./assets/node_modules/react-native-ratings/dist/images/airbnb-star.png
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-ratings/dist/images/rocket.png ... 
2025-02-19 07:26:05 +0000  4050 bytes for ./assets/node_modules/react-native-ratings/dist/images/rocket.png
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-ratings/dist/images/star.png ... 
2025-02-19 07:26:05 +0000  1961 bytes for ./assets/node_modules/react-native-ratings/dist/images/star.png
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/@react-navigation/elements/src/assets/<EMAIL> ... 
2025-02-19 07:26:05 +0000  761 bytes for ./assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/@react-navigation/elements/src/assets/<EMAIL> ... 
2025-02-19 07:26:05 +0000  405 bytes for ./assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/@react-navigation/elements/src/assets/back-icon.png ... 
2025-02-19 07:26:05 +0000  290 bytes for ./assets/node_modules/@react-navigation/elements/src/assets/back-icon.png
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png ... 
2025-02-19 07:26:05 +0000  913 bytes for ./assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-element-dropdown/src/assets/down.png ... 
2025-02-19 07:26:05 +0000  186 bytes for ./assets/node_modules/react-native-element-dropdown/src/assets/down.png
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-element-dropdown/src/assets/close.png ... 
2025-02-19 07:26:05 +0000  208 bytes for ./assets/node_modules/react-native-element-dropdown/src/assets/close.png
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-calendars/src/calendar/img/<EMAIL> ... 
2025-02-19 07:26:05 +0000  458 bytes for ./assets/node_modules/react-native-calendars/src/calendar/img/<EMAIL>
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-calendars/src/calendar/img/<EMAIL> ... 
2025-02-19 07:26:05 +0000  462 bytes for ./assets/node_modules/react-native-calendars/src/calendar/img/<EMAIL>
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-calendars/src/img/<EMAIL> ... 
2025-02-19 07:26:05 +0000  508 bytes for ./assets/node_modules/react-native-calendars/src/img/<EMAIL>
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-calendars/src/img/<EMAIL> ... 
2025-02-19 07:26:05 +0000  354 bytes for ./assets/node_modules/react-native-calendars/src/img/<EMAIL>
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-calendars/src/img/up.png ... 
2025-02-19 07:26:05 +0000  215 bytes for ./assets/node_modules/react-native-calendars/src/img/up.png
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-calendars/src/img/down.png ... 
2025-02-19 07:26:05 +0000  208 bytes for ./assets/node_modules/react-native-calendars/src/img/down.png
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-calendars/src/img/<EMAIL> ... 
2025-02-19 07:26:05 +0000  347 bytes for ./assets/node_modules/react-native-calendars/src/img/<EMAIL>
2025-02-19 07:26:05 +0000  copying file ./assets/node_modules/react-native-calendars/src/img/<EMAIL> ... 
2025-02-19 07:26:05 +0000  504 bytes for ./assets/node_modules/react-native-calendars/src/img/<EMAIL>
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/default/defaultPerson.jpeg ... 
2025-02-19 07:26:05 +0000  1302 bytes for ./assets/src/assets/default/defaultPerson.jpeg
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/bgImg.png ... 
2025-02-19 07:26:05 +0000  20750 bytes for ./assets/src/assets/Image/bgImg.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/Img5.png ... 
2025-02-19 07:26:05 +0000  7090 bytes for ./assets/src/assets/Image/Img5.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/Img4.png ... 
2025-02-19 07:26:05 +0000  11724 bytes for ./assets/src/assets/Image/Img4.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/logo.png ... 
2025-02-19 07:26:05 +0000  82500 bytes for ./assets/src/assets/Image/logo.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/Img3.png ... 
2025-02-19 07:26:05 +0000  12016 bytes for ./assets/src/assets/Image/Img3.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/Img2.png ... 
2025-02-19 07:26:05 +0000  9777 bytes for ./assets/src/assets/Image/Img2.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/Menu/Harddrive.png ... 
2025-02-19 07:26:05 +0000  621 bytes for ./assets/src/assets/Image/Menu/Harddrive.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/Menu/Mail.png ... 
2025-02-19 07:26:05 +0000  688 bytes for ./assets/src/assets/Image/Menu/Mail.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/Menu/User.png ... 
2025-02-19 07:26:05 +0000  721 bytes for ./assets/src/assets/Image/Menu/User.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/Menu/localtaxi.png ... 
2025-02-19 07:26:05 +0000  556 bytes for ./assets/src/assets/Image/Menu/localtaxi.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/Menu/Upload.png ... 
2025-02-19 07:26:05 +0000  367 bytes for ./assets/src/assets/Image/Menu/Upload.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/Menu/Phone.png ... 
2025-02-19 07:26:05 +0000  917 bytes for ./assets/src/assets/Image/Menu/Phone.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/slide.png ... 
2025-02-19 07:26:05 +0000  429896 bytes for ./assets/src/assets/Image/slide.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/techcreator.png ... 
2025-02-19 07:26:05 +0000  428959 bytes for ./assets/src/assets/Image/techcreator.png
2025-02-19 07:26:05 +0000  copying file ./assets/src/assets/Image/TaxiBanner.png ... 
2025-02-19 07:26:05 +0000  531988 bytes for ./assets/src/assets/Image/TaxiBanner.png
2025-02-19 07:26:05 +0000  copying file ./Info.plist ... 
2025-02-19 07:26:05 +0000  1427 bytes for ./Info.plist
2025-02-19 07:26:05 +0000  copying file ./RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy ... 
2025-02-19 07:26:05 +0000  512 bytes for ./RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy
2025-02-19 07:26:05 +0000  copying file ./RNCAsyncStorage_resources.bundle/Info.plist ... 
2025-02-19 07:26:05 +0000  799 bytes for ./RNCAsyncStorage_resources.bundle/Info.plist
2025-02-19 07:26:05 +0000  copying file ./PkgInfo ... 
2025-02-19 07:26:05 +0000  8 bytes for ./PkgInfo
2025-02-19 07:26:05 +0000  copying file ./MaterialIcons.ttf ... 
2025-02-19 07:26:05 +0000  356840 bytes for ./MaterialIcons.ttf
2025-02-19 07:26:05 +0000  /usr/bin/ditto exited with 0
2025-02-19 07:26:05 +0000  Processing step: IDEDistributionEmbedProfileStep
2025-02-19 07:26:05 +0000  Skipping profile for item: <IDEDistributionItem: 0x60001bccf660; bundleID='dev.hermesengine.iphoneos', path='<DVTFilePath:0x60000f6c1480:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/Frameworks/hermes.framework'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60002fa714f0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x60000a3670c0; name='Apple Development: Saud Khan (HA5WX37D9V)', hash='D6868630DA90DFCE6FF8EE13FA3FD369F33EBA81', serialNumber='5047EE2BA68B369F6D8FBEEE275C6C59', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-02-07 13:31:34 +0000''>', entitlements='(null)', teamID='9J6MH85TLG', identifier='dev.hermesengine.iphoneos', executablePath='<DVTFilePath:0x60000f139730:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/Frameworks/hermes.framework/hermes'>', hardenedRuntime='0'>'>
2025-02-19 07:26:05 +0000  Processing step: IDEDistributionInfoPlistStep
2025-02-19 07:26:05 +0000  Skipping Info.plist step because item: <IDEDistributionItem: 0x60001bccf660; bundleID='dev.hermesengine.iphoneos', path='<DVTFilePath:0x60000f6c1480:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/Frameworks/hermes.framework'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60002fa714f0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x6000023f0000; name='Apple Development: Saud Khan (HA5WX37D9V)', hash='D6868630DA90DFCE6FF8EE13FA3FD369F33EBA81', serialNumber='5047EE2BA68B369F6D8FBEEE275C6C59', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-02-07 13:31:34 +0000''>', entitlements='(null)', teamID='9J6MH85TLG', identifier='dev.hermesengine.iphoneos', executablePath='<DVTFilePath:0x60000f139730:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/Frameworks/hermes.framework/hermes'>', hardenedRuntime='0'>'> is a framework or embedded in a framework.
2025-02-19 07:26:05 +0000  Skipping setting DTXcodeBuildDistribution because toolsBuildVersionName was nil.
2025-02-19 07:26:05 +0000  Processing step: IDEDistributionItemRemovalStep
2025-02-19 07:26:05 +0000  Processing step: IDEDistributionAppThinningPlistStep
2025-02-19 07:26:05 +0000  Skipping step: IDEDistributionAppThinningPlistStep because it said so
2025-02-19 07:26:05 +0000  Processing step: IDEDistributionCompileBitcodeStep
2025-02-19 07:26:05 +0000  Skipping step: IDEDistributionCompileBitcodeStep because it said so
2025-02-19 07:26:05 +0000  Processing step: IDEDistributionCodeSlimmingStep
2025-02-19 07:26:05 +0000  Processing step: IDEDistributionCopyBCSymbolMapsStep
2025-02-19 07:26:05 +0000  Processing step: IDEDistributionSymbolsStep
2025-02-19 07:26:05 +0000  Processing symbols for hermes.framework
2025-02-19 07:26:05 +0000  Running /usr/bin/rsync '-8aPhhE' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app/Frameworks/hermes.framework' '--link-dest' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app/Frameworks' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj'
2025-02-19 07:26:05 +0000  building file list ... 
2025-02-19 07:26:05 +0000   0 files...
2025-02-19 07:26:05 +0000  5 files to consider
2025-02-19 07:26:05 +0000  hermes.framework/
2025-02-19 07:26:05 +0000  hermes.framework/_CodeSignature/
rsync: link "/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/hermes.framework/_CodeSignature/CodeResources" => /var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app/Frameworks/hermes.framework/_CodeSignature/CodeResources failed: Operation not permitted (1)
2025-02-19 07:26:05 +0000  hermes.framework/_CodeSignature/CodeResources
2025-02-19 07:26:05 +0000  
2025-02-19 07:26:05 +0000  sent 236 bytes  received 38 bytes  548.00 bytes/sec
total size is 4.24M  speedup is 16231.96
2025-02-19 07:26:05 +0000  /usr/bin/rsync exited with 0
2025-02-19 07:26:05 +0000  Running /usr/bin/rsync '-8aPhhE' '/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/dSYMs/' '--link-dest' '/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/dSYMs/' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj'
2025-02-19 07:26:05 +0000  building file list ... 
2025-02-19 07:26:05 +0000   0 files...
2025-02-19 07:26:05 +0000  10 files to consider
2025-02-19 07:26:05 +0000  ./
2025-02-19 07:26:05 +0000  TaxiRapport.app.dSYM/
TaxiRapport.app.dSYM/Contents/
TaxiRapport.app.dSYM/Contents/Resources/
TaxiRapport.app.dSYM/Contents/Resources/DWARF/
2025-02-19 07:26:05 +0000  TaxiRapport.app.dSYM/Contents/Resources/Relocations/
TaxiRapport.app.dSYM/Contents/Resources/Relocations/aarch64/
2025-02-19 07:26:05 +0000  
2025-02-19 07:26:05 +0000  sent 364 bytes  received 62 bytes  852.00 bytes/sec
total size is 84.17M  speedup is 207175.35
2025-02-19 07:26:05 +0000  /usr/bin/rsync exited with 0
2025-02-19 07:26:05 +0000  Writing /var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Symbols/A56CDAAF-2027-33D2-A806-BA11E8C1B3EF.symbols
2025-02-19 07:26:05 +0000  Processing symbols for TaxiRapport.app
2025-02-19 07:26:05 +0000  Running /usr/bin/rsync '-8aPhhE' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app' '--link-dest' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj'
2025-02-19 07:26:05 +0000  building file list ... 
2025-02-19 07:26:05 +0000   0 files...
2025-02-19 07:26:05 +0000   100 files...
2025-02-19 07:26:05 +0000   200 files...
2025-02-19 07:26:05 +0000  212 files to consider
2025-02-19 07:26:05 +0000  TaxiRapport.app/
2025-02-19 07:26:05 +0000  TaxiRapport.app/Frameworks/
TaxiRapport.app/Frameworks/hermes.framework/
TaxiRapport.app/Frameworks/hermes.framework/_CodeSignature/
TaxiRapport.app/LaunchScreen.storyboardc/
2025-02-19 07:26:05 +0000  TaxiRapport.app/RCTI18nStrings.bundle/
TaxiRapport.app/RCTI18nStrings.bundle/ar.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/cs.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/da.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/de.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/el.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/en-GB.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/en.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/es-ES.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/es.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/fi.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/fr.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/he.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/hi.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/hr.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/hu.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/id.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/it.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/ja.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/ko.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/ms.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/nb.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/nl.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/pl.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/pt-PT.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/pt.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/ro.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/ru.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/sk.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/sv.lproj/
2025-02-19 07:26:05 +0000  TaxiRapport.app/RCTI18nStrings.bundle/th.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/tr.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/uk.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/vi.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/zh-Hans.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/zh-Hant-HK.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/zh-Hant.lproj/
TaxiRapport.app/RCTI18nStrings.bundle/zu.lproj/
TaxiRapport.app/RNCAsyncStorage_resources.bundle/
TaxiRapport.app/_CodeSignature/
TaxiRapport.app/assets/
TaxiRapport.app/assets/node_modules/
TaxiRapport.app/assets/node_modules/@react-navigation/
TaxiRapport.app/assets/node_modules/@react-navigation/elements/
TaxiRapport.app/assets/node_modules/@react-navigation/elements/src/
TaxiRapport.app/assets/node_modules/@react-navigation/elements/src/assets/
TaxiRapport.app/assets/node_modules/react-native-calendars/
TaxiRapport.app/assets/node_modules/react-native-calendars/src/
TaxiRapport.app/assets/node_modules/react-native-calendars/src/calendar/
TaxiRapport.app/assets/node_modules/react-native-calendars/src/calendar/img/
TaxiRapport.app/assets/node_modules/react-native-calendars/src/img/
TaxiRapport.app/assets/node_modules/react-native-element-dropdown/
TaxiRapport.app/assets/node_modules/react-native-element-dropdown/src/
TaxiRapport.app/assets/node_modules/react-native-element-dropdown/src/assets/
TaxiRapport.app/assets/node_modules/react-native-ratings/
TaxiRapport.app/assets/node_modules/react-native-ratings/dist/
TaxiRapport.app/assets/node_modules/react-native-ratings/dist/images/
TaxiRapport.app/assets/src/
TaxiRapport.app/assets/src/assets/
TaxiRapport.app/assets/src/assets/Image/
TaxiRapport.app/assets/src/assets/Image/Menu/
TaxiRapport.app/assets/src/assets/default/
2025-02-19 07:26:05 +0000  
sent 5.92K bytes  received 422 bytes  12.67K bytes/sec
2025-02-19 07:26:05 +0000  total size is 19.77M  speedup is 3195.26
2025-02-19 07:26:05 +0000  /usr/bin/rsync exited with 0
2025-02-19 07:26:05 +0000  Running /usr/bin/rsync '-8aPhhE' '/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/dSYMs/' '--link-dest' '/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/dSYMs/' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj'
2025-02-19 07:26:05 +0000  building file list ... 
2025-02-19 07:26:05 +0000   0 files...
2025-02-19 07:26:05 +0000  10 files to consider
2025-02-19 07:26:05 +0000  ./
2025-02-19 07:26:05 +0000  TaxiRapport.app.dSYM/
TaxiRapport.app.dSYM/Contents/
TaxiRapport.app.dSYM/Contents/Resources/
TaxiRapport.app.dSYM/Contents/Resources/DWARF/
TaxiRapport.app.dSYM/Contents/Resources/Relocations/
2025-02-19 07:26:05 +0000  TaxiRapport.app.dSYM/Contents/Resources/Relocations/aarch64/
2025-02-19 07:26:05 +0000  
2025-02-19 07:26:05 +0000  sent 382 bytes  received 80 bytes  924.00 bytes/sec
total size is 84.17M  speedup is 191031.81
2025-02-19 07:26:05 +0000  /usr/bin/rsync exited with 0
2025-02-19 07:26:06 +0000  Writing /var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Symbols/005AFA4A-F683-3AC6-8E49-EEEA0835634B.symbols
2025-02-19 07:26:06 +0000  Processing step: IDEDistributionCopyAppleProvidedContentStep
2025-02-19 07:26:06 +0000  Processing step: IDEDistributionAppThinningStep
2025-02-19 07:26:06 +0000  Skipping step: IDEDistributionAppThinningStep because it said so
2025-02-19 07:26:06 +0000  Processing step: IDEDistributionArchThinningStep
2025-02-19 07:26:06 +0000  Item /Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/Frameworks/hermes.framework doesn't have the entitlement com.apple.developer.web-browser-engine.host enabled, returning ["arm64e"]
2025-02-19 07:26:06 +0000  Archs to thin for item /Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/Frameworks/hermes.framework are ["arm64e"]
2025-02-19 07:26:06 +0000  Running /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app/Frameworks/hermes.framework/hermes' '-verify_arch' 'arm64e'
2025-02-19 07:26:06 +0000  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo exited with 1
2025-02-19 07:26:06 +0000  Skipping architecture thinning for item "hermes" because arch "arm64e" wasn't found
2025-02-19 07:26:06 +0000  Item /Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app doesn't have the entitlement com.apple.developer.web-browser-engine.host enabled, returning ["arm64e"]
2025-02-19 07:26:06 +0000  Archs to thin for item /Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app are ["arm64e"]
2025-02-19 07:26:06 +0000  Running /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app/TaxiRapport' '-verify_arch' 'arm64e'
2025-02-19 07:26:06 +0000  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo exited with 1
2025-02-19 07:26:06 +0000  Skipping architecture thinning for item "TaxiRapport" because arch "arm64e" wasn't found
2025-02-19 07:26:06 +0000  Processing step: IDEDistributionODRStep
2025-02-19 07:26:06 +0000  Processing step: IDEDistributionStripXattrsStep
2025-02-19 07:26:06 +0000  Skipping stripping extended attributes because the codesign step will strip them.
2025-02-19 07:26:06 +0000  Skipping stripping extended attributes because the codesign step will strip them.
2025-02-19 07:26:06 +0000  Processing step: IDEDistributionCodesignStep
2025-02-19 07:26:06 +0000  Entitlements for <IDEDistributionItem: 0x60001bccf660; bundleID='dev.hermesengine.iphoneos', path='<DVTFilePath:0x60000f6c1480:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/Frameworks/hermes.framework'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60002fa714f0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x60000a359a40; name='Apple Development: Saud Khan (HA5WX37D9V)', hash='D6868630DA90DFCE6FF8EE13FA3FD369F33EBA81', serialNumber='5047EE2BA68B369F6D8FBEEE275C6C59', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-02-07 13:31:34 +0000''>', entitlements='(null)', teamID='9J6MH85TLG', identifier='dev.hermesengine.iphoneos', executablePath='<DVTFilePath:0x60000f139730:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/Frameworks/hermes.framework/hermes'>', hardenedRuntime='0'>'>: {
}
2025-02-19 07:26:06 +0000  Associated App Clip Identifiers Filter: Skipping because "com.apple.developer.associated-appclip-app-identifiers" is not present
2025-02-19 07:26:06 +0000  Entitlements for <IDEDistributionItem: 0x60001bccf660; bundleID='dev.hermesengine.iphoneos', path='<DVTFilePath:0x60000f6c1480:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/Frameworks/hermes.framework'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60002fa714f0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x60000a35bf00; name='Apple Development: Saud Khan (HA5WX37D9V)', hash='D6868630DA90DFCE6FF8EE13FA3FD369F33EBA81', serialNumber='5047EE2BA68B369F6D8FBEEE275C6C59', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-02-07 13:31:34 +0000''>', entitlements='(null)', teamID='9J6MH85TLG', identifier='dev.hermesengine.iphoneos', executablePath='<DVTFilePath:0x60000f139730:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/Frameworks/hermes.framework/hermes'>', hardenedRuntime='0'>'> are: {
}
2025-02-19 07:26:06 +0000  Writing entitlements for <IDEDistributionItem: 0x60001bccf660; bundleID='dev.hermesengine.iphoneos', path='<DVTFilePath:0x60000f6c1480:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/Frameworks/hermes.framework'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60002fa714f0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x60000a359d40; name='Apple Development: Saud Khan (HA5WX37D9V)', hash='D6868630DA90DFCE6FF8EE13FA3FD369F33EBA81', serialNumber='5047EE2BA68B369F6D8FBEEE275C6C59', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-02-07 13:31:34 +0000''>', entitlements='(null)', teamID='9J6MH85TLG', identifier='dev.hermesengine.iphoneos', executablePath='<DVTFilePath:0x60000f139730:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/Frameworks/hermes.framework/hermes'>', hardenedRuntime='0'>'> to: /var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/entitlements~~~1cOVe9
2025-02-19 07:26:06 +0000  Sending request CD4FE211-D63B-4F02-9497-D3671B3D2330 to <https://developerservices2.apple.com/services/QH65B2/listTeams.action?clientId=XABBG36SBA> for session <DVTServicesAccountBasedSession: 0x60001bcf2fa0; account='<DVTAppleIDBasedDeveloperAccount: 0x6000000f6700; username='<EMAIL>'>'>.
Method: POST

Headers:
{
    Accept = "text/x-xml-plist";
    "Accept-Encoding" = "gzip, deflate";
    "Content-Length" = 368;
    "Content-Type" = "text/x-xml-plist";
    "User-Agent" = Xcode;
    "X-Apple-App-Info" = "com.apple.gs.xcode.auth";
    "X-Apple-GS-Token" = "AAAABLwIAAAAAGemDT8RDWdzLnhjb2RlLmF1dGi9AL5XCym1Gek/WVGQEkN1O0rWYh5MOo59ovBY5UI/rXv5C0DwtFGJDQX81kZsvzjBWeHJ0ZltQsAkd4Ud1uBtB1rwwr3ksEEaHKx94XhKBTiHy4/wM65oFqaWdwgKanGhpa5oAfmd2FaKrUi0rn3+4KzFY9ef";
    "X-Apple-I-Identity-Id" = "000568-10-f65e5f67-0de0-426d-a18b-72683542c140";
    "X-MMe-Client-Info" = "<Mac15,7> <macOS;14.1;23B2073> <com.apple.AuthKit/1 (com.apple.dt.Xcode/22618)>";
    "X-Mme-Device-Id" = "C455628C-F5FE-541C-B93B-2258A26AC0B5";
    "X-Xcode-Version" = "15.3 (15E204a)";
}

Payload:
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>clientId</key>
	<string>XABBG36SBA</string>
	<key>protocolVersion</key>
	<string>QH65B2</string>
	<key>requestId</key>
	<string>CD4FE211-D63B-4F02-9497-D3671B3D2330</string>
</dict>
</plist>
2025-02-19 07:26:06 +0000  Received response for CD4FE211-D63B-4F02-9497-D3671B3D2330 @ <https://developerservices2.apple.com/services/QH65B2/listTeams.action?clientId=XABBG36SBA>. Code = 0
2025-02-19 07:26:06 +0000  Response payload: {
    creationTimestamp = "2025-02-19T07:26:06Z";
    protocolVersion = QH65B2;
    requestId = "CD4FE211-D63B-4F02-9497-D3671B3D2330";
    requestUrl = "https://developerservices2.apple.com:443/services/QH65B2/listTeams.action?clientId=XABBG36SBA";
    responseId = "316feaf7-7ad9-4366-8bbe-1c709069f825";
    resultCode = 0;
    teams =     (
                {
            currentTeamMember =             {
                developerStatus = active;
                email = "<EMAIL>";
                firstName = Saud;
                lastName = Khan;
                personId = 1287536112;
                privileges =                 {
                };
                roles =                 (
                    "XCODE_FREE_USER",
                    AGENT,
                    "TEAM_ADMIN",
                    "MAC_PROGRAM_TEAM_ADMIN"
                );
                teamMemberId = HA5WX37D9V;
            };
            dateCreated = "2025-02-06 02:36:35 +0000";
            extendedTeamAttributes =             {
            };
            memberships =             (
                                {
                    dateStart = "2025-02-07 13:40:22 +0000";
                    deleteDevicesOnExpiry = 0;
                    inIosDeviceResetWindow = 0;
                    inRenewalWindow = 0;
                    membershipId = 8XNH6T8A57;
                    membershipProductId = fp22;
                    name = "Xcode Free Provisioning Program";
                    platform = ios;
                    status = activeNeverExpire;
                },
                                {
                    dateExpire = "2026-02-06 23:59:00 +0000";
                    dateStart = "2025-02-06 02:36:00 +0000";
                    deleteDevicesOnExpiry = 0;
                    inIosDeviceResetWindow = 0;
                    inRenewalWindow = 0;
                    membershipId = 84RX3Z6PA2;
                    membershipProductId = ad19;
                    name = "Apple Developer Program";
                    platform = ios;
                    status = active;
                },
                                {
                    dateExpire = "2026-02-06 23:59:00 +0000";
                    dateStart = "2025-02-06 02:36:00 +0000";
                    deleteDevicesOnExpiry = 0;
                    inIosDeviceResetWindow = 0;
                    inRenewalWindow = 0;
                    membershipId = 84RX3Z6PA2;
                    membershipProductId = ad19;
                    name = "Apple Developer Program";
                    platform = mac;
                    status = active;
                },
                                {
                    dateExpire = "2026-02-06 23:59:00 +0000";
                    dateStart = "2025-02-06 02:36:00 +0000";
                    deleteDevicesOnExpiry = 0;
                    inIosDeviceResetWindow = 0;
                    inRenewalWindow = 0;
                    membershipId = 84RX3Z6PA2;
                    membershipProductId = ad19;
                    name = "Apple Developer Program";
                    platform = safari;
                    status = active;
                }
            );
            name = "Saud Khan";
            status = active;
            teamAgent =             {
                developerStatus = active;
                email = "<EMAIL>";
                firstName = Saud;
                lastName = Khan;
                personId = 1287536112;
                teamMemberId = HA5WX37D9V;
            };
            teamId = 9J6MH85TLG;
            teamProvisioningSettings =             {
                canDeveloperRoleAddAppIds = 1;
                canDeveloperRoleRegisterDevices = 1;
                canDeveloperRoleUpdateAppIds = 1;
            };
            type = Individual;
            xcodeFreeOnly = 0;
        }
    );
    userLocale = "en_US";
}

2025-02-19 07:26:06 +0000  invoking codesign: <NSConcreteTask: 0x60000bae5a90; launchPath='/usr/bin/codesign', arguments='(
    "-f",
    "-s",
    "-",
    "--signature-size",
    16000,
    "--entitlements",
    "/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/entitlements~~~1cOVe9",
    "--requirements",
    "=designated => anchor apple generic and identifier \"dev.hermesengine.iphoneos\" and certificate leaf[subject.CN] = \"Apple Distribution: Saud Khan (9J6MH85TLG)\" and certificate 1[field.1.2.840.113635.*********] /* exists */",
    "--preserve-metadata=identifier,flags,runtime",
    "--omit-adhoc-flag",
    "--team-identifier",
    9J6MH85TLG,
    "--generate-entitlement-der",
    "--strip-disallowed-xattrs",
    "-vvv",
    "/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app/Frameworks/hermes.framework"
)'>
2025-02-19 07:26:06 +0000  codesign output: /var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app/Frameworks/hermes.framework: replacing existing signature
/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app/Frameworks/hermes.framework: signed bundle with Mach-O thin (arm64) [dev.hermesengine.iphoneos]
2025-02-19 07:26:06 +0000  Remote code signing '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app/Frameworks/hermes.framework' with signature slices '[<DVTCodeSignatureSlice: 0x60000cdf4d20; architecture='arm64', cmsDigest='<DVTCodeSignatureHash: 0x600035f244a0; digestAlgorithmDescription='sha256', cdHashHexEncoded='1053bbae22ba070c71960e47b636e5a282b33a3f21de72589be1aa40f637bb31'>', codeSignatureHashes='{(
    <DVTCodeSignatureHash: 0x600035f247c0; digestAlgorithmDescription='sha256', cdHashHexEncoded='1053bbae22ba070c71960e47b636e5a282b33a3f21de72589be1aa40f637bb31'>
)}', cmsHexEncoded=''>]' and configuration 'RemoteSigningToolConfiguration(path: <DVTFilePath:0x60000f6b6380:'/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app/Frameworks/hermes.framework'>, signingCertificate: <DVTSigningCertificate: 0x600002307540; name='Apple Distribution: Saud Khan (9J6MH85TLG)', hash='3C88D4F9708EDA318C7F0585B85368710476B637', serialNumber='70EF3724C03181AB38394771C85CB1E5', certificateKinds='(
    "1.2.840.113635.*********",
    "1.2.840.113635.*********"
), issueDate='2025-02-19 07:07:52 +0000''>, session: <DVTServicesAccountBasedSession: 0x60001bcf2fa0; account='<DVTAppleIDBasedDeveloperAccount: 0x6000000f6700; username='<EMAIL>'>'>, team: <DVTPortalTeam: 0x6000002852c0; name='Saud Khan', teamID='9J6MH85TLG', type='Individual', memberships='(
    "<DVTPortalMembership: 0x600000dc45d0; programs='{(\n    <DVTPortalProgram: 0x600003fe4460; <DVTPortalProgram: 0x600003fe4460; identifier='Xcode.Portal.Program.iOS'>>\n)}'>",
    "<DVTPortalMembership: 0x600000dc6c40; programs='{(\n    <DVTPortalProgram: 0x600003fe4140; <DVTPortalProgram: 0x600003fe4140; identifier='Xcode.Portal.Program.Mac'>>\n)}'>"
)'>, codeResourcesDictionary: [AnyHashable("rules"): {
    "^.*" = 1;
    "^.*\\.lproj/" =     {
        optional = 1;
        weight = 1000;
    };
    "^.*\\.lproj/locversion.plist$" =     {
        omit = 1;
        weight = 1100;
    };
    "^Base\\.lproj/" =     {
        weight = 1010;
    };
    "^version.plist$" = 1;
}, AnyHashable("files"): {
    "Info.plist" = {length = 20, bytes = 0x6ffafde93f3441bd52412b5d33ae177c1f94a56f};
}, AnyHashable("files2"): {
}, AnyHashable("rules2"): {
    ".*\\.dSYM($|/)" =     {
        weight = 11;
    };
    "^(.*/)?\\.DS_Store$" =     {
        omit = 1;
        weight = 2000;
    };
    "^.*" = 1;
    "^.*\\.lproj/" =     {
        optional = 1;
        weight = 1000;
    };
    "^.*\\.lproj/locversion.plist$" =     {
        omit = 1;
        weight = 1100;
    };
    "^Base\\.lproj/" =     {
        weight = 1010;
    };
    "^Info\\.plist$" =     {
        omit = 1;
        weight = 20;
    };
    "^PkgInfo$" =     {
        omit = 1;
        weight = 20;
    };
    "^embedded\\.provisionprofile$" =     {
        weight = 20;
    };
    "^version\\.plist$" =     {
        weight = 20;
    };
}], enableSignatureValidation: true)'
2025-02-19 07:26:06 +0000  Sending request 39F68486-3C76-4370-B2F3-968412FB46D6 to <https://developerservices2.apple.com/services/v1/certificates> for session <DVTServicesAccountBasedSession: 0x60001bcf2fa0; account='<DVTAppleIDBasedDeveloperAccount: 0x6000000f6700; username='<EMAIL>'>'>.
Method: POST

Headers:
{
    Accept = "application/vnd.api+json";
    "Accept-Encoding" = "gzip, deflate";
    "Content-Length" = 104;
    "Content-Type" = "application/vnd.api+json";
    DSESSIONID = 1dsg6j0mgdv1s5mikaplho9pho9piqbh4enhdjelngvlei0umca2;
    "User-Agent" = Xcode;
    "X-Apple-App-Info" = "com.apple.gs.xcode.auth";
    "X-Apple-GS-Token" = "AAAABLwIAAAAAGemDT8RDWdzLnhjb2RlLmF1dGi9AL5XCym1Gek/WVGQEkN1O0rWYh5MOo59ovBY5UI/rXv5C0DwtFGJDQX81kZsvzjBWeHJ0ZltQsAkd4Ud1uBtB1rwwr3ksEEaHKx94XhKBTiHy4/wM65oFqaWdwgKanGhpa5oAfmd2FaKrUi0rn3+4KzFY9ef";
    "X-Apple-I-Identity-Id" = "000568-10-f65e5f67-0de0-426d-a18b-72683542c140";
    "X-HTTP-Method-Override" = GET;
    "X-MMe-Client-Info" = "<Mac15,7> <macOS;14.1;23B2073> <com.apple.AuthKit/1 (com.apple.dt.Xcode/22618)>";
    "X-Mme-Device-Id" = "C455628C-F5FE-541C-B93B-2258A26AC0B5";
    "X-Xcode-Version" = "15.3 (15E204a)";
}

Payload:
{"urlEncodedQueryParams":"teamId=9J6MH85TLG&filter%5BcertificateType%5D=DISTRIBUTION_MANAGED&limit=200"}
2025-02-19 07:26:07 +0000  Received response for 39F68486-3C76-4370-B2F3-968412FB46D6 @ <https://developerservices2.apple.com/services/v1/certificates>. Code = 0
2025-02-19 07:26:07 +0000  Response payload: {
  "data" : [ {
    "type" : "certificates",
    "id" : "W25BG77468",
    "attributes" : {
      "requesterEmail" : "<EMAIL>",
      "serialNumber" : "70EF3724C03181AB38394771C85CB1E5",
      "certificateContent" : "MIIF0zCCBLugAwIBAgIQcO83JMAxgas4OUdxyFyx5TANBgkqhkiG9w0BAQsFADB1MUQwQgYDVQQDDDtBcHBsZSBXb3JsZHdpZGUgRGV2ZWxvcGVyIFJlbGF0aW9ucyBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTELMAkGA1UECwwCRzMxEzARBgNVBAoMCkFwcGxlIEluYy4xCzAJBgNVBAYTAlVTMB4XDTI1MDIxOTA3MDc1MloXDTI2MDIxOTA3MDc1MVowgYcxGjAYBgoJkiaJk/IsZAEBDAo5SjZNSDg1VExHMTMwMQYDVQQDDCpBcHBsZSBEaXN0cmlidXRpb246IFNhdWQgS2hhbiAoOUo2TUg4NVRMRykxEzARBgNVBAsMCjlKNk1IODVUTEcxEjAQBgNVBAoMCVNhdWQgS2hhbjELMAkGA1UEBhMCUEswggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCxGLDgp3FuSqZ8WYp6irnl+XAwoXoyTYv08IMnjHNkA/Nu6FyKZYefBZU9fuH1rnPRU7GPCfwHMOIls0c5YAgYW5BW3eAaMo02u7YROvANSTtCoaogNnxzdGtRNc8ACGrBfNkFN/ZbhAJefnpxxA6aictjMugd0O1vfDCFrwAjl8WJpI/cOL71Ve1kGuelNnwIvYox3LqN5H6B8CRHdxBzrCQGP+41C4KxX1pbpIcFlaoed5lifO3TydPJOF20hkc3fXerUNeeLILbGIZurP9tfzlSiLwHMHjeR8e377Stfb+sRIcOJPUyBpIoWb1W/uXSi1r/9LBPVG/W1onb6hQ9AgMBAAGjggJKMIICRjAMBgNVHRMBAf8EAjAAMB8GA1UdIwQYMBaAFAn+wBWQ+a9kCpISuSYoYwyX7KeyMHAGCCsGAQUFBwEBBGQwYjAtBggrBgEFBQcwAoYhaHR0cDovL2NlcnRzLmFwcGxlLmNvbS93d2RyZzMuZGVyMDEGCCsGAQUFBzABhiVodHRwOi8vb2NzcC5hcHBsZS5jb20vb2NzcDAzLXd3ZHJnMzExMIIBHgYDVR0gBIIBFTCCAREwggENBgkqhkiG92NkBQEwgf8wgcMGCCsGAQUFBwICMIG2DIGzUmVsaWFuY2Ugb24gdGhpcyBjZXJ0aWZpY2F0ZSBieSBhbnkgcGFydHkgYXNzdW1lcyBhY2NlcHRhbmNlIG9mIHRoZSB0aGVuIGFwcGxpY2FibGUgc3RhbmRhcmQgdGVybXMgYW5kIGNvbmRpdGlvbnMgb2YgdXNlLCBjZXJ0aWZpY2F0ZSBwb2xpY3kgYW5kIGNlcnRpZmljYXRpb24gcHJhY3RpY2Ugc3RhdGVtZW50cy4wNwYIKwYBBQUHAgEWK2h0dHBzOi8vd3d3LmFwcGxlLmNvbS9jZXJ0aWZpY2F0ZWF1dGhvcml0eS8wFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwMwHQYDVR0OBBYEFPjBhep0hjPJESKh9eAzq7yh3p56MA4GA1UdDwEB/wQEAwIHgDATBgoqhkiG92NkBgEHAQH/BAIFADAQBgoqhkiG92NkBgEgBAIFADATBgoqhkiG92NkBgEEAQH/BAIFADANBgkqhkiG9w0BAQsFAAOCAQEAZSmrTo3WaO25Sfc3uMKfnbBfplvEhGNiw2q0Te4waZb5btxvAv3kW7RsQjwuE6hHZhQR3bsD7h3HBWjrGhmZvpg65raZDXkHi0Wz7yylTxUcpNxs58VuGvvf+xN9sQsLk5K5rR3Nyq7je6P8CqFt729n/2M4odg4izzeQE0At3m5urb2UH0jC35eVHs9MImwJ1+tkQzwzUG7D77MpKVjIfGyHWCqDYfirZVGTS5b/jRQzJHA/l3hkfjl5LqurLI5mOK0b2WfLE3rF67D7s7BBQf91QC2v8L/zss2VDfWYaDuhXSjIkB6vRo5AC26NAFbZXutLJMR+/bf4iBh5uxG1Q==",
      "displayName" : "Saud Khan",
      "requesterLastName" : "Khan",
      "csrContent" : null,
      "machineName" : null,
      "platform" : null,
      "requesterFirstName" : "Saud",
      "machineId" : null,
      "name" : "Apple Distribution: Saud Khan",
      "responseId" : "258bd1b8-b4e9-48bd-ad32-50036403755a",
      "expirationDate" : "2026-02-19T07:07:51.000+00:00",
      "certificateType" : "DISTRIBUTION_MANAGED"
    },
    "links" : {
      "self" : "https://developerservices2.apple.com:443/services/v1/certificates/W25BG77468"
    }
  } ],
  "links" : {
    "self" : "https://developerservices2.apple.com:443/services/v1/certificates?filter%5BcertificateType%5D=DISTRIBUTION_MANAGED&limit=200"
  },
  "meta" : {
    "paging" : {
      "total" : 1,
      "limit" : 200
    }
  }
}

2025-02-19 07:26:07 +0000  Sending request 69FB5E4D-E922-4C95-B0FF-E9713EE127C4 to <https://developerservices3.apple.com/services/v1/batch> for session <DVTServicesAccountBasedSession: 0x60001bcf2fa0; account='<DVTAppleIDBasedDeveloperAccount: 0x6000000f6700; username='<EMAIL>'>'>.
Method: POST

Headers:
{
    Accept = "application/vnd.api+json";
    "Accept-Encoding" = "gzip, deflate";
    "Content-Length" = 429;
    "Content-Type" = "application/vnd.api+json";
    DSESSIONID = 1dsg6j0mgdv1s5mikaplho9pho9piqbh4enhdjelngvlei0umca2;
    "User-Agent" = Xcode;
    "X-Apple-App-Info" = "com.apple.gs.xcode.auth";
    "X-Apple-GS-Token" = "AAAABLwIAAAAAGemDT8RDWdzLnhjb2RlLmF1dGi9AL5XCym1Gek/WVGQEkN1O0rWYh5MOo59ovBY5UI/rXv5C0DwtFGJDQX81kZsvzjBWeHJ0ZltQsAkd4Ud1uBtB1rwwr3ksEEaHKx94XhKBTiHy4/wM65oFqaWdwgKanGhpa5oAfmd2FaKrUi0rn3+4KzFY9ef";
    "X-Apple-I-Identity-Id" = "000568-10-f65e5f67-0de0-426d-a18b-72683542c140";
    "X-MMe-Client-Info" = "<Mac15,7> <macOS;14.1;23B2073> <com.apple.AuthKit/1 (com.apple.dt.Xcode/22618)>";
    "X-Mme-Device-Id" = "C455628C-F5FE-541C-B93B-2258A26AC0B5";
    "X-Xcode-Version" = "15.3 (15E204a)";
}

Payload:
{"data":{"type":"batch","attributes":{"teamId":"9J6MH85TLG","resourceVersion":"1","resourceList":[{"data":{"type":"codeSignatures","relationships":{"certificate":{"data":{"id":"W25BG77468","type":"certificates"}}},"attributes":{"digestHash":"1053BBAE22BA070C71960E47B636E5A282B33A3F21DE72589BE1AA40F637BB31","teamId":"9J6MH85TLG","cdHashSha256":"1053BBAE22BA070C71960E47B636E5A282B33A3F21DE72589BE1AA40F637BB31","name":"0"}}}]}}}
2025-02-19 07:26:08 +0000  Received response for 69FB5E4D-E922-4C95-B0FF-E9713EE127C4 @ <https://developerservices3.apple.com/services/v1/batch>. Code = 0
2025-02-19 07:26:08 +0000  Response payload: {
  "data" : {
    "type" : "batch",
    "id" : "4b034e00-cf9a-435f-bf85-a4acfed6c35f",
    "attributes" : {
      "responseId" : "4b034e00-cf9a-435f-bf85-a4acfed6c35f",
      "resourceList" : [ {
        "type" : "codeSignatures",
        "id" : "313032db-0fd3-412b-b3e8-e8adf63c8e0e",
        "attributes" : {
          "digestHash" : "1053BBAE22BA070C71960E47B636E5A282B33A3F21DE72589BE1AA40F637BB31",
          "cdHashSha256" : "1053BBAE22BA070C71960E47B636E5A282B33A3F21DE72589BE1AA40F637BB31",
          "signature" : "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",
          "name" : "0",
          "responseId" : "4b034e00-cf9a-435f-bf85-a4acfed6c35f",
          "cmsSignature" : "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",
          "timestamp" : false
        },
        "relationships" : {
          "bundleId" : {
            "data" : null,
            "links" : {
              "self" : {
                "href" : "https://developerservices3.apple.com:443/services/v1/codeSignatures/313032db-0fd3-412b-b3e8-e8adf63c8e0e/relationships/bundleId"
              },
              "related" : {
                "href" : "https://developerservices3.apple.com:443/services/v1/codeSignatures/313032db-0fd3-412b-b3e8-e8adf63c8e0e/bundleId"
              }
            }
          },
          "certificate" : {
            "data" : {
              "type" : "certificates",
              "id" : "W25BG77468"
            },
            "links" : {
              "self" : {
                "href" : "https://developerservices3.apple.com:443/services/v1/codeSignatures/313032db-0fd3-412b-b3e8-e8adf63c8e0e/relationships/certificate"
              },
              "related" : {
                "href" : "https://developerservices3.apple.com:443/services/v1/codeSignatures/313032db-0fd3-412b-b3e8-e8adf63c8e0e/certificate"
              }
            }
          }
        },
        "links" : {
          "self" : {
            "href" : "https://developerservices3.apple.com:443/services/v1/codeSignatures/313032db-0fd3-412b-b3e8-e8adf63c8e0e"
          }
        }
      } ],
      "resourceVersion" : "1"
    },
    "links" : {
      "self" : "https://developerservices3.apple.com:443/services/v1/batch/4b034e00-cf9a-435f-bf85-a4acfed6c35f"
    }
  },
  "links" : {
    "self" : "https://developerservices3.apple.com:443/services/v1/batch"
  }
}

2025-02-19 07:26:08 +0000  Remote signature for /var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app/Frameworks/hermes.framework (arch: arm64) is 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
2025-02-19 07:26:08 +0000  invoking codesign: <NSConcreteTask: 0x60000bae6fd0; launchPath='/usr/bin/codesign', arguments='(
    "-e",
    "--edit-cms",
    "/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/cms~~~3Tj3sb",
    "/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app/Frameworks/hermes.framework"
)'>
2025-02-19 07:26:08 +0000  codesign output: 
2025-02-19 07:26:09 +0000  Entitlements for <IDEDistributionItem: 0x60001bcb5e00; bundleID='com.techcreator.taxirapport.app', path='<DVTFilePath:0x600013728380:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60002fa02fd0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x60000a3670c0; name='Apple Development: Saud Khan (HA5WX37D9V)', hash='D6868630DA90DFCE6FF8EE13FA3FD369F33EBA81', serialNumber='5047EE2BA68B369F6D8FBEEE275C6C59', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-02-07 13:31:34 +0000''>', entitlements='{
    "application-identifier" = "9J6MH85TLG.com.techcreator.taxirapport.app";
    "com.apple.developer.team-identifier" = 9J6MH85TLG;
    "get-task-allow" = 1;
}', teamID='9J6MH85TLG', identifier='com.techcreator.taxirapport.app', executablePath='<DVTFilePath:0x60000f6d6080:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/TaxiRapport'>', hardenedRuntime='0'>'>: {
    "application-identifier" = "9J6MH85TLG.com.techcreator.taxirapport.app";
    "beta-reports-active" = 1;
    "com.apple.developer.team-identifier" = 9J6MH85TLG;
    "get-task-allow" = 0;
}
2025-02-19 07:26:09 +0000  Associated App Clip Identifiers Filter: Skipping because "com.apple.developer.associated-appclip-app-identifiers" is not present
2025-02-19 07:26:09 +0000  Entitlements for <IDEDistributionItem: 0x60001bcb5e00; bundleID='com.techcreator.taxirapport.app', path='<DVTFilePath:0x600013728380:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60002fa02fd0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x60000a366640; name='Apple Development: Saud Khan (HA5WX37D9V)', hash='D6868630DA90DFCE6FF8EE13FA3FD369F33EBA81', serialNumber='5047EE2BA68B369F6D8FBEEE275C6C59', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-02-07 13:31:34 +0000''>', entitlements='{
    "application-identifier" = "9J6MH85TLG.com.techcreator.taxirapport.app";
    "com.apple.developer.team-identifier" = 9J6MH85TLG;
    "get-task-allow" = 1;
}', teamID='9J6MH85TLG', identifier='com.techcreator.taxirapport.app', executablePath='<DVTFilePath:0x60000f6d6080:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/TaxiRapport'>', hardenedRuntime='0'>'> are: {
    "application-identifier" = "9J6MH85TLG.com.techcreator.taxirapport.app";
    "beta-reports-active" = 1;
    "com.apple.developer.team-identifier" = 9J6MH85TLG;
    "get-task-allow" = 0;
}
2025-02-19 07:26:09 +0000  Writing entitlements for <IDEDistributionItem: 0x60001bcb5e00; bundleID='com.techcreator.taxirapport.app', path='<DVTFilePath:0x600013728380:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60002fa02fd0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x6000023d5c80; name='Apple Development: Saud Khan (HA5WX37D9V)', hash='D6868630DA90DFCE6FF8EE13FA3FD369F33EBA81', serialNumber='5047EE2BA68B369F6D8FBEEE275C6C59', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-02-07 13:31:34 +0000''>', entitlements='{
    "application-identifier" = "9J6MH85TLG.com.techcreator.taxirapport.app";
    "com.apple.developer.team-identifier" = 9J6MH85TLG;
    "get-task-allow" = 1;
}', teamID='9J6MH85TLG', identifier='com.techcreator.taxirapport.app', executablePath='<DVTFilePath:0x60000f6d6080:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-19/TaxiRapport 19-02-2025, 12.16 PM.xcarchive/Products/Applications/TaxiRapport.app/TaxiRapport'>', hardenedRuntime='0'>'> to: /var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/entitlements~~~QAVpTt
2025-02-19 07:26:09 +0000  Sending request B7C81A7C-8C1C-4079-95DD-11EF62261384 to <https://developerservices2.apple.com/services/QH65B2/listTeams.action?clientId=XABBG36SBA> for session <DVTServicesAccountBasedSession: 0x60001b317180; account='<DVTAppleIDBasedDeveloperAccount: 0x6000000f6700; username='<EMAIL>'>'>.
Method: POST

Headers:
{
    Accept = "text/x-xml-plist";
    "Accept-Encoding" = "gzip, deflate";
    "Content-Length" = 368;
    "Content-Type" = "text/x-xml-plist";
    "User-Agent" = Xcode;
    "X-Apple-App-Info" = "com.apple.gs.xcode.auth";
    "X-Apple-GS-Token" = "AAAABLwIAAAAAGemDT8RDWdzLnhjb2RlLmF1dGi9AL5XCym1Gek/WVGQEkN1O0rWYh5MOo59ovBY5UI/rXv5C0DwtFGJDQX81kZsvzjBWeHJ0ZltQsAkd4Ud1uBtB1rwwr3ksEEaHKx94XhKBTiHy4/wM65oFqaWdwgKanGhpa5oAfmd2FaKrUi0rn3+4KzFY9ef";
    "X-Apple-I-Identity-Id" = "000568-10-f65e5f67-0de0-426d-a18b-72683542c140";
    "X-MMe-Client-Info" = "<Mac15,7> <macOS;14.1;23B2073> <com.apple.AuthKit/1 (com.apple.dt.Xcode/22618)>";
    "X-Mme-Device-Id" = "C455628C-F5FE-541C-B93B-2258A26AC0B5";
    "X-Xcode-Version" = "15.3 (15E204a)";
}

Payload:
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>clientId</key>
	<string>XABBG36SBA</string>
	<key>protocolVersion</key>
	<string>QH65B2</string>
	<key>requestId</key>
	<string>B7C81A7C-8C1C-4079-95DD-11EF62261384</string>
</dict>
</plist>
2025-02-19 07:26:09 +0000  Received response for B7C81A7C-8C1C-4079-95DD-11EF62261384 @ <https://developerservices2.apple.com/services/QH65B2/listTeams.action?clientId=XABBG36SBA>. Code = 0
2025-02-19 07:26:09 +0000  Response payload: {
    creationTimestamp = "2025-02-19T07:26:09Z";
    protocolVersion = QH65B2;
    requestId = "B7C81A7C-8C1C-4079-95DD-11EF62261384";
    requestUrl = "https://developerservices2.apple.com:443/services/QH65B2/listTeams.action?clientId=XABBG36SBA";
    responseId = "3c3b4e9e-75b0-4652-aec2-496b1a2fe450";
    resultCode = 0;
    teams =     (
                {
            currentTeamMember =             {
                developerStatus = active;
                email = "<EMAIL>";
                firstName = Saud;
                lastName = Khan;
                personId = 1287536112;
                privileges =                 {
                };
                roles =                 (
                    "XCODE_FREE_USER",
                    AGENT,
                    "TEAM_ADMIN",
                    "MAC_PROGRAM_TEAM_ADMIN"
                );
                teamMemberId = HA5WX37D9V;
            };
            dateCreated = "2025-02-06 02:36:35 +0000";
            extendedTeamAttributes =             {
            };
            memberships =             (
                                {
                    dateStart = "2025-02-07 13:40:22 +0000";
                    deleteDevicesOnExpiry = 0;
                    inIosDeviceResetWindow = 0;
                    inRenewalWindow = 0;
                    membershipId = 8XNH6T8A57;
                    membershipProductId = fp22;
                    name = "Xcode Free Provisioning Program";
                    platform = ios;
                    status = activeNeverExpire;
                },
                                {
                    dateExpire = "2026-02-06 23:59:00 +0000";
                    dateStart = "2025-02-06 02:36:00 +0000";
                    deleteDevicesOnExpiry = 0;
                    inIosDeviceResetWindow = 0;
                    inRenewalWindow = 0;
                    membershipId = 84RX3Z6PA2;
                    membershipProductId = ad19;
                    name = "Apple Developer Program";
                    platform = ios;
                    status = active;
                },
                                {
                    dateExpire = "2026-02-06 23:59:00 +0000";
                    dateStart = "2025-02-06 02:36:00 +0000";
                    deleteDevicesOnExpiry = 0;
                    inIosDeviceResetWindow = 0;
                    inRenewalWindow = 0;
                    membershipId = 84RX3Z6PA2;
                    membershipProductId = ad19;
                    name = "Apple Developer Program";
                    platform = mac;
                    status = active;
                },
                                {
                    dateExpire = "2026-02-06 23:59:00 +0000";
                    dateStart = "2025-02-06 02:36:00 +0000";
                    deleteDevicesOnExpiry = 0;
                    inIosDeviceResetWindow = 0;
                    inRenewalWindow = 0;
                    membershipId = 84RX3Z6PA2;
                    membershipProductId = ad19;
                    name = "Apple Developer Program";
                    platform = safari;
                    status = active;
                }
            );
            name = "Saud Khan";
            status = active;
            teamAgent =             {
                developerStatus = active;
                email = "<EMAIL>";
                firstName = Saud;
                lastName = Khan;
                personId = 1287536112;
                teamMemberId = HA5WX37D9V;
            };
            teamId = 9J6MH85TLG;
            teamProvisioningSettings =             {
                canDeveloperRoleAddAppIds = 1;
                canDeveloperRoleRegisterDevices = 1;
                canDeveloperRoleUpdateAppIds = 1;
            };
            type = Individual;
            xcodeFreeOnly = 0;
        }
    );
    userLocale = "en_US";
}

2025-02-19 07:26:09 +0000  invoking codesign: <NSConcreteTask: 0x60000bae25d0; launchPath='/usr/bin/codesign', arguments='(
    "-f",
    "-s",
    "-",
    "--signature-size",
    16000,
    "--entitlements",
    "/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/entitlements~~~QAVpTt",
    "--requirements",
    "=designated => anchor apple generic and identifier \"com.techcreator.taxirapport.app\" and certificate leaf[subject.CN] = \"Apple Distribution: Saud Khan (9J6MH85TLG)\" and certificate 1[field.1.2.840.113635.*********] /* exists */",
    "--preserve-metadata=identifier,flags,runtime",
    "--omit-adhoc-flag",
    "--team-identifier",
    9J6MH85TLG,
    "--generate-entitlement-der",
    "--strip-disallowed-xattrs",
    "-vvv",
    "/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app"
)'>
2025-02-19 07:26:09 +0000  codesign output: /var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app: replacing existing signature
/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app: signed app bundle with Mach-O thin (arm64) [com.techcreator.taxirapport.app]
2025-02-19 07:26:09 +0000  Remote code signing '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app' with signature slices '[<DVTCodeSignatureSlice: 0x60000ce90c90; architecture='arm64', cmsDigest='<DVTCodeSignatureHash: 0x6000358e9f40; digestAlgorithmDescription='sha256', cdHashHexEncoded='7cc36eebc1b9914098c67dfdfcc4fc69b1ea25b211f8189ef95363020b59a651'>', codeSignatureHashes='{(
    <DVTCodeSignatureHash: 0x6000358ea280; digestAlgorithmDescription='sha256', cdHashHexEncoded='7cc36eebc1b9914098c67dfdfcc4fc69b1ea25b211f8189ef95363020b59a651'>
)}', cmsHexEncoded=''>]' and configuration 'RemoteSigningToolConfiguration(path: <DVTFilePath:0x60000340d180:'/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app'>, signingCertificate: <DVTSigningCertificate: 0x600002305140; name='Apple Distribution: Saud Khan (9J6MH85TLG)', hash='3C88D4F9708EDA318C7F0585B85368710476B637', serialNumber='70EF3724C03181AB38394771C85CB1E5', certificateKinds='(
    "1.2.840.113635.*********",
    "1.2.840.113635.*********"
), issueDate='2025-02-19 07:07:52 +0000''>, session: <DVTServicesAccountBasedSession: 0x60001b317180; account='<DVTAppleIDBasedDeveloperAccount: 0x6000000f6700; username='<EMAIL>'>'>, team: <DVTPortalTeam: 0x600000286ec0; name='Saud Khan', teamID='9J6MH85TLG', type='Individual', memberships='(
    "<DVTPortalMembership: 0x600000dc7270; programs='{(\n    <DVTPortalProgram: 0x600003fe4460; <DVTPortalProgram: 0x600003fe4460; identifier='Xcode.Portal.Program.iOS'>>\n)}'>",
    "<DVTPortalMembership: 0x600000dc7ae0; programs='{(\n    <DVTPortalProgram: 0x600003fe4140; <DVTPortalProgram: 0x600003fe4140; identifier='Xcode.Portal.Program.Mac'>>\n)}'>"
)'>, codeResourcesDictionary: [AnyHashable("rules2"): {
    ".*\\.dSYM($|/)" =     {
        weight = 11;
    };
    "^(.*/)?\\.DS_Store$" =     {
        omit = 1;
        weight = 2000;
    };
    "^.*" = 1;
    "^.*\\.lproj/" =     {
        optional = 1;
        weight = 1000;
    };
    "^.*\\.lproj/locversion.plist$" =     {
        omit = 1;
        weight = 1100;
    };
    "^Base\\.lproj/" =     {
        weight = 1010;
    };
    "^Info\\.plist$" =     {
        omit = 1;
        weight = 20;
    };
    "^PkgInfo$" =     {
        omit = 1;
        weight = 20;
    };
    "^embedded\\.provisionprofile$" =     {
        weight = 20;
    };
    "^version\\.plist$" =     {
        weight = 20;
    };
}, AnyHashable("files"): {
    "AntDesign.ttf" = {length = 20, bytes = 0x4e77868439280fb434d4697c7b911271406c81f3};
    "Assets.car" = {length = 20, bytes = 0xf6389794608b2b4c4be46cfeb7f6e82f36bf06f7};
    "Entypo.ttf" = {length = 20, bytes = 0x12b5670eb178138f77285d5f2c246d3cc5fa67d6};
    "EvilIcons.ttf" = {length = 20, bytes = 0x91d377ea3cf47490b256c2ed081704a7dabdae0c};
    "Feather.ttf" = {length = 20, bytes = 0xe6604258b1ced5efd51360875d782fca65381d47};
    "FontAwesome.ttf" = {length = 20, bytes = 0x13b1eab65a983c7a73bc7997c479d66943f7c6cb};
    "FontAwesome5_Brands.ttf" = {length = 20, bytes = 0x3fa2d67cef22da5c3f3eb5730c6afbd6fecf0372};
    "FontAwesome5_Regular.ttf" = {length = 20, bytes = 0x7d849a3981a716e2ba4a84634bc57d0b8054a6a3};
    "FontAwesome5_Solid.ttf" = {length = 20, bytes = 0xc1b9fae262f42868c075ac865a8ab34920e20a2c};
    "FontAwesome6_Brands.ttf" = {length = 20, bytes = 0xd8b3568e9d8a1d3c01c85520eb9ca0b49b72815d};
    "FontAwesome6_Regular.ttf" = {length = 20, bytes = 0x2d7890e12afb77490112ec57fe47ca0688aebda2};
    "FontAwesome6_Solid.ttf" = {length = 20, bytes = 0xe9ace557c3aa403307f1e7a2cef1c035d522b94b};
    "Fontisto.ttf" = {length = 20, bytes = 0xc090a3ec96a3f1bb9b615c2f3f204ce0dcdcdbc3};
    "Foundation.ttf" = {length = 20, bytes = 0x4b2bce6c792493a4a5716b6fec2dbefe89492c3f};
    "Frameworks/hermes.framework/Info.plist" = {length = 20, bytes = 0x6ffafde93f3441bd52412b5d33ae177c1f94a56f};
    "Frameworks/hermes.framework/_CodeSignature/CodeResources" = {length = 20, bytes = 0xc2dbf40d78ba0508775b5cf391fcea4ced5197d1};
    "Frameworks/hermes.framework/hermes" = {length = 20, bytes = 0xcb0ef79c8a603ae8e29084d9e78d4d427f1c799f};
    "Info.plist" = {length = 20, bytes = 0x034c84d9f7b756f114d4cd3e92766d8d78813686};
    "Ionicons.ttf" = {length = 20, bytes = 0x86e07c3d974eb09099e6e5a9b3b8310303cf0feb};
    "LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib" = {length = 20, bytes = 0xf6a5ce934becce48654d5391abc6ed45eddf4350};
    "LaunchScreen.storyboardc/Info.plist" = {length = 20, bytes = 0x9f6b7c82c0e97c4e979211b7d69ec84094714f15};
    "LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib" = {length = 20, bytes = 0x65580cd7e2b065c667c2181a23417b06dd5b6b67};
    "MaterialCommunityIcons.ttf" = {length = 20, bytes = 0x4c2a838b00dbb5e8bb1b368fce0de534e8eb241c};
    "MaterialIcons.ttf" = {length = 20, bytes = 0x7e02c3f005532ff4d24148567c84089756a7848a};
    "Octicons.ttf" = {length = 20, bytes = 0x56acefc0731f35ee23b195b1c8cb5fa94a0db97b};
    PkgInfo = {length = 20, bytes = 0x9f9eea0cfe2d65f2c3d6b092e375b40782d08f31};
    "PrivacyInfo.xcprivacy" = {length = 20, bytes = 0x4f84d86d15d562e23e77bfad0725ba48c5304cde};
    "RCTI18nStrings.bundle/Info.plist" = {length = 20, bytes = 0x66212ddf8d703dd5309f93f7bdd9c83876002547};
    "RCTI18nStrings.bundle/ar.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0xb2ef7bb91970dca206d9476f3336fd2414160b9d};
        optional = 1;
    };
    "RCTI18nStrings.bundle/cs.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0xc55c4cb30df4ada4b0cf28429a794bbcd227fa32};
        optional = 1;
    };
    "RCTI18nStrings.bundle/da.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0xad7c163de732fc031ba3b4da23e193eb21fc2d38};
        optional = 1;
    };
    "RCTI18nStrings.bundle/de.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x12c84bc104147b4335e0242179bf864dacb7299f};
        optional = 1;
    };
    "RCTI18nStrings.bundle/el.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x146b31420d965170799592144ce3291e1868db6f};
        optional = 1;
    };
    "RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x8a7e5a801c42a7dd86255739a0686cd243e56786};
        optional = 1;
    };
    "RCTI18nStrings.bundle/en.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x95cc1c18ca7307037040de6ff34db6327f98a163};
        optional = 1;
    };
    "RCTI18nStrings.bundle/es.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0xb8220ac256e19358de816c25d607d68a5b926e9a};
        optional = 1;
    };
    "RCTI18nStrings.bundle/fi.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0xb714aaafcfa20f935699e487eaa32632ed7c0f88};
        optional = 1;
    };
    "RCTI18nStrings.bundle/fr.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x3fc12d22a0ddff46f070d5574ee7ade151d12839};
        optional = 1;
    };
    "RCTI18nStrings.bundle/he.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x2951d9e955612ec05c4af1fe844c1e9b05c41b95};
        optional = 1;
    };
    "RCTI18nStrings.bundle/hi.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0xc6cf78d1d99cb957ecd6c5a7a8b5aec5cef2891e};
        optional = 1;
    };
    "RCTI18nStrings.bundle/hr.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x67f68f6e26269190acbbca822e9b78d182442672};
        optional = 1;
    };
    "RCTI18nStrings.bundle/hu.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0xd55244cbd693e771efe74bf88fc802fab5104253};
        optional = 1;
    };
    "RCTI18nStrings.bundle/id.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x27604fefb09d662b5add371ec3fdf3d65fc8c163};
        optional = 1;
    };
    "RCTI18nStrings.bundle/it.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x19ce92f09bda826ba463791680ce1abf81018e94};
        optional = 1;
    };
    "RCTI18nStrings.bundle/ja.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x4ea31f5ac120d0c174a5bb005f56b245e95a664c};
        optional = 1;
    };
    "RCTI18nStrings.bundle/ko.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x08e950479c793adad7e22c52e92d3fbd11268ba8};
        optional = 1;
    };
    "RCTI18nStrings.bundle/ms.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x1491e9998b8f1d8d528f7c06d42d8e1ee53a4170};
        optional = 1;
    };
    "RCTI18nStrings.bundle/nb.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0xa3c127541a8af8ae073796c918882d5c45de87b4};
        optional = 1;
    };
    "RCTI18nStrings.bundle/nl.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x24fe29dcaae55aeba420f6c3e655025b7a697af4};
        optional = 1;
    };
    "RCTI18nStrings.bundle/pl.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0xd39f11cea8844f675b678c4212054676f4b5a4b1};
        optional = 1;
    };
    "RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x5cee87b10c7967fd8018ca3946923ade9457919f};
        optional = 1;
    };
    "RCTI18nStrings.bundle/pt.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x56c969c3d0378ef49f06f5eba31aa4dd5d2fef8b};
        optional = 1;
    };
    "RCTI18nStrings.bundle/ro.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0xba0a4cff739567645e115de38dbda548cf5b7586};
        optional = 1;
    };
    "RCTI18nStrings.bundle/ru.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x6d4c69c0eec60c9eb2ed1d73090588a36db54cc7};
        optional = 1;
    };
    "RCTI18nStrings.bundle/sk.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x98f4bb11c38fe4b721b2caa1b85b1145fa90d05f};
        optional = 1;
    };
    "RCTI18nStrings.bundle/sv.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x826a92799b9a82f3cc3af1adb661cc2a4f864d9f};
        optional = 1;
    };
    "RCTI18nStrings.bundle/th.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0xf37c056908b7f590550af7b05801de7c07b0f506};
        optional = 1;
    };
    "RCTI18nStrings.bundle/tr.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0xc2442db3479dc54f432f7daf4a241a7993931da7};
        optional = 1;
    };
    "RCTI18nStrings.bundle/uk.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x513a0a670a2938719a09d84227bf69e4ffb1c6b8};
        optional = 1;
    };
    "RCTI18nStrings.bundle/vi.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x413e9c1d3e92d9e56f8bac330f3a6292456ad6b5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x2a6e50d0368ae536b90362fea790643ef8d577f8};
        optional = 1;
    };
    "RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x40e54bec3739c7b36300848ea1a31a639182c692};
        optional = 1;
    };
    "RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x1541fe9bb5ae9c3f04e4bb45d79527048d923c3b};
        optional = 1;
    };
    "RCTI18nStrings.bundle/zu.lproj/Localizable.strings" =     {
        hash = {length = 20, bytes = 0xdc1ad33f62bbdbae9de351fe71f98210966e42c5};
        optional = 1;
    };
    "RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin" =     {
        hash = {length = 20, bytes = 0x732ed7075be4b23a4289c1288ad4387816a535c7};
        optional = 1;
    };
    "RNCAsyncStorage_resources.bundle/Info.plist" = {length = 20, bytes = 0xa4634b79e411a1b3b13cc3cc687db79100c541e9};
    "RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy" = {length = 20, bytes = 0xb0f0dfc82c17545c123fc70645646c9296e55a03};
    "SimpleLineIcons.ttf" = {length = 20, bytes = 0x9ffb81a5a11112e292f2cc323e98486bad597599};
    "Zocial.ttf" = {length = 20, bytes = 0x6a48a962b06cc3acbdfd61df0d9a34744eea5e8d};
    "assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png" = {length = 20, bytes = 0xffdc4ed473b2be2806e99d0f680d1d2e9fe00e56};
    "assets/node_modules/@react-navigation/elements/src/assets/back-icon.png" = {length = 20, bytes = 0x91e2ce8b58a725d5ecd084522a75eaca15b9ec0c};
    "assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>" = {length = 20, bytes = 0xad2bc6f26e2e3cc488b4872c205f8ddad0c90fc5};
    "assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>" = {length = 20, bytes = 0xf24146f15bfb1dfaf0e3255c16b85485b1e27810};
    "assets/node_modules/react-native-calendars/src/calendar/img/<EMAIL>" = {length = 20, bytes = 0xed61b07a97383a633a39921a078fe4fdc5095845};
    "assets/node_modules/react-native-calendars/src/calendar/img/<EMAIL>" = {length = 20, bytes = 0xe0503209662fbfb175970c65b57cadb5074124bd};
    "assets/node_modules/react-native-calendars/src/img/down.png" = {length = 20, bytes = 0x26bdfe38892ad9c4c4848b4665ed47a84efc35d5};
    "assets/node_modules/react-native-calendars/src/img/<EMAIL>" = {length = 20, bytes = 0xbe89c56378f2cc1038597e9e9b1acacd63e529f0};
    "assets/node_modules/react-native-calendars/src/img/<EMAIL>" = {length = 20, bytes = 0xa076dffc34f325f144713cc07133fa7e0edb2c1a};
    "assets/node_modules/react-native-calendars/src/img/up.png" = {length = 20, bytes = 0x3ef1a4072fb6d119e19e74be2c65bccea3d5331c};
    "assets/node_modules/react-native-calendars/src/img/<EMAIL>" = {length = 20, bytes = 0xe47c4a9b489f2825aebe8a1e2b6cf1fe31bb66f9};
    "assets/node_modules/react-native-calendars/src/img/<EMAIL>" = {length = 20, bytes = 0x1d62eabc0047dcdbbf98fc99ce28a5a612bdd37c};
    "assets/node_modules/react-native-element-dropdown/src/assets/close.png" = {length = 20, bytes = 0xd8709cf2f46620a79cecbba8de3ad0b0aa54a764};
    "assets/node_modules/react-native-element-dropdown/src/assets/down.png" = {length = 20, bytes = 0x36404cd88b0ca1f9a4b568518f762ade751f89ca};
    "assets/node_modules/react-native-ratings/dist/images/airbnb-star-selected.png" = {length = 20, bytes = 0x8667f752e29f3d44bead057754245294e8a77095};
    "assets/node_modules/react-native-ratings/dist/images/airbnb-star.png" = {length = 20, bytes = 0xebe49914eca97db66b448c9e8aa837ec0d4446de};
    "assets/node_modules/react-native-ratings/dist/images/bell.png" = {length = 20, bytes = 0x9919360fac0e772bac3af9042276f22f72bf13a9};
    "assets/node_modules/react-native-ratings/dist/images/heart.png" = {length = 20, bytes = 0xae1655bea45d4caa14d08aec76a41ecc5774cdbf};
    "assets/node_modules/react-native-ratings/dist/images/rocket.png" = {length = 20, bytes = 0x9aa398283fb8f2223c8b6afafcb1ca5659b4446c};
    "assets/node_modules/react-native-ratings/dist/images/star.png" = {length = 20, bytes = 0x5cf15a0aed63fc779e13bce82b7f2e94feb7e6a7};
    "assets/src/assets/Image/Img2.png" = {length = 20, bytes = 0xd29feb1cc1f226e863d1f9b338a06512d52e93d3};
    "assets/src/assets/Image/Img3.png" = {length = 20, bytes = 0x13a45d1e1afe86348e57048391d013b753025332};
    "assets/src/assets/Image/Img4.png" = {length = 20, bytes = 0x1e9032fe3db59055753d7da97e56f58c1eb9cb06};
    "assets/src/assets/Image/Img5.png" = {length = 20, bytes = 0xe37e8b1d90683699458e9e3c6b9419f2c26fe80a};
    "assets/src/assets/Image/Menu/Harddrive.png" = {length = 20, bytes = 0x0811de9a4f04778de35b70b2910be998376cf02b};
    "assets/src/assets/Image/Menu/Mail.png" = {length = 20, bytes = 0x679d6aef07b7f08db159f8cb75499e62c3caa9b2};
    "assets/src/assets/Image/Menu/Phone.png" = {length = 20, bytes = 0xb7c483afcac1cb1ce944060140d454a1eb2c0235};
    "assets/src/assets/Image/Menu/Upload.png" = {length = 20, bytes = 0x9da5d63f6b68bf30cfcb566095c72a7944d3523d};
    "assets/src/assets/Image/Menu/User.png" = {length = 20, bytes = 0xb9c53c4eb7bfc68708a9748582f7ebb4ef86df07};
    "assets/src/assets/Image/Menu/localtaxi.png" = {length = 20, bytes = 0x1e575c05f3b8d3d7fba7cd3eedebf217474109bb};
    "assets/src/assets/Image/TaxiBanner.png" = {length = 20, bytes = 0x8bf88266c25568d4fc884ac4934af6396b7b8efc};
    "assets/src/assets/Image/bgImg.png" = {length = 20, bytes = 0xed532094d829c391acca1910c822993b322c504a};
    "assets/src/assets/Image/logo.png" = {length = 20, bytes = 0xdb13be58850fe54f8a8904fd6b18aa5fadade2f6};
    "assets/src/assets/Image/slide.png" = {length = 20, bytes = 0x9f52630f03340c990bcdbd734cc461d1d5c6ba09};
    "assets/src/assets/Image/techcreator.png" = {length = 20, bytes = 0x03ed79ebd79f8fb128995d2c0cfd06cd9b8494fe};
    "assets/src/assets/default/defaultPerson.jpeg" = {length = 20, bytes = 0x19a1150839fbe2f3fec3e3b92b690dc1e00c25d6};
    "embedded.mobileprovision" = {length = 20, bytes = 0x3ef00d294df06a567f14453ac6abe2493a6525c6};
    "main.jsbundle" = {length = 20, bytes = 0x9cada2af2812f0868bdb62a40ba2bca79dddca0d};
}, AnyHashable("rules"): {
    "^.*" = 1;
    "^.*\\.lproj/" =     {
        optional = 1;
        weight = 1000;
    };
    "^.*\\.lproj/locversion.plist$" =     {
        omit = 1;
        weight = 1100;
    };
    "^Base\\.lproj/" =     {
        weight = 1010;
    };
    "^version.plist$" = 1;
}, AnyHashable("files2"): {
    "AntDesign.ttf" =     {
        hash2 = {length = 32, bytes = 0x7955ca14 127b3041 2c114eb1 3cfd702b ... 695d73ca a824cde1 };
    };
    "Assets.car" =     {
        hash2 = {length = 32, bytes = 0x4ef77283 c0fc7ba3 8aace993 b8450b6c ... 93c4e0a5 7ecdf5b1 };
    };
    "Entypo.ttf" =     {
        hash2 = {length = 32, bytes = 0x16f92298 587bab2b 469bc13c 0dfc6d5d ... 3ad16bc0 23bf717d };
    };
    "EvilIcons.ttf" =     {
        hash2 = {length = 32, bytes = 0xa5caeb4d 395c5e32 f1d5a30a cb382aeb ... 9907f221 5e4e3e26 };
    };
    "Feather.ttf" =     {
        hash2 = {length = 32, bytes = 0x57599360 fc9b80bc e473b364 f7f255b5 ... 18cb0e0f 838462b4 };
    };
    "FontAwesome.ttf" =     {
        hash2 = {length = 32, bytes = 0xaa58f33f 239a0fb0 2f5c7a6c 45c043d7 ... 94ecd6d4 edc0d6a8 };
    };
    "FontAwesome5_Brands.ttf" =     {
        hash2 = {length = 32, bytes = 0x06f4d009 23ea2469 7df5df0b 92984175 ... 531bb401 e393ec42 };
    };
    "FontAwesome5_Regular.ttf" =     {
        hash2 = {length = 32, bytes = 0xc651b8a6 7d319320 6f622c3c 3b0fbca4 ... 52c1e2a2 e84c9b31 };
    };
    "FontAwesome5_Solid.ttf" =     {
        hash2 = {length = 32, bytes = 0x3d06af1f 31cd83ac e7a265a0 14b8fb5d ... 5555190e 627e03c2 };
    };
    "FontAwesome6_Brands.ttf" =     {
        hash2 = {length = 32, bytes = 0x003f1154 1856a649 a6c8235c 6266c893 ... da24dc55 56d14fbf };
    };
    "FontAwesome6_Regular.ttf" =     {
        hash2 = {length = 32, bytes = 0x7d81a1a7 cc07e1ab 196e4049 6d3f4359 ... 46675ee6 9912950b };
    };
    "FontAwesome6_Solid.ttf" =     {
        hash2 = {length = 32, bytes = 0xcea79b34 5caf49d6 223098b8 a2b04d70 ... cf7f7a26 7793e9aa };
    };
    "Fontisto.ttf" =     {
        hash2 = {length = 32, bytes = 0x94dab9f1 d5b13ea0 769d124a 2da0d024 ... c0446a77 158100c0 };
    };
    "Foundation.ttf" =     {
        hash2 = {length = 32, bytes = 0x7e1dd03d d4ce90b6 58052554 cd7459df ... a4c6d56a 5f8933e6 };
    };
    "Frameworks/hermes.framework/Info.plist" =     {
        hash2 = {length = 32, bytes = 0xf5bad9f8 92f0b9c6 ea45aff6 28cd6f69 ... ad9d17ac 420b64d8 };
    };
    "Frameworks/hermes.framework/_CodeSignature/CodeResources" =     {
        hash2 = {length = 32, bytes = 0x9aeb132e a2b74089 2bfc2cda 2dcd841b ... 49a7d526 a45f5c53 };
    };
    "Frameworks/hermes.framework/hermes" =     {
        hash2 = {length = 32, bytes = 0x675d18da 9564d448 78493304 4e370508 ... 3b0adce2 60ea5e24 };
    };
    "Ionicons.ttf" =     {
        hash2 = {length = 32, bytes = 0x9c7fb907 1a6c2858 2c6b8446 8cbff2c7 ... 48a15993 8d22916b };
    };
    "LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib" =     {
        hash2 = {length = 32, bytes = 0xce2ef6cd ded67c98 b4271da7 8b9a614c ... ef287689 fc6634b6 };
    };
    "LaunchScreen.storyboardc/Info.plist" =     {
        hash2 = {length = 32, bytes = 0x1f255d5c c53b531e 3f29a940 6a8df49a ... bd8374f7 07f7d5c8 };
    };
    "LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib" =     {
        hash2 = {length = 32, bytes = 0x54f3637f 671feba5 f19e82ec 4f4a7fb4 ... 89aa571f c8f053e2 };
    };
    "MaterialCommunityIcons.ttf" =     {
        hash2 = {length = 32, bytes = 0x9e94d4db e2e87ea4 95f0d42d 879146bc ... 154a15ee fa37ef59 };
    };
    "MaterialIcons.ttf" =     {
        hash2 = {length = 32, bytes = 0xef149f08 bdd2ff09 a4e2c857 3476b7b0 ... e59899e7 175bedda };
    };
    "Octicons.ttf" =     {
        hash2 = {length = 32, bytes = 0x16db3480 53ea1125 99e2a635 6f6c1fa0 ... fadffcda 90e2227c };
    };
    "PrivacyInfo.xcprivacy" =     {
        hash2 = {length = 32, bytes = 0xb9289ecf ec677540 a293fba9 48db7268 ... 1e7a85ba 0c31a252 };
    };
    "RCTI18nStrings.bundle/Info.plist" =     {
        hash2 = {length = 32, bytes = 0x4790f2ed d15ff5f1 aaed7c84 61c283f0 ... 214cfcb9 001bb9dd };
    };
    "RCTI18nStrings.bundle/ar.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x868c712f 9eff8cf2 e2416405 c7604698 ... 1491a163 bac7efeb };
        optional = 1;
    };
    "RCTI18nStrings.bundle/cs.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x9f351ecc a06cc24f 047da05f 9590dca5 ... 068521dc c889663d };
        optional = 1;
    };
    "RCTI18nStrings.bundle/da.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0xc29020e9 71e100cf 6e92ca51 610ee29d ... d26aa734 d9e1e87c };
        optional = 1;
    };
    "RCTI18nStrings.bundle/de.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x65b89684 924e1086 b3556a49 8c1228b6 ... 0eb81575 6c7d62d7 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/el.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x2bdc51fb 769d4e8a 47d6da67 dc1bee75 ... 2a39c01d f3ef24b0 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x57c8b478 15aaa2fa 3ef0eccd 363f52ee ... 947b6772 aeaf2348 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/en.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0xd0329cfd ed933adb 086e40b6 609095a0 ... f6980349 25eba0d7 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/es.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x279c53d6 78918ca0 99b1eaf2 43b8ed91 ... bed2896c 3bec18fe };
        optional = 1;
    };
    "RCTI18nStrings.bundle/fi.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0xb455fed7 01abfa2c 60523b11 b06aea5a ... 54c79a15 527e16ec };
        optional = 1;
    };
    "RCTI18nStrings.bundle/fr.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0xeea6e73c ff10128a b5e5015b 3537e58e ... 22eb9d99 ebff378a };
        optional = 1;
    };
    "RCTI18nStrings.bundle/he.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x0b33b058 6d2a7e8e c2fa3733 7cac037e ... de04311e 1bbb39f3 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/hi.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x3552eda5 763f15c6 0d2246c1 28993b91 ... 7b974c33 61f46706 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/hr.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0xb18be146 e3b8aeda 67635a0d e9f666d3 ... 5ed0f8f6 d8dc418d };
        optional = 1;
    };
    "RCTI18nStrings.bundle/hu.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x37b84650 4debc847 615ea8e3 feeac2cb ... 61af6143 52187fae };
        optional = 1;
    };
    "RCTI18nStrings.bundle/id.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x0fa7c1b3 49c7b577 70018317 545f8222 ... ec8220e3 deb9118b };
        optional = 1;
    };
    "RCTI18nStrings.bundle/it.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x5a7a2a98 0cc1b583 c50cf0b4 0cd67c7b ... 142ebc39 e9318c42 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/ja.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0xab7a9825 427bee2e 88962ced f45ce570 ... 26d04207 3c5260f4 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/ko.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0xdd39b06c 35b50d95 6ce64e7d c36525fc ... a75d8912 0aca4cd7 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/ms.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x149f344e 1d1acc26 ecf4c0b6 3a526311 ... 8ae87000 b2192e8f };
        optional = 1;
    };
    "RCTI18nStrings.bundle/nb.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x58045531 f61c6804 ce95a882 51401e9b ... 0f0b77ee 848408db };
        optional = 1;
    };
    "RCTI18nStrings.bundle/nl.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0xc8bda745 bfb33124 15820889 58052db4 ... 2b919766 d442a637 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/pl.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x47329ed0 3e768181 edb30fe6 a6cdeb8d ... d0b5beef 0d817a22 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x10ee8f93 77145fd8 0bbee126 18c97d96 ... 34b28a2d 235280bf };
        optional = 1;
    };
    "RCTI18nStrings.bundle/pt.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0xb9c2fa31 f688ae0f 74b36860 6ee9f16b ... 76f5b33b 45c2b2f5 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/ro.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x4a255b7e 5589beba be29edb9 b9614418 ... f27c3f6c 2ce4d6d1 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/ru.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x6b8eabfd 9b087d0d 203edfc6 612afcda ... 037be5d1 aeaf4600 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/sk.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x4f7160f0 7eaa71e5 88eb7b39 a6497b26 ... 47fe4696 11d76c50 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/sv.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0xd2f5f6ca 01857728 110bcdd8 aeda0fa5 ... 73d1e55f 28eac2c9 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/th.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x726ef226 e11efe4f b134b707 48a09da7 ... e1bddeae 65dab225 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/tr.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x68bf2203 b66b9a1f cff96961 e2e24d39 ... e76cb443 ffd6872f };
        optional = 1;
    };
    "RCTI18nStrings.bundle/uk.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0xd0ec30db 3f854c84 d1e5d8e1 089a8b4f ... 3a71461b de870391 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/vi.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0xc747f8f9 7ef90077 66f4a82b 42f9a0f9 ... 7ad5a073 4f918156 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0xe64a5c44 3e77893f 242fd348 63e32c9e ... 34f388b6 73c8307d };
        optional = 1;
    };
    "RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x5f59a27a 0fa765c6 c7d3410b dd7d628e ... 2f8e6940 efee1854 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x2a0ffa47 39357103 4e59ee1f 74a96670 ... 5e47e397 5bd976d2 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/zu.lproj/Localizable.strings" =     {
        hash2 = {length = 32, bytes = 0xc6e78324 c3a5b0ea 7666901b 03cab311 ... 7dbdaefb 93488261 };
        optional = 1;
    };
    "RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin" =     {
        hash2 = {length = 32, bytes = 0x2b48d9ab 74ee0478 8c9c8f8f 1a9902b8 ... 89ecd3fa d43f4ff5 };
        optional = 1;
    };
    "RNCAsyncStorage_resources.bundle/Info.plist" =     {
        hash2 = {length = 32, bytes = 0xb8bfbb31 e60ddbb2 52a95487 a1d3b51c ... caad9ef2 43802f65 };
    };
    "RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy" =     {
        hash2 = {length = 32, bytes = 0x0b1287d0 e686cf86 7d38d97c ca5dc694 ... dd4bd2fd 5458a2f0 };
    };
    "SimpleLineIcons.ttf" =     {
        hash2 = {length = 32, bytes = 0x3f501ddb 05c70829 bbb51cfe 9ca9fff5 ... 3ccf25df 767f0870 };
    };
    "Zocial.ttf" =     {
        hash2 = {length = 32, bytes = 0xdd8ee403 bbcbf5e5 8b77dca4 ac03ebaf ... 9ca49043 5a86fa48 };
    };
    "assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png" =     {
        hash2 = {length = 32, bytes = 0x2c95e8c2 675e7993 35fd0046 bb07dbda ... 22c6309d 619cc4d3 };
    };
    "assets/node_modules/@react-navigation/elements/src/assets/back-icon.png" =     {
        hash2 = {length = 32, bytes = 0x2cdfeb8e 5ccde797 6f7012fb 8cce73af ... 09fed114 871c6cd8 };
    };
    "assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>" =     {
        hash2 = {length = 32, bytes = 0x2b7443a9 a58e92ca 11a575bd c1159861 ... 0d602606 b302cd98 };
    };
    "assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>" =     {
        hash2 = {length = 32, bytes = 0x3ddd0773 ed27e23d 25789f30 1731b3ac ... 1b838f89 c6d27225 };
    };
    "assets/node_modules/react-native-calendars/src/calendar/img/<EMAIL>" =     {
        hash2 = {length = 32, bytes = 0x0bcfea72 226dddf7 25104e65 7a969ad8 ... fbd6ed1a bd95d4e5 };
    };
    "assets/node_modules/react-native-calendars/src/calendar/img/<EMAIL>" =     {
        hash2 = {length = 32, bytes = 0xd2318197 121d5907 c2428979 d2c68c9c ... 7c4ca451 8eb1732f };
    };
    "assets/node_modules/react-native-calendars/src/img/down.png" =     {
        hash2 = {length = 32, bytes = 0x61b7b566 a6bd8d91 eddc58ed bcb0c2f8 ... 5444a42d 91539633 };
    };
    "assets/node_modules/react-native-calendars/src/img/<EMAIL>" =     {
        hash2 = {length = 32, bytes = 0xcf1bfe7c 1653f3d0 6c2c2b14 ab9fe031 ... 5d3228d6 a58a424c };
    };
    "assets/node_modules/react-native-calendars/src/img/<EMAIL>" =     {
        hash2 = {length = 32, bytes = 0xd0d9df8c 65bb6744 ce32204a fa3abe47 ... 7d3f7758 f330dc23 };
    };
    "assets/node_modules/react-native-calendars/src/img/up.png" =     {
        hash2 = {length = 32, bytes = 0xb77bffd5 c43658d0 17a36a83 06ccd5ec ... 923c7781 7eb623c9 };
    };
    "assets/node_modules/react-native-calendars/src/img/<EMAIL>" =     {
        hash2 = {length = 32, bytes = 0xed1f1c8f c37e3694 f6f965ff 4bbca6c4 ... bcbd71b6 c80f6d25 };
    };
    "assets/node_modules/react-native-calendars/src/img/<EMAIL>" =     {
        hash2 = {length = 32, bytes = 0x43565cc5 0264c73d 6e426d0f d3dc6e69 ... 867f2f8d d235a6aa };
    };
    "assets/node_modules/react-native-element-dropdown/src/assets/close.png" =     {
        hash2 = {length = 32, bytes = 0x739381db 30acdf6c 0909f5fc 826ff6e5 ... 3af20767 6b379767 };
    };
    "assets/node_modules/react-native-element-dropdown/src/assets/down.png" =     {
        hash2 = {length = 32, bytes = 0x6331558e f9218df3 5372f08e dbd20ff3 ... 565d3207 5414a1c5 };
    };
    "assets/node_modules/react-native-ratings/dist/images/airbnb-star-selected.png" =     {
        hash2 = {length = 32, bytes = 0xd2e86a6e 418810cb da7dc589 ab958837 ... fa418f95 d19993e3 };
    };
    "assets/node_modules/react-native-ratings/dist/images/airbnb-star.png" =     {
        hash2 = {length = 32, bytes = 0xa15b007a a47fa315 c13854a0 2cabdeef ... ddfe756f 8003b0c8 };
    };
    "assets/node_modules/react-native-ratings/dist/images/bell.png" =     {
        hash2 = {length = 32, bytes = 0x31574921 3623d6d0 f095ed3a 2bea7c20 ... 831bb30d f73d0188 };
    };
    "assets/node_modules/react-native-ratings/dist/images/heart.png" =     {
        hash2 = {length = 32, bytes = 0xbf6adfcc 3b77fdc0 e224a8dd 84ff1e8e ... 486b70cc e6fc0316 };
    };
    "assets/node_modules/react-native-ratings/dist/images/rocket.png" =     {
        hash2 = {length = 32, bytes = 0x202386b8 13319deb cf2a4a53 07f01ae4 ... 3df888a6 045f7aa9 };
    };
    "assets/node_modules/react-native-ratings/dist/images/star.png" =     {
        hash2 = {length = 32, bytes = 0x42899d37 7ac160d2 51ea1cca f1570a66 ... acd322f8 5c5abb95 };
    };
    "assets/src/assets/Image/Img2.png" =     {
        hash2 = {length = 32, bytes = 0xdc7f17c8 f8f8aa78 9e58344f d4466046 ... f6256ef3 91936b73 };
    };
    "assets/src/assets/Image/Img3.png" =     {
        hash2 = {length = 32, bytes = 0xd2bcf77f 1fe01904 f6594cc9 a0d3ad13 ... f0e318f5 1439b452 };
    };
    "assets/src/assets/Image/Img4.png" =     {
        hash2 = {length = 32, bytes = 0x734ef344 c639d8cb 4d8be7c2 1efc0161 ... 16276ec1 8014bd5b };
    };
    "assets/src/assets/Image/Img5.png" =     {
        hash2 = {length = 32, bytes = 0xea0e39da 25f5c546 1d816fa8 68d35bad ... 8e203b98 d4d741c4 };
    };
    "assets/src/assets/Image/Menu/Harddrive.png" =     {
        hash2 = {length = 32, bytes = 0x181424f7 8f415933 67d705d0 b6f7d730 ... d03b1047 57c3d576 };
    };
    "assets/src/assets/Image/Menu/Mail.png" =     {
        hash2 = {length = 32, bytes = 0xde76f8b2 564b582e 8003dce4 a8bd55e2 ... a947c4a7 d5edc366 };
    };
    "assets/src/assets/Image/Menu/Phone.png" =     {
        hash2 = {length = 32, bytes = 0xb6a6a097 d2c99ac6 e8a6e78d 3375e7ea ... fb9dd441 b9479b53 };
    };
    "assets/src/assets/Image/Menu/Upload.png" =     {
        hash2 = {length = 32, bytes = 0xe8aa0c67 fa77dbfb 04338258 c3b89270 ... bd6fe948 2e1d940d };
    };
    "assets/src/assets/Image/Menu/User.png" =     {
        hash2 = {length = 32, bytes = 0xd194bc40 33671b7c 63ebe456 1af0b4fd ... da57df42 9b8cae65 };
    };
    "assets/src/assets/Image/Menu/localtaxi.png" =     {
        hash2 = {length = 32, bytes = 0xc8f64515 8eaa69c9 c51669cf 51650b0b ... 0c059bee 4bb7fb93 };
    };
    "assets/src/assets/Image/TaxiBanner.png" =     {
        hash2 = {length = 32, bytes = 0x97f42d07 fc55ea1b d70c31da 553c8401 ... a6d508c6 a1cdc61b };
    };
    "assets/src/assets/Image/bgImg.png" =     {
        hash2 = {length = 32, bytes = 0x03957f65 400a7a1e 0a44c44e 04457142 ... b8971ad6 5d87f642 };
    };
    "assets/src/assets/Image/logo.png" =     {
        hash2 = {length = 32, bytes = 0x94a51ddd 1acc81df b537e8cb ea0ea05e ... 2c64de0d 12d2785b };
    };
    "assets/src/assets/Image/slide.png" =     {
        hash2 = {length = 32, bytes = 0x97ac755d 105abe72 4f22a35e 0d0e14de ... bc94f1ed 7aeceb24 };
    };
    "assets/src/assets/Image/techcreator.png" =     {
        hash2 = {length = 32, bytes = 0xf58f0ea9 552debd6 f323fa14 e534deb5 ... 36bd754c 03ca94b5 };
    };
    "assets/src/assets/default/defaultPerson.jpeg" =     {
        hash2 = {length = 32, bytes = 0xfd633315 df9b4281 c66ef9bc 57088069 ... 00d1438c 8652ed0b };
    };
    "embedded.mobileprovision" =     {
        hash2 = {length = 32, bytes = 0x1e70c312 0d346456 4f5c8ef5 118d1a34 ... 13fb91e0 c9b5f100 };
    };
    "main.jsbundle" =     {
        hash2 = {length = 32, bytes = 0x89286607 563d7c0b e698bcfd ecc39436 ... 63284fa4 a0300c51 };
    };
}], enableSignatureValidation: true)'
2025-02-19 07:26:09 +0000  Sending request A4E72609-30DC-4E66-B5DA-566E8330E487 to <https://developerservices2.apple.com/services/v1/certificates> for session <DVTServicesAccountBasedSession: 0x60001b317180; account='<DVTAppleIDBasedDeveloperAccount: 0x6000000f6700; username='<EMAIL>'>'>.
Method: POST

Headers:
{
    Accept = "application/vnd.api+json";
    "Accept-Encoding" = "gzip, deflate";
    "Content-Length" = 104;
    "Content-Type" = "application/vnd.api+json";
    DSESSIONID = 1i7h1a6bqqk6nur7nh50efj7jpaka0hc3satajufl754jjhk3uri;
    "User-Agent" = Xcode;
    "X-Apple-App-Info" = "com.apple.gs.xcode.auth";
    "X-Apple-GS-Token" = "AAAABLwIAAAAAGemDT8RDWdzLnhjb2RlLmF1dGi9AL5XCym1Gek/WVGQEkN1O0rWYh5MOo59ovBY5UI/rXv5C0DwtFGJDQX81kZsvzjBWeHJ0ZltQsAkd4Ud1uBtB1rwwr3ksEEaHKx94XhKBTiHy4/wM65oFqaWdwgKanGhpa5oAfmd2FaKrUi0rn3+4KzFY9ef";
    "X-Apple-I-Identity-Id" = "000568-10-f65e5f67-0de0-426d-a18b-72683542c140";
    "X-HTTP-Method-Override" = GET;
    "X-MMe-Client-Info" = "<Mac15,7> <macOS;14.1;23B2073> <com.apple.AuthKit/1 (com.apple.dt.Xcode/22618)>";
    "X-Mme-Device-Id" = "C455628C-F5FE-541C-B93B-2258A26AC0B5";
    "X-Xcode-Version" = "15.3 (15E204a)";
}

Payload:
{"urlEncodedQueryParams":"teamId=9J6MH85TLG&filter%5BcertificateType%5D=DISTRIBUTION_MANAGED&limit=200"}
2025-02-19 07:26:10 +0000  Received response for A4E72609-30DC-4E66-B5DA-566E8330E487 @ <https://developerservices2.apple.com/services/v1/certificates>. Code = 0
2025-02-19 07:26:10 +0000  Response payload: {
  "data" : [ {
    "type" : "certificates",
    "id" : "W25BG77468",
    "attributes" : {
      "requesterEmail" : "<EMAIL>",
      "serialNumber" : "70EF3724C03181AB38394771C85CB1E5",
      "certificateContent" : "MIIF0zCCBLugAwIBAgIQcO83JMAxgas4OUdxyFyx5TANBgkqhkiG9w0BAQsFADB1MUQwQgYDVQQDDDtBcHBsZSBXb3JsZHdpZGUgRGV2ZWxvcGVyIFJlbGF0aW9ucyBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTELMAkGA1UECwwCRzMxEzARBgNVBAoMCkFwcGxlIEluYy4xCzAJBgNVBAYTAlVTMB4XDTI1MDIxOTA3MDc1MloXDTI2MDIxOTA3MDc1MVowgYcxGjAYBgoJkiaJk/IsZAEBDAo5SjZNSDg1VExHMTMwMQYDVQQDDCpBcHBsZSBEaXN0cmlidXRpb246IFNhdWQgS2hhbiAoOUo2TUg4NVRMRykxEzARBgNVBAsMCjlKNk1IODVUTEcxEjAQBgNVBAoMCVNhdWQgS2hhbjELMAkGA1UEBhMCUEswggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCxGLDgp3FuSqZ8WYp6irnl+XAwoXoyTYv08IMnjHNkA/Nu6FyKZYefBZU9fuH1rnPRU7GPCfwHMOIls0c5YAgYW5BW3eAaMo02u7YROvANSTtCoaogNnxzdGtRNc8ACGrBfNkFN/ZbhAJefnpxxA6aictjMugd0O1vfDCFrwAjl8WJpI/cOL71Ve1kGuelNnwIvYox3LqN5H6B8CRHdxBzrCQGP+41C4KxX1pbpIcFlaoed5lifO3TydPJOF20hkc3fXerUNeeLILbGIZurP9tfzlSiLwHMHjeR8e377Stfb+sRIcOJPUyBpIoWb1W/uXSi1r/9LBPVG/W1onb6hQ9AgMBAAGjggJKMIICRjAMBgNVHRMBAf8EAjAAMB8GA1UdIwQYMBaAFAn+wBWQ+a9kCpISuSYoYwyX7KeyMHAGCCsGAQUFBwEBBGQwYjAtBggrBgEFBQcwAoYhaHR0cDovL2NlcnRzLmFwcGxlLmNvbS93d2RyZzMuZGVyMDEGCCsGAQUFBzABhiVodHRwOi8vb2NzcC5hcHBsZS5jb20vb2NzcDAzLXd3ZHJnMzExMIIBHgYDVR0gBIIBFTCCAREwggENBgkqhkiG92NkBQEwgf8wgcMGCCsGAQUFBwICMIG2DIGzUmVsaWFuY2Ugb24gdGhpcyBjZXJ0aWZpY2F0ZSBieSBhbnkgcGFydHkgYXNzdW1lcyBhY2NlcHRhbmNlIG9mIHRoZSB0aGVuIGFwcGxpY2FibGUgc3RhbmRhcmQgdGVybXMgYW5kIGNvbmRpdGlvbnMgb2YgdXNlLCBjZXJ0aWZpY2F0ZSBwb2xpY3kgYW5kIGNlcnRpZmljYXRpb24gcHJhY3RpY2Ugc3RhdGVtZW50cy4wNwYIKwYBBQUHAgEWK2h0dHBzOi8vd3d3LmFwcGxlLmNvbS9jZXJ0aWZpY2F0ZWF1dGhvcml0eS8wFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwMwHQYDVR0OBBYEFPjBhep0hjPJESKh9eAzq7yh3p56MA4GA1UdDwEB/wQEAwIHgDATBgoqhkiG92NkBgEHAQH/BAIFADAQBgoqhkiG92NkBgEgBAIFADATBgoqhkiG92NkBgEEAQH/BAIFADANBgkqhkiG9w0BAQsFAAOCAQEAZSmrTo3WaO25Sfc3uMKfnbBfplvEhGNiw2q0Te4waZb5btxvAv3kW7RsQjwuE6hHZhQR3bsD7h3HBWjrGhmZvpg65raZDXkHi0Wz7yylTxUcpNxs58VuGvvf+xN9sQsLk5K5rR3Nyq7je6P8CqFt729n/2M4odg4izzeQE0At3m5urb2UH0jC35eVHs9MImwJ1+tkQzwzUG7D77MpKVjIfGyHWCqDYfirZVGTS5b/jRQzJHA/l3hkfjl5LqurLI5mOK0b2WfLE3rF67D7s7BBQf91QC2v8L/zss2VDfWYaDuhXSjIkB6vRo5AC26NAFbZXutLJMR+/bf4iBh5uxG1Q==",
      "displayName" : "Saud Khan",
      "requesterLastName" : "Khan",
      "csrContent" : null,
      "machineName" : null,
      "platform" : null,
      "requesterFirstName" : "Saud",
      "machineId" : null,
      "name" : "Apple Distribution: Saud Khan",
      "responseId" : "872a6637-7b8a-4819-b463-187e30388931",
      "expirationDate" : "2026-02-19T07:07:51.000+00:00",
      "certificateType" : "DISTRIBUTION_MANAGED"
    },
    "links" : {
      "self" : "https://developerservices2.apple.com:443/services/v1/certificates/W25BG77468"
    }
  } ],
  "links" : {
    "self" : "https://developerservices2.apple.com:443/services/v1/certificates?filter%5BcertificateType%5D=DISTRIBUTION_MANAGED&limit=200"
  },
  "meta" : {
    "paging" : {
      "total" : 1,
      "limit" : 200
    }
  }
}

2025-02-19 07:26:10 +0000  Sending request C603420D-209F-4288-A55F-E046BED00BCD to <https://developerservices3.apple.com/services/v1/batch> for session <DVTServicesAccountBasedSession: 0x60001b317180; account='<DVTAppleIDBasedDeveloperAccount: 0x6000000f6700; username='<EMAIL>'>'>.
Method: POST

Headers:
{
    Accept = "application/vnd.api+json";
    "Accept-Encoding" = "gzip, deflate";
    "Content-Length" = 488;
    "Content-Type" = "application/vnd.api+json";
    DSESSIONID = 1i7h1a6bqqk6nur7nh50efj7jpaka0hc3satajufl754jjhk3uri;
    "User-Agent" = Xcode;
    "X-Apple-App-Info" = "com.apple.gs.xcode.auth";
    "X-Apple-GS-Token" = "AAAABLwIAAAAAGemDT8RDWdzLnhjb2RlLmF1dGi9AL5XCym1Gek/WVGQEkN1O0rWYh5MOo59ovBY5UI/rXv5C0DwtFGJDQX81kZsvzjBWeHJ0ZltQsAkd4Ud1uBtB1rwwr3ksEEaHKx94XhKBTiHy4/wM65oFqaWdwgKanGhpa5oAfmd2FaKrUi0rn3+4KzFY9ef";
    "X-Apple-I-Identity-Id" = "000568-10-f65e5f67-0de0-426d-a18b-72683542c140";
    "X-MMe-Client-Info" = "<Mac15,7> <macOS;14.1;23B2073> <com.apple.AuthKit/1 (com.apple.dt.Xcode/22618)>";
    "X-Mme-Device-Id" = "C455628C-F5FE-541C-B93B-2258A26AC0B5";
    "X-Xcode-Version" = "15.3 (15E204a)";
}

Payload:
{"data":{"type":"batch","attributes":{"resourceList":[{"data":{"relationships":{"bundleId":{"data":{"id":"SKC935Z4N9","type":"bundleIds"}},"certificate":{"data":{"type":"certificates","id":"W25BG77468"}}},"attributes":{"digestHash":"7CC36EEBC1B9914098C67DFDFCC4FC69B1EA25B211F8189EF95363020B59A651","name":"0","cdHashSha256":"7CC36EEBC1B9914098C67DFDFCC4FC69B1EA25B211F8189EF95363020B59A651","teamId":"9J6MH85TLG"},"type":"codeSignatures"}}],"teamId":"9J6MH85TLG","resourceVersion":"1"}}}
2025-02-19 07:26:10 +0000  Received response for C603420D-209F-4288-A55F-E046BED00BCD @ <https://developerservices3.apple.com/services/v1/batch>. Code = 0
2025-02-19 07:26:10 +0000  Response payload: {
  "data" : {
    "type" : "batch",
    "id" : "810844e3-5c1b-4a6a-a36d-c7de0867a4fa",
    "attributes" : {
      "responseId" : "810844e3-5c1b-4a6a-a36d-c7de0867a4fa",
      "resourceList" : [ {
        "type" : "codeSignatures",
        "id" : "2c39f101-2896-4a8b-9fa7-7added56df6f",
        "attributes" : {
          "digestHash" : "7CC36EEBC1B9914098C67DFDFCC4FC69B1EA25B211F8189EF95363020B59A651",
          "cdHashSha256" : "7CC36EEBC1B9914098C67DFDFCC4FC69B1EA25B211F8189EF95363020B59A651",
          "signature" : "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",
          "name" : "0",
          "responseId" : "810844e3-5c1b-4a6a-a36d-c7de0867a4fa",
          "cmsSignature" : "MIAGCSqGSIb3DQEHAqCAMIACAQExDzANBglghkgBZQMEAgEFADCABgkqhkiG9w0BBwEAAKCAMIIF0zCCBLugAwIBAgIQcO83JMAxgas4OUdxyFyx5TANBgkqhkiG9w0BAQsFADB1MUQwQgYDVQQDDDtBcHBsZSBXb3JsZHdpZGUgRGV2ZWxvcGVyIFJlbGF0aW9ucyBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTELMAkGA1UECwwCRzMxEzARBgNVBAoMCkFwcGxlIEluYy4xCzAJBgNVBAYTAlVTMB4XDTI1MDIxOTA3MDc1MloXDTI2MDIxOTA3MDc1MVowgYcxGjAYBgoJkiaJk/IsZAEBDAo5SjZNSDg1VExHMTMwMQYDVQQDDCpBcHBsZSBEaXN0cmlidXRpb246IFNhdWQgS2hhbiAoOUo2TUg4NVRMRykxEzARBgNVBAsMCjlKNk1IODVUTEcxEjAQBgNVBAoMCVNhdWQgS2hhbjELMAkGA1UEBhMCUEswggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCxGLDgp3FuSqZ8WYp6irnl+XAwoXoyTYv08IMnjHNkA/Nu6FyKZYefBZU9fuH1rnPRU7GPCfwHMOIls0c5YAgYW5BW3eAaMo02u7YROvANSTtCoaogNnxzdGtRNc8ACGrBfNkFN/ZbhAJefnpxxA6aictjMugd0O1vfDCFrwAjl8WJpI/cOL71Ve1kGuelNnwIvYox3LqN5H6B8CRHdxBzrCQGP+41C4KxX1pbpIcFlaoed5lifO3TydPJOF20hkc3fXerUNeeLILbGIZurP9tfzlSiLwHMHjeR8e377Stfb+sRIcOJPUyBpIoWb1W/uXSi1r/9LBPVG/W1onb6hQ9AgMBAAGjggJKMIICRjAMBgNVHRMBAf8EAjAAMB8GA1UdIwQYMBaAFAn+wBWQ+a9kCpISuSYoYwyX7KeyMHAGCCsGAQUFBwEBBGQwYjAtBggrBgEFBQcwAoYhaHR0cDovL2NlcnRzLmFwcGxlLmNvbS93d2RyZzMuZGVyMDEGCCsGAQUFBzABhiVodHRwOi8vb2NzcC5hcHBsZS5jb20vb2NzcDAzLXd3ZHJnMzExMIIBHgYDVR0gBIIBFTCCAREwggENBgkqhkiG92NkBQEwgf8wgcMGCCsGAQUFBwICMIG2DIGzUmVsaWFuY2Ugb24gdGhpcyBjZXJ0aWZpY2F0ZSBieSBhbnkgcGFydHkgYXNzdW1lcyBhY2NlcHRhbmNlIG9mIHRoZSB0aGVuIGFwcGxpY2FibGUgc3RhbmRhcmQgdGVybXMgYW5kIGNvbmRpdGlvbnMgb2YgdXNlLCBjZXJ0aWZpY2F0ZSBwb2xpY3kgYW5kIGNlcnRpZmljYXRpb24gcHJhY3RpY2Ugc3RhdGVtZW50cy4wNwYIKwYBBQUHAgEWK2h0dHBzOi8vd3d3LmFwcGxlLmNvbS9jZXJ0aWZpY2F0ZWF1dGhvcml0eS8wFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwMwHQYDVR0OBBYEFPjBhep0hjPJESKh9eAzq7yh3p56MA4GA1UdDwEB/wQEAwIHgDATBgoqhkiG92NkBgEHAQH/BAIFADAQBgoqhkiG92NkBgEgBAIFADATBgoqhkiG92NkBgEEAQH/BAIFADANBgkqhkiG9w0BAQsFAAOCAQEAZSmrTo3WaO25Sfc3uMKfnbBfplvEhGNiw2q0Te4waZb5btxvAv3kW7RsQjwuE6hHZhQR3bsD7h3HBWjrGhmZvpg65raZDXkHi0Wz7yylTxUcpNxs58VuGvvf+xN9sQsLk5K5rR3Nyq7je6P8CqFt729n/2M4odg4izzeQE0At3m5urb2UH0jC35eVHs9MImwJ1+tkQzwzUG7D77MpKVjIfGyHWCqDYfirZVGTS5b/jRQzJHA/l3hkfjl5LqurLI5mOK0b2WfLE3rF67D7s7BBQf91QC2v8L/zss2VDfWYaDuhXSjIkB6vRo5AC26NAFbZXutLJMR+/bf4iBh5uxG1TCCBFEwggM5oAMCAQICEHyvaQoltzn+e5tEesF4xe4wDQYJKoZIhvcNAQELBQAwYjELMAkGA1UEBhMCVVMxEzARBgNVBAoTCkFwcGxlIEluYy4xJjAkBgNVBAsTHUFwcGxlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MRYwFAYDVQQDEw1BcHBsZSBSb290IENBMB4XDTIwMDIxOTE4MTM0N1oXDTMwMDIyMDAwMDAwMFowdTFEMEIGA1UEAww7QXBwbGUgV29ybGR3aWRlIERldmVsb3BlciBSZWxhdGlvbnMgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkxCzAJBgNVBAsMAkczMRMwEQYDVQQKDApBcHBsZSBJbmMuMQswCQYDVQQGEwJVUzCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBANj1ifyoWQuHx0yRLi1WkNN4HaQw6aVI7wtDvy2E+yRdRRvl61k2ElxUZJ5sYi20xpfKzDz21gaR/OUCphxqtHnVZ8us6T8HEeGEvEcdZo45oui0Se060uEQYHqOk4zKwNoMwIPQ4/mR1qeMyMFz4q5G0WKdkqiQYDd9aJbNjeD8m/p4u+N7ry0X3Vv+gFkjdRe4EsHtG+U0zv4itZ5JsOVVv4+EojyousWGlRbkAMmfA9DMszoWe9YFXJ3rR64NtS2MDmlNQNBLTDsEno0hsawuQ1QwzsGKhpSYS99qDT/++xyulxfCeAow4F8fVDtJtyUAGjeC0laXpVJJfGA4m+8CAwEAAaOB7zCB7DASBgNVHRMBAf8ECDAGAQH/AgEAMB8GA1UdIwQYMBaAFCvQaUeUdgn+9GuNLkCm90dNfwheMEQGCCsGAQUFBwEBBDgwNjA0BggrBgEFBQcwAYYoaHR0cDovL29jc3AuYXBwbGUuY29tL29jc3AwMy1hcHBsZXJvb3RjYTAuBgNVHR8EJzAlMCOgIaAfhh1odHRwOi8vY3JsLmFwcGxlLmNvbS9yb290LmNybDAdBgNVHQ4EFgQUCf7AFZD5r2QKkhK5JihjDJfsp7IwDgYDVR0PAQH/BAQDAgEGMBAGCiqGSIb3Y2QGAgEEAgUAMA0GCSqGSIb3DQEBCwUAA4IBAQCtZRPo9uCBd0QCR0K+X6U5IOpiqf3FkMlzE9Wem9CqD42Ny+0Bz2woQFvHVSRB+PzPwbUj6dzs8W/KgB13wsRhSSVnrw/KOSWt0+N6zDMoDQ4uoVdAc/rmXK4GUSnt44UMT2HcMhaLd9BEyl1yAzFGnK6bQBr69ODTPvovjGafl8RUWe/SSPQHmUlgWRnH3ZTRwMFsf3gh7wzrO2yZgktSYDi1OYJt7FIxU74PkUxJSXSPplHLhEdOHXUnbr350lzzf8JsCwk24mTkwjcDFBnV6mqUqqnb/vaaCGeL7yu4qhdJg6/Py7zpz+qVcbC0RaLM5YeqCsNBOnlc2lA0nZU7MIIEuzCCA6OgAwIBAgIBAjANBgkqhkiG9w0BAQUFADBiMQswCQYDVQQGEwJVUzETMBEGA1UEChMKQXBwbGUgSW5jLjEmMCQGA1UECxMdQXBwbGUgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkxFjAUBgNVBAMTDUFwcGxlIFJvb3QgQ0EwHhcNMDYwNDI1MjE0MDM2WhcNMzUwMjA5MjE0MDM2WjBiMQswCQYDVQQGEwJVUzETMBEGA1UEChMKQXBwbGUgSW5jLjEmMCQGA1UECxMdQXBwbGUgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkxFjAUBgNVBAMTDUFwcGxlIFJvb3QgQ0EwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDkkakJH5HbHkdQ6wXtXnmELes2oldMVeyLGYne+Uts9QerIjAC6Bg++FAJ039BqJj50cpmnCRrEdCju+QbKsMflZ56DKRHi1vUFjczy8QPTc4UadHJGXL1XQ7Vf1+b8iUDulWPTV0N8WQ1IxVLFVkds5T39pyez1C6wVhQZ48ItCD3y6wsIG9wtj8BMIy3Q88PnT3zK0koGsj+zrW5DtleHNbLPbU6rfQPDgCSC7EhFi501TwN22IWq6NxkkdTVcGvL0Gz+PvjcM3mo0xFfh9Ma1CWQYnEdGILEINBhzOKgbEwWOxaBDKMaLOPHd5lc/9nXmW8Sdh2nzMUZaF3lMktAgMBAAGjggF6MIIBdjAOBgNVHQ8BAf8EBAMCAQYwDwYDVR0TAQH/BAUwAwEB/zAdBgNVHQ4EFgQUK9BpR5R2Cf70a40uQKb3R01/CF4wHwYDVR0jBBgwFoAUK9BpR5R2Cf70a40uQKb3R01/CF4wggERBgNVHSAEggEIMIIBBDCCAQAGCSqGSIb3Y2QFATCB8jAqBggrBgEFBQcCARYeaHR0cHM6Ly93d3cuYXBwbGUuY29tL2FwcGxlY2EvMIHDBggrBgEFBQcCAjCBthqBs1JlbGlhbmNlIG9uIHRoaXMgY2VydGlmaWNhdGUgYnkgYW55IHBhcnR5IGFzc3VtZXMgYWNjZXB0YW5jZSBvZiB0aGUgdGhlbiBhcHBsaWNhYmxlIHN0YW5kYXJkIHRlcm1zIGFuZCBjb25kaXRpb25zIG9mIHVzZSwgY2VydGlmaWNhdGUgcG9saWN5IGFuZCBjZXJ0aWZpY2F0aW9uIHByYWN0aWNlIHN0YXRlbWVudHMuMA0GCSqGSIb3DQEBBQUAA4IBAQBcNplMLXi37Yyb3PN3m/J20ncwT8EfhYOFG5k9RzfyqZtAjizUsZAS2L70c5vu0mQPy3lPNNiiPvl4/2vIB+x9OYOLUyDTOMSxv5pPCmv/K/xZpwUJfBdAVhEedNO3iyM7R6PVbyTi69G3cN8PReEnyvFteO3ntRcXqNx+IjXKJdXZD9Zr1KIkIxH3oayPc4FgxhtbCS+SsvhESPBgOJ4V9T0mZyCKM2r3DYLP3uujL/lTaltkwGMzd/c6ByxW69oPIQ7aunMZT7XZNn/Bh1XZp5m5MkL72NVxnn6hUrcbvZNCJBIqxw8dtk2cXmPIS4AXUKqK1drk/NAJBzewdXUhAAAxggNiMIIDXgIBATCBiTB1MUQwQgYDVQQDDDtBcHBsZSBXb3JsZHdpZGUgRGV2ZWxvcGVyIFJlbGF0aW9ucyBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTELMAkGA1UECwwCRzMxEzARBgNVBAoMCkFwcGxlIEluYy4xCzAJBgNVBAYTAlVTAhBw7zckwDGBqzg5R3HIXLHlMA0GCWCGSAFlAwQCAQUAoIIBqTAYBgkqhkiG9w0BCQMxCwYJKoZIhvcNAQcBMBwGCSqGSIb3DQEJBTEPFw0yNTAyMTkwNzI2MTBaMC8GCSqGSIb3DQEJBDEiBCB8w27rwbmRQJjGff38xPxpseolshH4GJ75U2MCC1mmUTA8BgkqhkiG92NkCQIxLzAtBglghkgBZQMEAgEEIHzDbuvBuZFAmMZ9/fzE/Gmx6iWyEfgYnvlTYwILWaZRMIH/BgkqhkiG92NkCQExgfEEge48IURPQ1RZUEUgcGxpc3QgUFVCTElDICItLy9BcHBsZS8vRFREIFBMSVNUIDEuMC8vRU4iICJodHRwOi8vd3d3LmFwcGxlLmNvbS9EVERzL1Byb3BlcnR5TGlzdC0xLjAuZHRkIj4KPHBsaXN0IHZlcnNpb249IjEuMCI+CjxkaWN0PgoJPGtleT5jZGhhc2hlczwva2V5PgoJPGFycmF5PgoJCTxkYXRhPgoJCWZNTnU2OEc1a1VDWXhuMzkvTVQ4YWJIcUpiST0KCQk8L2RhdGE+Cgk8L2FycmF5Pgo8L2RpY3Q+CjwvcGxpc3Q+MA0GCSqGSIb3DQEBCwUABIIBAI+leiJMqQC5in1cr8TaINoRE5q0peR+0WgxwEGs/01xttnpqgixdjWglvoDtLjm+lYzSG27M44w+bp37JRezdttm6m/TDZzMc/MP0HZzNjyRrMsMLsVXHbt0Z9rtrbWp51jb2yfQWKYCKIA7W0hIMJ8Zr8CJKG9wMIoEdbt9AGaemE/gWhgjg39REelkTqnLS184j4C8zgBXO4vHWw9IXDi053HkEtPcUlas65vFBRmbtoMidcLDCCFYRjYhOtOuZt9ZdKVayIs64lNm1cBbfzR89WI4wzYhH1RleTVRTIGXHnfCVRaNPCJfDeIx4OOM0tZPC0NRDOtCGtVJWYBG7IAAAAAAAA=",
          "timestamp" : false
        },
        "relationships" : {
          "bundleId" : {
            "data" : {
              "type" : "bundleIds",
              "id" : "SKC935Z4N9"
            },
            "links" : {
              "self" : {
                "href" : "https://developerservices3.apple.com:443/services/v1/codeSignatures/2c39f101-2896-4a8b-9fa7-7added56df6f/relationships/bundleId"
              },
              "related" : {
                "href" : "https://developerservices3.apple.com:443/services/v1/codeSignatures/2c39f101-2896-4a8b-9fa7-7added56df6f/bundleId"
              }
            }
          },
          "certificate" : {
            "data" : {
              "type" : "certificates",
              "id" : "W25BG77468"
            },
            "links" : {
              "self" : {
                "href" : "https://developerservices3.apple.com:443/services/v1/codeSignatures/2c39f101-2896-4a8b-9fa7-7added56df6f/relationships/certificate"
              },
              "related" : {
                "href" : "https://developerservices3.apple.com:443/services/v1/codeSignatures/2c39f101-2896-4a8b-9fa7-7added56df6f/certificate"
              }
            }
          }
        },
        "links" : {
          "self" : {
            "href" : "https://developerservices3.apple.com:443/services/v1/codeSignatures/2c39f101-2896-4a8b-9fa7-7added56df6f"
          }
        }
      } ],
      "resourceVersion" : "1"
    },
    "links" : {
      "self" : "https://developerservices3.apple.com:443/services/v1/batch/810844e3-5c1b-4a6a-a36d-c7de0867a4fa"
    }
  },
  "links" : {
    "self" : "https://developerservices3.apple.com:443/services/v1/batch"
  }
}

2025-02-19 07:26:10 +0000  Remote signature for /var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app (arch: arm64) is MIAGCSqGSIb3DQEHAqCAMIACAQExDzANBglghkgBZQMEAgEFADCABgkqhkiG9w0BBwEAAKCAMIIF0zCCBLugAwIBAgIQcO83JMAxgas4OUdxyFyx5TANBgkqhkiG9w0BAQsFADB1MUQwQgYDVQQDDDtBcHBsZSBXb3JsZHdpZGUgRGV2ZWxvcGVyIFJlbGF0aW9ucyBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTELMAkGA1UECwwCRzMxEzARBgNVBAoMCkFwcGxlIEluYy4xCzAJBgNVBAYTAlVTMB4XDTI1MDIxOTA3MDc1MloXDTI2MDIxOTA3MDc1MVowgYcxGjAYBgoJkiaJk/IsZAEBDAo5SjZNSDg1VExHMTMwMQYDVQQDDCpBcHBsZSBEaXN0cmlidXRpb246IFNhdWQgS2hhbiAoOUo2TUg4NVRMRykxEzARBgNVBAsMCjlKNk1IODVUTEcxEjAQBgNVBAoMCVNhdWQgS2hhbjELMAkGA1UEBhMCUEswggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCxGLDgp3FuSqZ8WYp6irnl+XAwoXoyTYv08IMnjHNkA/Nu6FyKZYefBZU9fuH1rnPRU7GPCfwHMOIls0c5YAgYW5BW3eAaMo02u7YROvANSTtCoaogNnxzdGtRNc8ACGrBfNkFN/ZbhAJefnpxxA6aictjMugd0O1vfDCFrwAjl8WJpI/cOL71Ve1kGuelNnwIvYox3LqN5H6B8CRHdxBzrCQGP+41C4KxX1pbpIcFlaoed5lifO3TydPJOF20hkc3fXerUNeeLILbGIZurP9tfzlSiLwHMHjeR8e377Stfb+sRIcOJPUyBpIoWb1W/uXSi1r/9LBPVG/W1onb6hQ9AgMBAAGjggJKMIICRjAMBgNVHRMBAf8EAjAAMB8GA1UdIwQYMBaAFAn+wBWQ+a9kCpISuSYoYwyX7KeyMHAGCCsGAQUFBwEBBGQwYjAtBggrBgEFBQcwAoYhaHR0cDovL2NlcnRzLmFwcGxlLmNvbS93d2RyZzMuZGVyMDEGCCsGAQUFBzABhiVodHRwOi8vb2NzcC5hcHBsZS5jb20vb2NzcDAzLXd3ZHJnMzExMIIBHgYDVR0gBIIBFTCCAREwggENBgkqhkiG92NkBQEwgf8wgcMGCCsGAQUFBwICMIG2DIGzUmVsaWFuY2Ugb24gdGhpcyBjZXJ0aWZpY2F0ZSBieSBhbnkgcGFydHkgYXNzdW1lcyBhY2NlcHRhbmNlIG9mIHRoZSB0aGVuIGFwcGxpY2FibGUgc3RhbmRhcmQgdGVybXMgYW5kIGNvbmRpdGlvbnMgb2YgdXNlLCBjZXJ0aWZpY2F0ZSBwb2xpY3kgYW5kIGNlcnRpZmljYXRpb24gcHJhY3RpY2Ugc3RhdGVtZW50cy4wNwYIKwYBBQUHAgEWK2h0dHBzOi8vd3d3LmFwcGxlLmNvbS9jZXJ0aWZpY2F0ZWF1dGhvcml0eS8wFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwMwHQYDVR0OBBYEFPjBhep0hjPJESKh9eAzq7yh3p56MA4GA1UdDwEB/wQEAwIHgDATBgoqhkiG92NkBgEHAQH/BAIFADAQBgoqhkiG92NkBgEgBAIFADATBgoqhkiG92NkBgEEAQH/BAIFADANBgkqhkiG9w0BAQsFAAOCAQEAZSmrTo3WaO25Sfc3uMKfnbBfplvEhGNiw2q0Te4waZb5btxvAv3kW7RsQjwuE6hHZhQR3bsD7h3HBWjrGhmZvpg65raZDXkHi0Wz7yylTxUcpNxs58VuGvvf+xN9sQsLk5K5rR3Nyq7je6P8CqFt729n/2M4odg4izzeQE0At3m5urb2UH0jC35eVHs9MImwJ1+tkQzwzUG7D77MpKVjIfGyHWCqDYfirZVGTS5b/jRQzJHA/l3hkfjl5LqurLI5mOK0b2WfLE3rF67D7s7BBQf91QC2v8L/zss2VDfWYaDuhXSjIkB6vRo5AC26NAFbZXutLJMR+/bf4iBh5uxG1TCCBFEwggM5oAMCAQICEHyvaQoltzn+e5tEesF4xe4wDQYJKoZIhvcNAQELBQAwYjELMAkGA1UEBhMCVVMxEzARBgNVBAoTCkFwcGxlIEluYy4xJjAkBgNVBAsTHUFwcGxlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MRYwFAYDVQQDEw1BcHBsZSBSb290IENBMB4XDTIwMDIxOTE4MTM0N1oXDTMwMDIyMDAwMDAwMFowdTFEMEIGA1UEAww7QXBwbGUgV29ybGR3aWRlIERldmVsb3BlciBSZWxhdGlvbnMgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkxCzAJBgNVBAsMAkczMRMwEQYDVQQKDApBcHBsZSBJbmMuMQswCQYDVQQGEwJVUzCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBANj1ifyoWQuHx0yRLi1WkNN4HaQw6aVI7wtDvy2E+yRdRRvl61k2ElxUZJ5sYi20xpfKzDz21gaR/OUCphxqtHnVZ8us6T8HEeGEvEcdZo45oui0Se060uEQYHqOk4zKwNoMwIPQ4/mR1qeMyMFz4q5G0WKdkqiQYDd9aJbNjeD8m/p4u+N7ry0X3Vv+gFkjdRe4EsHtG+U0zv4itZ5JsOVVv4+EojyousWGlRbkAMmfA9DMszoWe9YFXJ3rR64NtS2MDmlNQNBLTDsEno0hsawuQ1QwzsGKhpSYS99qDT/++xyulxfCeAow4F8fVDtJtyUAGjeC0laXpVJJfGA4m+8CAwEAAaOB7zCB7DASBgNVHRMBAf8ECDAGAQH/AgEAMB8GA1UdIwQYMBaAFCvQaUeUdgn+9GuNLkCm90dNfwheMEQGCCsGAQUFBwEBBDgwNjA0BggrBgEFBQcwAYYoaHR0cDovL29jc3AuYXBwbGUuY29tL29jc3AwMy1hcHBsZXJvb3RjYTAuBgNVHR8EJzAlMCOgIaAfhh1odHRwOi8vY3JsLmFwcGxlLmNvbS9yb290LmNybDAdBgNVHQ4EFgQUCf7AFZD5r2QKkhK5JihjDJfsp7IwDgYDVR0PAQH/BAQDAgEGMBAGCiqGSIb3Y2QGAgEEAgUAMA0GCSqGSIb3DQEBCwUAA4IBAQCtZRPo9uCBd0QCR0K+X6U5IOpiqf3FkMlzE9Wem9CqD42Ny+0Bz2woQFvHVSRB+PzPwbUj6dzs8W/KgB13wsRhSSVnrw/KOSWt0+N6zDMoDQ4uoVdAc/rmXK4GUSnt44UMT2HcMhaLd9BEyl1yAzFGnK6bQBr69ODTPvovjGafl8RUWe/SSPQHmUlgWRnH3ZTRwMFsf3gh7wzrO2yZgktSYDi1OYJt7FIxU74PkUxJSXSPplHLhEdOHXUnbr350lzzf8JsCwk24mTkwjcDFBnV6mqUqqnb/vaaCGeL7yu4qhdJg6/Py7zpz+qVcbC0RaLM5YeqCsNBOnlc2lA0nZU7MIIEuzCCA6OgAwIBAgIBAjANBgkqhkiG9w0BAQUFADBiMQswCQYDVQQGEwJVUzETMBEGA1UEChMKQXBwbGUgSW5jLjEmMCQGA1UECxMdQXBwbGUgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkxFjAUBgNVBAMTDUFwcGxlIFJvb3QgQ0EwHhcNMDYwNDI1MjE0MDM2WhcNMzUwMjA5MjE0MDM2WjBiMQswCQYDVQQGEwJVUzETMBEGA1UEChMKQXBwbGUgSW5jLjEmMCQGA1UECxMdQXBwbGUgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkxFjAUBgNVBAMTDUFwcGxlIFJvb3QgQ0EwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDkkakJH5HbHkdQ6wXtXnmELes2oldMVeyLGYne+Uts9QerIjAC6Bg++FAJ039BqJj50cpmnCRrEdCju+QbKsMflZ56DKRHi1vUFjczy8QPTc4UadHJGXL1XQ7Vf1+b8iUDulWPTV0N8WQ1IxVLFVkds5T39pyez1C6wVhQZ48ItCD3y6wsIG9wtj8BMIy3Q88PnT3zK0koGsj+zrW5DtleHNbLPbU6rfQPDgCSC7EhFi501TwN22IWq6NxkkdTVcGvL0Gz+PvjcM3mo0xFfh9Ma1CWQYnEdGILEINBhzOKgbEwWOxaBDKMaLOPHd5lc/9nXmW8Sdh2nzMUZaF3lMktAgMBAAGjggF6MIIBdjAOBgNVHQ8BAf8EBAMCAQYwDwYDVR0TAQH/BAUwAwEB/zAdBgNVHQ4EFgQUK9BpR5R2Cf70a40uQKb3R01/CF4wHwYDVR0jBBgwFoAUK9BpR5R2Cf70a40uQKb3R01/CF4wggERBgNVHSAEggEIMIIBBDCCAQAGCSqGSIb3Y2QFATCB8jAqBggrBgEFBQcCARYeaHR0cHM6Ly93d3cuYXBwbGUuY29tL2FwcGxlY2EvMIHDBggrBgEFBQcCAjCBthqBs1JlbGlhbmNlIG9uIHRoaXMgY2VydGlmaWNhdGUgYnkgYW55IHBhcnR5IGFzc3VtZXMgYWNjZXB0YW5jZSBvZiB0aGUgdGhlbiBhcHBsaWNhYmxlIHN0YW5kYXJkIHRlcm1zIGFuZCBjb25kaXRpb25zIG9mIHVzZSwgY2VydGlmaWNhdGUgcG9saWN5IGFuZCBjZXJ0aWZpY2F0aW9uIHByYWN0aWNlIHN0YXRlbWVudHMuMA0GCSqGSIb3DQEBBQUAA4IBAQBcNplMLXi37Yyb3PN3m/J20ncwT8EfhYOFG5k9RzfyqZtAjizUsZAS2L70c5vu0mQPy3lPNNiiPvl4/2vIB+x9OYOLUyDTOMSxv5pPCmv/K/xZpwUJfBdAVhEedNO3iyM7R6PVbyTi69G3cN8PReEnyvFteO3ntRcXqNx+IjXKJdXZD9Zr1KIkIxH3oayPc4FgxhtbCS+SsvhESPBgOJ4V9T0mZyCKM2r3DYLP3uujL/lTaltkwGMzd/c6ByxW69oPIQ7aunMZT7XZNn/Bh1XZp5m5MkL72NVxnn6hUrcbvZNCJBIqxw8dtk2cXmPIS4AXUKqK1drk/NAJBzewdXUhAAAxggNiMIIDXgIBATCBiTB1MUQwQgYDVQQDDDtBcHBsZSBXb3JsZHdpZGUgRGV2ZWxvcGVyIFJlbGF0aW9ucyBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTELMAkGA1UECwwCRzMxEzARBgNVBAoMCkFwcGxlIEluYy4xCzAJBgNVBAYTAlVTAhBw7zckwDGBqzg5R3HIXLHlMA0GCWCGSAFlAwQCAQUAoIIBqTAYBgkqhkiG9w0BCQMxCwYJKoZIhvcNAQcBMBwGCSqGSIb3DQEJBTEPFw0yNTAyMTkwNzI2MTBaMC8GCSqGSIb3DQEJBDEiBCB8w27rwbmRQJjGff38xPxpseolshH4GJ75U2MCC1mmUTA8BgkqhkiG92NkCQIxLzAtBglghkgBZQMEAgEEIHzDbuvBuZFAmMZ9/fzE/Gmx6iWyEfgYnvlTYwILWaZRMIH/BgkqhkiG92NkCQExgfEEge48IURPQ1RZUEUgcGxpc3QgUFVCTElDICItLy9BcHBsZS8vRFREIFBMSVNUIDEuMC8vRU4iICJodHRwOi8vd3d3LmFwcGxlLmNvbS9EVERzL1Byb3BlcnR5TGlzdC0xLjAuZHRkIj4KPHBsaXN0IHZlcnNpb249IjEuMCI+CjxkaWN0PgoJPGtleT5jZGhhc2hlczwva2V5PgoJPGFycmF5PgoJCTxkYXRhPgoJCWZNTnU2OEc1a1VDWXhuMzkvTVQ4YWJIcUpiST0KCQk8L2RhdGE+Cgk8L2FycmF5Pgo8L2RpY3Q+CjwvcGxpc3Q+MA0GCSqGSIb3DQEBCwUABIIBAI+leiJMqQC5in1cr8TaINoRE5q0peR+0WgxwEGs/01xttnpqgixdjWglvoDtLjm+lYzSG27M44w+bp37JRezdttm6m/TDZzMc/MP0HZzNjyRrMsMLsVXHbt0Z9rtrbWp51jb2yfQWKYCKIA7W0hIMJ8Zr8CJKG9wMIoEdbt9AGaemE/gWhgjg39REelkTqnLS184j4C8zgBXO4vHWw9IXDi053HkEtPcUlas65vFBRmbtoMidcLDCCFYRjYhOtOuZt9ZdKVayIs64lNm1cBbfzR89WI4wzYhH1RleTVRTIGXHnfCVRaNPCJfDeIx4OOM0tZPC0NRDOtCGtVJWYBG7IAAAAAAAA=
2025-02-19 07:26:10 +0000  invoking codesign: <NSConcreteTask: 0x60000bac9ae0; launchPath='/usr/bin/codesign', arguments='(
    "-e",
    "--edit-cms",
    "/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/cms~~~CuQrLP",
    "/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app"
)'>
2025-02-19 07:26:10 +0000  codesign output: 
2025-02-19 07:26:10 +0000  Processing step: IDEDistributionZipODRItemStep
2025-02-19 07:26:10 +0000  Skipping step: IDEDistributionZipODRItemStep because it said so
2025-02-19 07:26:10 +0000  Skipping step: IDEDistributionZipODRItemStep because it said so
2025-02-19 07:26:10 +0000  Processing step: IDEDistributionCreateIPAStep
2025-02-19 07:26:10 +0000  Running /usr/bin/rsync '-8aPhhE' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Symbols' '--link-dest' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root'
2025-02-19 07:26:10 +0000  building file list ... 
2025-02-19 07:26:10 +0000   0 files...
2025-02-19 07:26:10 +0000  3 files to consider
2025-02-19 07:26:10 +0000  Symbols/
2025-02-19 07:26:10 +0000  
sent 213 bytes  received 26 bytes  478.00 bytes/sec
2025-02-19 07:26:10 +0000  total size is 21.53M  speedup is 94480.97
2025-02-19 07:26:10 +0000  /usr/bin/rsync exited with 0
2025-02-19 07:26:10 +0000  Running /usr/bin/ditto '-V' '-c' '-k' '--norsrc' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root' '/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Packages/TaxiRapport.ipa'
2025-02-19 07:26:10 +0000  >>> Copying /var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root 
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/_CodeSignature/CodeResources ... 
2025-02-19 07:26:10 +0000  49026 bytes for ./Payload/TaxiRapport.app/_CodeSignature/CodeResources
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/Fontisto.ttf ... 
2025-02-19 07:26:10 +0000  313528 bytes for ./Payload/TaxiRapport.app/Fontisto.ttf
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/Octicons.ttf ... 
2025-02-19 07:26:10 +0000  49404 bytes for ./Payload/TaxiRapport.app/Octicons.ttf
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/Feather.ttf ... 
2025-02-19 07:26:10 +0000  56228 bytes for ./Payload/TaxiRapport.app/Feather.ttf
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/FontAwesome6_Regular.ttf ... 
2025-02-19 07:26:10 +0000  63348 bytes for ./Payload/TaxiRapport.app/FontAwesome6_Regular.ttf
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/Entypo.ttf ... 
2025-02-19 07:26:10 +0000  66200 bytes for ./Payload/TaxiRapport.app/Entypo.ttf
copying file ./Payload/TaxiRapport.app/TaxiRapport ... 
2025-02-19 07:26:10 +0000  5182784 bytes for ./Payload/TaxiRapport.app/TaxiRapport
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/FontAwesome5_Brands.ttf ... 
2025-02-19 07:26:10 +0000  134040 bytes for ./Payload/TaxiRapport.app/FontAwesome5_Brands.ttf
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/MaterialCommunityIcons.ttf ... 
2025-02-19 07:26:10 +0000  1147844 bytes for ./Payload/TaxiRapport.app/MaterialCommunityIcons.ttf
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/AntDesign.ttf ... 
2025-02-19 07:26:10 +0000  70344 bytes for ./Payload/TaxiRapport.app/AntDesign.ttf
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/Foundation.ttf ... 
2025-02-19 07:26:10 +0000  56976 bytes for ./Payload/TaxiRapport.app/Foundation.ttf
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/Ionicons.ttf ... 
2025-02-19 07:26:10 +0000  442604 bytes for ./Payload/TaxiRapport.app/Ionicons.ttf
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/FontAwesome5_Solid.ttf ... 
2025-02-19 07:26:10 +0000  202744 bytes for ./Payload/TaxiRapport.app/FontAwesome5_Solid.ttf
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/FontAwesome5_Regular.ttf ... 
2025-02-19 07:26:10 +0000  33736 bytes for ./Payload/TaxiRapport.app/FontAwesome5_Regular.ttf
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/FontAwesome.ttf ... 
2025-02-19 07:26:10 +0000  165548 bytes for ./Payload/TaxiRapport.app/FontAwesome.ttf
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/Zocial.ttf ... 
2025-02-19 07:26:10 +0000  25788 bytes for ./Payload/TaxiRapport.app/Zocial.ttf
2025-02-19 07:26:10 +0000  copying file ./Payload/TaxiRapport.app/Assets.car ... 
2025-02-19 07:26:11 +0000  349272 bytes for ./Payload/TaxiRapport.app/Assets.car
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/EvilIcons.ttf ... 
2025-02-19 07:26:11 +0000  13456 bytes for ./Payload/TaxiRapport.app/EvilIcons.ttf
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/main.jsbundle ... 
2025-02-19 07:26:11 +0000  5229982 bytes for ./Payload/TaxiRapport.app/main.jsbundle
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/de.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/de.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1312 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/he.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/he.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1444 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ar.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ar.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1500 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/el.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/el.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1600 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1296 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ja.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ja.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1436 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/en.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/en.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1296 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/uk.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/uk.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1564 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/nb.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/nb.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1296 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1268 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/es.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/es.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1364 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/da.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/da.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1300 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/it.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/it.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1388 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/sk.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/sk.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1356 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1368 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ms.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ms.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1292 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/sv.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/sv.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1300 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/cs.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/cs.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1368 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ko.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ko.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1332 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1308 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/hu.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/hu.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1348 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/tr.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/tr.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1360 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/pl.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/pl.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1340 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/vi.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/vi.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1352 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ru.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ru.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1576 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/fr.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/fr.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1360 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/fi.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/fi.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1340 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/id.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/id.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1300 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/nl.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/nl.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1324 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/th.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/th.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1784 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/pt.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/pt.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1356 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zu.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zu.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  2076 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1372 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ro.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ro.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1344 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/Info.plist ... 
2025-02-19 07:26:11 +0000  777 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/Info.plist
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/hr.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/hr.lproj/Localizable.strings
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin ... 
2025-02-19 07:26:11 +0000  1352 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/hi.lproj/Localizable.strings ... 
2025-02-19 07:26:11 +0000  64 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/hi.lproj/Localizable.strings
copying file ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin ... 
1640 bytes for ./Payload/TaxiRapport.app/RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin
copying file ./Payload/TaxiRapport.app/FontAwesome6_Solid.ttf ... 
2025-02-19 07:26:11 +0000  394668 bytes for ./Payload/TaxiRapport.app/FontAwesome6_Solid.ttf
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/PrivacyInfo.xcprivacy ... 
2025-02-19 07:26:11 +0000  986 bytes for ./Payload/TaxiRapport.app/PrivacyInfo.xcprivacy
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib ... 
2025-02-19 07:26:11 +0000  3819 bytes for ./Payload/TaxiRapport.app/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib ... 
2025-02-19 07:26:11 +0000  924 bytes for ./Payload/TaxiRapport.app/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/LaunchScreen.storyboardc/Info.plist ... 
2025-02-19 07:26:11 +0000  258 bytes for ./Payload/TaxiRapport.app/LaunchScreen.storyboardc/Info.plist
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/Frameworks/hermes.framework/_CodeSignature/CodeResources ... 
2025-02-19 07:26:11 +0000  1798 bytes for ./Payload/TaxiRapport.app/Frameworks/hermes.framework/_CodeSignature/CodeResources
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/Frameworks/hermes.framework/hermes ... 
2025-02-19 07:26:11 +0000  4443200 bytes for ./Payload/TaxiRapport.app/Frameworks/hermes.framework/hermes
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/Frameworks/hermes.framework/Info.plist ... 
815 bytes for ./Payload/TaxiRapport.app/Frameworks/hermes.framework/Info.plist
copying file ./Payload/TaxiRapport.app/SimpleLineIcons.ttf ... 
2025-02-19 07:26:11 +0000  54056 bytes for ./Payload/TaxiRapport.app/SimpleLineIcons.ttf
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/FontAwesome6_Brands.ttf ... 
2025-02-19 07:26:11 +0000  189684 bytes for ./Payload/TaxiRapport.app/FontAwesome6_Brands.ttf
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/embedded.mobileprovision ... 
2025-02-19 07:26:11 +0000  12301 bytes for ./Payload/TaxiRapport.app/embedded.mobileprovision
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-ratings/dist/images/bell.png ... 
2025-02-19 07:26:11 +0000  2989 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-ratings/dist/images/bell.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-ratings/dist/images/airbnb-star-selected.png ... 
2025-02-19 07:26:11 +0000  934 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-ratings/dist/images/airbnb-star-selected.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-ratings/dist/images/heart.png ... 
2025-02-19 07:26:11 +0000  1928 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-ratings/dist/images/heart.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-ratings/dist/images/airbnb-star.png ... 
2025-02-19 07:26:11 +0000  930 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-ratings/dist/images/airbnb-star.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-ratings/dist/images/rocket.png ... 
2025-02-19 07:26:11 +0000  4050 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-ratings/dist/images/rocket.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-ratings/dist/images/star.png ... 
2025-02-19 07:26:11 +0000  1961 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-ratings/dist/images/star.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/@react-navigation/elements/src/assets/<EMAIL> ... 
2025-02-19 07:26:11 +0000  761 bytes for ./Payload/TaxiRapport.app/assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/@react-navigation/elements/src/assets/<EMAIL> ... 
2025-02-19 07:26:11 +0000  405 bytes for ./Payload/TaxiRapport.app/assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/@react-navigation/elements/src/assets/back-icon.png ... 
2025-02-19 07:26:11 +0000  290 bytes for ./Payload/TaxiRapport.app/assets/node_modules/@react-navigation/elements/src/assets/back-icon.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png ... 
2025-02-19 07:26:11 +0000  913 bytes for ./Payload/TaxiRapport.app/assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-element-dropdown/src/assets/down.png ... 
2025-02-19 07:26:11 +0000  186 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-element-dropdown/src/assets/down.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-element-dropdown/src/assets/close.png ... 
2025-02-19 07:26:11 +0000  208 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-element-dropdown/src/assets/close.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/calendar/img/<EMAIL> ... 
2025-02-19 07:26:11 +0000  458 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/calendar/img/<EMAIL>
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/calendar/img/<EMAIL> ... 
2025-02-19 07:26:11 +0000  462 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/calendar/img/<EMAIL>
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/img/<EMAIL> ... 
2025-02-19 07:26:11 +0000  508 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/img/<EMAIL>
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/img/<EMAIL> ... 
2025-02-19 07:26:11 +0000  354 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/img/<EMAIL>
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/img/up.png ... 
2025-02-19 07:26:11 +0000  215 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/img/up.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/img/down.png ... 
2025-02-19 07:26:11 +0000  208 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/img/down.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/img/<EMAIL> ... 
2025-02-19 07:26:11 +0000  347 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/img/<EMAIL>
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/img/<EMAIL> ... 
2025-02-19 07:26:11 +0000  504 bytes for ./Payload/TaxiRapport.app/assets/node_modules/react-native-calendars/src/img/<EMAIL>
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/default/defaultPerson.jpeg ... 
2025-02-19 07:26:11 +0000  1302 bytes for ./Payload/TaxiRapport.app/assets/src/assets/default/defaultPerson.jpeg
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/bgImg.png ... 
2025-02-19 07:26:11 +0000  20750 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/bgImg.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/Img5.png ... 
2025-02-19 07:26:11 +0000  7090 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/Img5.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/Img4.png ... 
2025-02-19 07:26:11 +0000  11724 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/Img4.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/logo.png ... 
2025-02-19 07:26:11 +0000  82500 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/logo.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/Img3.png ... 
2025-02-19 07:26:11 +0000  12016 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/Img3.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/Img2.png ... 
2025-02-19 07:26:11 +0000  9777 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/Img2.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/Menu/Harddrive.png ... 
2025-02-19 07:26:11 +0000  621 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/Menu/Harddrive.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/Menu/Mail.png ... 
2025-02-19 07:26:11 +0000  688 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/Menu/Mail.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/Menu/User.png ... 
2025-02-19 07:26:11 +0000  721 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/Menu/User.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/Menu/localtaxi.png ... 
2025-02-19 07:26:11 +0000  556 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/Menu/localtaxi.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/Menu/Upload.png ... 
2025-02-19 07:26:11 +0000  367 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/Menu/Upload.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/Menu/Phone.png ... 
2025-02-19 07:26:11 +0000  917 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/Menu/Phone.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/slide.png ... 
2025-02-19 07:26:11 +0000  429896 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/slide.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/techcreator.png ... 
2025-02-19 07:26:11 +0000  428959 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/techcreator.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/assets/src/assets/Image/TaxiBanner.png ... 
2025-02-19 07:26:11 +0000  531988 bytes for ./Payload/TaxiRapport.app/assets/src/assets/Image/TaxiBanner.png
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/Info.plist ... 
2025-02-19 07:26:11 +0000  1427 bytes for ./Payload/TaxiRapport.app/Info.plist
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy ... 
2025-02-19 07:26:11 +0000  512 bytes for ./Payload/TaxiRapport.app/RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/RNCAsyncStorage_resources.bundle/Info.plist ... 
2025-02-19 07:26:11 +0000  799 bytes for ./Payload/TaxiRapport.app/RNCAsyncStorage_resources.bundle/Info.plist
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/PkgInfo ... 
2025-02-19 07:26:11 +0000  8 bytes for ./Payload/TaxiRapport.app/PkgInfo
2025-02-19 07:26:11 +0000  copying file ./Payload/TaxiRapport.app/MaterialIcons.ttf ... 
2025-02-19 07:26:11 +0000  356840 bytes for ./Payload/TaxiRapport.app/MaterialIcons.ttf
2025-02-19 07:26:11 +0000  copying file ./Symbols/005AFA4A-F683-3AC6-8E49-EEEA0835634B.symbols ... 
2025-02-19 07:26:11 +0000  21484916 bytes for ./Symbols/005AFA4A-F683-3AC6-8E49-EEEA0835634B.symbols
2025-02-19 07:26:11 +0000  copying file ./Symbols/A56CDAAF-2027-33D2-A806-BA11E8C1B3EF.symbols ... 
2025-02-19 07:26:11 +0000  1096036 bytes for ./Symbols/A56CDAAF-2027-33D2-A806-BA11E8C1B3EF.symbols
2025-02-19 07:26:11 +0000  /usr/bin/ditto exited with 0
2025-02-19 07:26:11 +0000  Processing step: IDEDistributionAppStoreInformationStep
2025-02-19 07:26:11 +0000  Skipping step: IDEDistributionAppStoreInformationStep because it said so
2025-02-19 07:26:11 +0000  Processing step: IDEDistributionGenerateProcessedDistributionItems
2025-02-19 07:26:11 +0000  IDEDistributionItem init <DVTFilePath:0x60000f6b6380:'/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app/Frameworks/hermes.framework'>
2025-02-19 07:26:11 +0000  IDEDistributionItem init <DVTFilePath:0x60000340d180:'/var/folders/pl/f678phk51gb5v_8d1rbtlzfr0000gn/T/XcodeDistPipeline.~~~cN4Wdj/Root/Payload/TaxiRapport.app'>
2025-02-19 07:26:11 +0000  Processing step: IDEDistributionCreateManifestStep
2025-02-19 07:26:11 +0000  Skipping step: IDEDistributionCreateManifestStep because it said so
