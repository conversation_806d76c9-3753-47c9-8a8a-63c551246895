<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="1.2" xsi:schemaLocation="urn:oasis:names:tc:xliff:document:1.2 http://docs.oasis-open.org/xliff/v1.2/os/xliff-core-1.2-strict.xsd">
  <file original="TaxiRapport/en.lproj/InfoPlist.strings" datatype="plaintext" source-language="en" target-language="es">
    <header>
      <tool tool-id="com.apple.dt.xcode" tool-name="Xcode" tool-version="15.3" build-num="15E204a"/>
    </header>
    <body>
      <trans-unit id="CFBundleDisplayName" xml:space="preserve">
        <source>TaxiRapport</source>
        <note>Bundle display name</note>
      </trans-unit>
      <trans-unit id="CFBundleName" xml:space="preserve">
        <source>TaxiRapport</source>
        <note>Bundle name</note>
      </trans-unit>
      <trans-unit id="NSLocationWhenInUseUsageDescription" xml:space="preserve">
        <source/>
        <note>Privacy - Location When In Use Usage Description</note>
      </trans-unit>
    </body>
  </file>
</xliff>
