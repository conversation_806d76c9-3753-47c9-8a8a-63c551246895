#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes. Error detail: AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=75308, tid=68540
#
# JRE version:  (17.0.12+7) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.12+7, mixed mode, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\lombok\lombok-1.18.33.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.900.v20240613-2009.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.34.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\97c2628cb9069fc2c03a6e5dc60642b0\redhat.java\ss_ws --pipe=\\.\pipe\lsp-18d37aad3bb2c5b95f5b7fab35e24bbf-sock

Host: 12th Gen Intel(R) Core(TM) i5-1235U, 12 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.4717)
Time: Tue Sep 10 13:07:29 2024 Pakistan Standard Time elapsed time: 0.052875 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001cef36ef3a0):  JavaThread "Unknown thread" [_thread_in_vm, id=68540, stack(0x0000001030900000,0x0000001030a00000)]

Stack: [0x0000001030900000,0x0000001030a00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x67f929]
V  [jvm.dll+0x8371ba]
V  [jvm.dll+0x838c7e]
V  [jvm.dll+0x8392e3]
V  [jvm.dll+0x24834f]
V  [jvm.dll+0xa70d3]
V  [jvm.dll+0x6b2e9a]
V  [jvm.dll+0x6b3b8f]
V  [jvm.dll+0x68c0b3]
V  [jvm.dll+0x80916b]
V  [jvm.dll+0x36d434]
V  [jvm.dll+0x7e7425]
V  [jvm.dll+0x3f0edf]
V  [jvm.dll+0x3f2a31]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ff9eb186f18, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x000001cef3703690 GCTaskThread "GC Thread#0" [stack: 0x0000001030a00000,0x0000001030b00000] [id=56012]

=>0x000001cef36ef3a0 (exited) JavaThread "Unknown thread" [_thread_in_vm, id=68540, stack(0x0000001030900000,0x0000001030a00000)]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x0000000140000000, reserved size: 1073741824
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x140000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 7916M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 10

Heap:
 PSYoungGen      total 29696K, used 512K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 2% used [0x00000000eab00000,0x00000000eab80070,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 1149K, committed 1216K, reserved 1114112K
  class space    used 101K, committed 128K, reserved 1048576K

Card table byte_map: [0x000001cef7d50000,0x000001cef7f60000] _byte_map_base: 0x000001cef7750000

Marking Bits: (ParMarkBitMap*) 0x00007ff9eb1f58b0
 Begin Bits: [0x000001cef80c0000, 0x000001cef90c0000)
 End Bits:   [0x000001cef90c0000, 0x000001cefa0c0000)

Polling page: 0x000001cef37b0000

Metaspace:

Usage:
  Non-class:      1.02 MB used.
      Class:    101.05 KB used.
       Both:      1.12 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       1.06 MB (  2%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     128.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       1.19 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  12.00 MB
       Class:  15.75 MB
        Both:  27.75 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 17179869184.00 GB
CDS: off
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 2.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 19.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 3.
num_chunk_merges: 0.
num_chunk_splits: 2.
num_chunks_enlarged: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=0Kb max_used=0Kb free=120000Kb
 bounds [0x000001ce87ad0000, 0x000001ce87d40000, 0x000001ce8f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=0Kb max_used=0Kb free=120000Kb
 bounds [0x000001ce80000000, 0x000001ce80270000, 0x000001ce87530000]
CodeHeap 'non-nmethods': size=5760Kb used=202Kb max_used=353Kb free=5557Kb
 bounds [0x000001ce87530000, 0x000001ce877a0000, 0x000001ce87ad0000]
 total_blobs=66 nmethods=0 adapters=47
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (0 events):
No events

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.018 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (20 events):
Event: 0.044 Loading class java/lang/Long
Event: 0.044 Loading class java/lang/Long done
Event: 0.044 Loading class java/util/Iterator
Event: 0.044 Loading class java/util/Iterator done
Event: 0.044 Loading class java/lang/reflect/RecordComponent
Event: 0.044 Loading class java/lang/reflect/RecordComponent done
Event: 0.044 Loading class jdk/internal/vm/vector/VectorSupport
Event: 0.044 Loading class jdk/internal/vm/vector/VectorSupport done
Event: 0.044 Loading class jdk/internal/vm/vector/VectorSupport$VectorPayload
Event: 0.044 Loading class jdk/internal/vm/vector/VectorSupport$VectorPayload done
Event: 0.044 Loading class jdk/internal/vm/vector/VectorSupport$Vector
Event: 0.044 Loading class jdk/internal/vm/vector/VectorSupport$Vector done
Event: 0.044 Loading class jdk/internal/vm/vector/VectorSupport$VectorMask
Event: 0.044 Loading class jdk/internal/vm/vector/VectorSupport$VectorMask done
Event: 0.044 Loading class jdk/internal/vm/vector/VectorSupport$VectorShuffle
Event: 0.044 Loading class jdk/internal/vm/vector/VectorSupport$VectorShuffle done
Event: 0.049 Loading class java/lang/NullPointerException
Event: 0.049 Loading class java/lang/NullPointerException done
Event: 0.049 Loading class java/lang/ArithmeticException
Event: 0.049 Loading class java/lang/ArithmeticException done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff676320000 - 0x00007ff67632e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\java.exe
0x00007ffa60830000 - 0x00007ffa60a28000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa5f6a0000 - 0x00007ffa5f761000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa5e240000 - 0x00007ffa5e53d000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa5e5e0000 - 0x00007ffa5e6e0000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa390f0000 - 0x00007ffa3910b000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffa3ca00000 - 0x00007ffa3ca17000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\jli.dll
0x00007ffa3ac90000 - 0x00007ffa3af2a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16\COMCTL32.dll
0x00007ffa60280000 - 0x00007ffa6041d000 	C:\Windows\System32\USER32.dll
0x00007ffa5ee90000 - 0x00007ffa5ef2e000 	C:\Windows\System32\msvcrt.dll
0x00007ffa5f100000 - 0x00007ffa5f12b000 	C:\Windows\System32\GDI32.dll
0x00007ffa5e540000 - 0x00007ffa5e562000 	C:\Windows\System32\win32u.dll
0x00007ffa5df70000 - 0x00007ffa5e087000 	C:\Windows\System32\gdi32full.dll
0x00007ffa5e790000 - 0x00007ffa5e82d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa5f820000 - 0x00007ffa5f84f000 	C:\Windows\System32\IMM32.DLL
0x00007ffa536f0000 - 0x00007ffa536fc000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffa13810000 - 0x00007ffa1389d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\msvcp140.dll
0x00007ff9ea650000 - 0x00007ff9eb2ba000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\server\jvm.dll
0x00007ffa5f190000 - 0x00007ffa5f240000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa5e980000 - 0x00007ffa5ea20000 	C:\Windows\System32\sechost.dll
0x00007ffa60150000 - 0x00007ffa60273000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa5e830000 - 0x00007ffa5e857000 	C:\Windows\System32\bcrypt.dll
0x00007ffa60780000 - 0x00007ffa607eb000 	C:\Windows\System32\WS2_32.dll
0x00007ffa5dc10000 - 0x00007ffa5dc5b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffa4f290000 - 0x00007ffa4f2b7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa4f2d0000 - 0x00007ffa4f2da000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa5da40000 - 0x00007ffa5da52000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffa5c6b0000 - 0x00007ffa5c6c2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa51220000 - 0x00007ffa5122a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\jimage.dll
0x00007ffa5b960000 - 0x00007ffa5bb44000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa37390000 - 0x00007ffa373c4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa5dee0000 - 0x00007ffa5df62000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa55b50000 - 0x00007ffa55b5e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\instrument.dll
0x00007ffa32580000 - 0x00007ffa325a5000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\java.dll
0x00007ffa32010000 - 0x00007ffa32028000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\zip.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16;c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\lombok\lombok-1.18.33.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.900.v20240613-2009.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.34.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\97c2628cb9069fc2c03a6e5dc60642b0\redhat.java\ss_ws --pipe=\\.\pipe\lsp-18d37aad3bb2c5b95f5b7fab35e24bbf-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.900.v20240613-2009.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-17.0.10.7-hotspot\
PATH=C:\Program Files\Microsoft\jdk-17.0.10.7-hotspot\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Yarn\bin;%NVM_HOME%;%NVM_SYMLINK%;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs
USERNAME=PMYLS
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 154 Stepping 4, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.4717)
OS uptime: 4 days 2:28 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 154 stepping 4 microcode 0x424, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv
Processor Information for processor 0
  Max Mhz: 1300, Current Mhz: 1300, Mhz Limit: 1300
Processor Information for processor 1
  Max Mhz: 1300, Current Mhz: 1300, Mhz Limit: 1300
Processor Information for processor 2
  Max Mhz: 1300, Current Mhz: 1300, Mhz Limit: 1300
Processor Information for processor 3
  Max Mhz: 1300, Current Mhz: 1300, Mhz Limit: 1300
Processor Information for processor 4
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 5
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 6
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 7
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 8
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 9
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 10
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 11
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900

Memory: 4k page, system-wide physical 7916M (448M free)
TotalPageFile size 30861M (AvailPageFile size 12M)
current process WorkingSet (physical memory assigned to process): 15M, peak: 15M
current process commit charge ("private bytes"): 154M, peak: 155M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7) for windows-amd64 JRE (17.0.12+7), built on Jul 16 2024 22:08:24 by "admin" with MS VC++ 16.10 / 16.11 (VS2019)

END.
