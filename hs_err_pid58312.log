#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 131088 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=58312, tid=76484
#
# JRE version: OpenJDK Runtime Environment Temurin-17.0.12+7 (17.0.12+7) (build 17.0.12+7)
# Java VM: OpenJDK 64-Bit Server VM Temurin-17.0.12+7 (17.0.12+7, mixed mode, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\lombok\lombok-1.18.33.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\97c2628cb9069fc2c03a6e5dc60642b0\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.900.v20240613-2009.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.34.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\97c2628cb9069fc2c03a6e5dc60642b0\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-0a044a6e5b1799973af834e0c9f191d2-sock

Host: 12th Gen Intel(R) Core(TM) i5-1235U, 12 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.4717)
Time: Tue Sep 10 13:06:20 2024 Pakistan Standard Time elapsed time: 5.312657 seconds (0d 0h 0m 5s)

---------------  T H R E A D  ---------------

Current thread (0x000001e0359377b0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=76484, stack(0x0000008544b00000,0x0000008544c00000)]


Current CompileTask:
C2:   5312 3670       4       org.lombokweb.asm.ClassReader::readMethod (1066 bytes)

Stack: [0x0000008544b00000,0x0000008544c00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x67f929]
V  [jvm.dll+0x8371ba]
V  [jvm.dll+0x838c7e]
V  [jvm.dll+0x8392e3]
V  [jvm.dll+0x24834f]
V  [jvm.dll+0xac9d4]
V  [jvm.dll+0xad01c]
V  [jvm.dll+0xacadb]
V  [jvm.dll+0x65d05c]
V  [jvm.dll+0x75608d]
V  [jvm.dll+0x75528f]
V  [jvm.dll+0x59aa6b]
V  [jvm.dll+0x5870ea]
V  [jvm.dll+0x2231c2]
V  [jvm.dll+0x21c37f]
V  [jvm.dll+0x219be1]
V  [jvm.dll+0x1a58bd]
V  [jvm.dll+0x229a2d]
V  [jvm.dll+0x227bdc]
V  [jvm.dll+0x7ec1f7]
V  [jvm.dll+0x7e65dc]
V  [jvm.dll+0x67e7f7]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001e0380f1680, length=22, elements={
0x000001e01a7efc70, 0x000001e01a8a5380, 0x000001e0305d9bf0, 0x000001e01a8a8100,
0x000001e01a8a8e90, 0x000001e01a8a9c20, 0x000001e01a8ada10, 0x000001e0359377b0,
0x000001e03593dda0, 0x000001e03593ee50, 0x000001e01a8506e0, 0x000001e036f922a0,
0x000001e037412c30, 0x000001e0374aa030, 0x000001e0374aabe0, 0x000001e037584150,
0x000001e037526c60, 0x000001e03764e120, 0x000001e037b32420, 0x000001e037b30ad0,
0x000001e037b314f0, 0x000001e037b32e40
}

Java Threads: ( => current thread )
  0x000001e01a7efc70 JavaThread "main" [_thread_blocked, id=9732, stack(0x0000008544200000,0x0000008544300000)]
  0x000001e01a8a5380 JavaThread "Reference Handler" daemon [_thread_blocked, id=79732, stack(0x0000008544500000,0x0000008544600000)]
  0x000001e0305d9bf0 JavaThread "Finalizer" daemon [_thread_blocked, id=62144, stack(0x0000008544600000,0x0000008544700000)]
  0x000001e01a8a8100 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=77332, stack(0x0000008544700000,0x0000008544800000)]
  0x000001e01a8a8e90 JavaThread "Attach Listener" daemon [_thread_blocked, id=78092, stack(0x0000008544800000,0x0000008544900000)]
  0x000001e01a8a9c20 JavaThread "Service Thread" daemon [_thread_blocked, id=39544, stack(0x0000008544900000,0x0000008544a00000)]
  0x000001e01a8ada10 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=48364, stack(0x0000008544a00000,0x0000008544b00000)]
=>0x000001e0359377b0 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=76484, stack(0x0000008544b00000,0x0000008544c00000)]
  0x000001e03593dda0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=68528, stack(0x0000008544c00000,0x0000008544d00000)]
  0x000001e03593ee50 JavaThread "Sweeper thread" daemon [_thread_blocked, id=73512, stack(0x0000008544d00000,0x0000008544e00000)]
  0x000001e01a8506e0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=48284, stack(0x0000008544e00000,0x0000008544f00000)]
  0x000001e036f922a0 JavaThread "Notification Thread" daemon [_thread_blocked, id=71772, stack(0x0000008544f00000,0x0000008545000000)]
  0x000001e037412c30 JavaThread "Active Thread: Equinox Container: 7d895f85-c0af-482e-8cd8-7fce8707c9e9" [_thread_blocked, id=66904, stack(0x0000008545700000,0x0000008545800000)]
  0x000001e0374aa030 JavaThread "Framework Event Dispatcher: Equinox Container: 7d895f85-c0af-482e-8cd8-7fce8707c9e9" daemon [_thread_blocked, id=76320, stack(0x0000008545800000,0x0000008545900000)]
  0x000001e0374aabe0 JavaThread "Start Level: Equinox Container: 7d895f85-c0af-482e-8cd8-7fce8707c9e9" daemon [_thread_in_Java, id=79008, stack(0x0000008545900000,0x0000008545a00000)]
  0x000001e037584150 JavaThread "Bundle File Closer" daemon [_thread_blocked, id=75360, stack(0x0000008545b00000,0x0000008545c00000)]
  0x000001e037526c60 JavaThread "SCR Component Actor" daemon [_thread_blocked, id=76844, stack(0x0000008545d00000,0x0000008545e00000)]
  0x000001e03764e120 JavaThread "SCR Component Registry" daemon [_thread_blocked, id=76200, stack(0x0000008545e00000,0x0000008545f00000)]
  0x000001e037b32420 JavaThread "Worker-JM" [_thread_blocked, id=63272, stack(0x0000008545f00000,0x0000008546000000)]
  0x000001e037b30ad0 JavaThread "JNA Cleaner" daemon [_thread_blocked, id=67984, stack(0x0000008546100000,0x0000008546200000)]
  0x000001e037b314f0 JavaThread "Worker-0" [_thread_blocked, id=69556, stack(0x0000008546200000,0x0000008546300000)]
  0x000001e037b32e40 JavaThread "Worker-1" [_thread_blocked, id=78456, stack(0x0000008546300000,0x0000008546400000)]

Other Threads:
  0x000001e01a878770 VMThread "VM Thread" [stack: 0x0000008544400000,0x0000008544500000] [id=66040]
  0x000001e035c47500 WatcherThread [stack: 0x0000008545000000,0x0000008545100000] [id=44308]
  0x000001e01a803ad0 GCTaskThread "GC Thread#0" [stack: 0x0000008544300000,0x0000008544400000] [id=75456]
  0x000001e037438d90 GCTaskThread "GC Thread#1" [stack: 0x0000008545100000,0x0000008545200000] [id=73520]
  0x000001e0373648c0 GCTaskThread "GC Thread#2" [stack: 0x0000008545200000,0x0000008545300000] [id=77204]
  0x000001e037428340 GCTaskThread "GC Thread#3" [stack: 0x0000008545300000,0x0000008545400000] [id=72048]
  0x000001e037428600 GCTaskThread "GC Thread#4" [stack: 0x0000008545400000,0x0000008545500000] [id=79684]
  0x000001e03744b410 GCTaskThread "GC Thread#5" [stack: 0x0000008545500000,0x0000008545600000] [id=60028]
  0x000001e03744b6d0 GCTaskThread "GC Thread#6" [stack: 0x0000008545600000,0x0000008545700000] [id=75648]
  0x000001e03759f450 GCTaskThread "GC Thread#7" [stack: 0x0000008545a00000,0x0000008545b00000] [id=79580]
  0x000001e03752a1d0 GCTaskThread "GC Thread#8" [stack: 0x0000008545c00000,0x0000008545d00000] [id=76440]
  0x000001e037525230 GCTaskThread "GC Thread#9" [stack: 0x0000008546000000,0x0000008546100000] [id=77980]

Threads with active compile tasks:
C2 CompilerThread0     5334 3670       4       org.lombokweb.asm.ClassReader::readMethod (1066 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x0000000140000000, reserved size: 1073741824
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x140000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 7916M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 10

Heap:
 PSYoungGen      total 22016K, used 11407K [0x00000000eab00000, 0x00000000ec600000, 0x0000000100000000)
  eden space 19968K, 48% used [0x00000000eab00000,0x00000000eb4826d0,0x00000000ebe80000)
  from space 2048K, 81% used [0x00000000ec400000,0x00000000ec5a1898,0x00000000ec600000)
  to   space 3072K, 0% used [0x00000000ec000000,0x00000000ec000000,0x00000000ec300000)
 ParOldGen       total 68608K, used 22916K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 33% used [0x00000000c0000000,0x00000000c1661218,0x00000000c4300000)
 Metaspace       used 31961K, committed 32640K, reserved 1114112K
  class space    used 3154K, committed 3456K, reserved 1048576K

Card table byte_map: [0x000001e02de50000,0x000001e02e060000] _byte_map_base: 0x000001e02d850000

Marking Bits: (ParMarkBitMap*) 0x00007ff9eb1f58b0
 Begin Bits: [0x000001e02e1c0000, 0x000001e02f1c0000)
 End Bits:   [0x000001e02f1c0000, 0x000001e0301c0000)

Polling page: 0x000001e01a8b0000

Metaspace:

Usage:
  Non-class:     28.13 MB used.
      Class:      3.08 MB used.
       Both:     31.21 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      28.50 MB ( 45%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       3.38 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      31.88 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  2.64 MB
       Class:  12.59 MB
        Both:  15.23 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.00 MB
CDS: off
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 356.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 510.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 17.
num_chunks_taken_from_freelist: 1294.
num_chunk_merges: 8.
num_chunk_splits: 850.
num_chunks_enlarged: 597.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1639Kb max_used=1639Kb free=118360Kb
 bounds [0x000001e026730000, 0x000001e0269a0000, 0x000001e02dc60000]
CodeHeap 'profiled nmethods': size=120000Kb used=7680Kb max_used=7680Kb free=112319Kb
 bounds [0x000001e01ec60000, 0x000001e01f3f0000, 0x000001e026190000]
CodeHeap 'non-nmethods': size=5760Kb used=1244Kb max_used=1264Kb free=4515Kb
 bounds [0x000001e026190000, 0x000001e026400000, 0x000001e026730000]
 total_blobs=4242 nmethods=3679 adapters=476
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 5.124 Thread 0x000001e03593dda0 nmethod 3666 0x000001e01f3d5790 code [0x000001e01f3d59c0, 0x000001e01f3d61c8]
Event: 5.124 Thread 0x000001e03593dda0 3667       3       org.lombokweb.asm.SymbolTable::addConstantMethodref (24 bytes)
Event: 5.124 Thread 0x000001e03593dda0 nmethod 3667 0x000001e01f3d6510 code [0x000001e01f3d66c0, 0x000001e01f3d6868]
Event: 5.130 Thread 0x000001e0359377b0 3668   !   4       org.eclipse.osgi.storage.bundlefile.CloseableBundleFile::getEntry (28 bytes)
Event: 5.157 Thread 0x000001e03593dda0 3669       3       org.lombokweb.asm.ClassReader::readLong (29 bytes)
Event: 5.157 Thread 0x000001e03593dda0 nmethod 3669 0x000001e01f3d6910 code [0x000001e01f3d6ac0, 0x000001e01f3d6c78]
Event: 5.219 Thread 0x000001e0359377b0 nmethod 3668 0x000001e0268c5310 code [0x000001e0268c5660, 0x000001e0268c7c58]
Event: 5.219 Thread 0x000001e0359377b0 3670       4       org.lombokweb.asm.ClassReader::readMethod (1066 bytes)
Event: 5.252 Thread 0x000001e03593dda0 3671       3       java.lang.reflect.Modifier::isAbstract (14 bytes)
Event: 5.252 Thread 0x000001e03593dda0 nmethod 3671 0x000001e01f3d6d90 code [0x000001e01f3d6f20, 0x000001e01f3d7078]
Event: 5.275 Thread 0x000001e03593dda0 3672       3       org.lombokweb.asm.SymbolTable::addConstantIntegerOrFloat (100 bytes)
Event: 5.276 Thread 0x000001e03593dda0 nmethod 3672 0x000001e01f3d7110 code [0x000001e01f3d7320, 0x000001e01f3d7878]
Event: 5.276 Thread 0x000001e03593dda0 3673       3       lombok.patcher.scripts.AddFieldScript$1::visitField (35 bytes)
Event: 5.276 Thread 0x000001e03593dda0 nmethod 3673 0x000001e01f3d7b10 code [0x000001e01f3d7cc0, 0x000001e01f3d7f38]
Event: 5.277 Thread 0x000001e03593dda0 3675 %     3       org.lombokweb.asm.ClassReader::<init> @ 110 (371 bytes)
Event: 5.279 Thread 0x000001e03593dda0 nmethod 3675% 0x000001e01f3d8090 code [0x000001e01f3d8380, 0x000001e01f3d96d8]
Event: 5.279 Thread 0x000001e03593dda0 3676 %     3       org.lombokweb.asm.SymbolTable::<init> @ 98 (536 bytes)
Event: 5.282 Thread 0x000001e03593dda0 nmethod 3676% 0x000001e01f3d9c90 code [0x000001e01f3da240, 0x000001e01f3dce48]
Event: 5.282 Thread 0x000001e03593dda0 3677   !   3       java.util.regex.Pattern::matcher (44 bytes)
Event: 5.282 Thread 0x000001e03593dda0 nmethod 3677 0x000001e01f3de010 code [0x000001e01f3de1c0, 0x000001e01f3de518]

GC Heap History (20 events):
Event: 1.661 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 28724K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 76% used [0x00000000ec400000,0x00000000ec70d2f0,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 14543K, committed 14784K, reserved 1114112K
  class space    used 1455K, committed 1600K, reserved 1048576K
}
Event: 1.666 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4083K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfcfa0,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 1488K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 2% used [0x00000000c0000000,0x00000000c0174360,0x00000000c4300000)
 Metaspace       used 14543K, committed 14784K, reserved 1114112K
  class space    used 1455K, committed 1600K, reserved 1048576K
}
Event: 2.254 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 29683K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfcfa0,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 1488K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 2% used [0x00000000c0000000,0x00000000c0174360,0x00000000c4300000)
 Metaspace       used 16037K, committed 16320K, reserved 1114112K
  class space    used 1614K, committed 1728K, reserved 1048576K
}
Event: 2.261 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 4091K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fef98,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 5242K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 7% used [0x00000000c0000000,0x00000000c051eba0,0x00000000c4300000)
 Metaspace       used 16037K, committed 16320K, reserved 1114112K
  class space    used 1614K, committed 1728K, reserved 1048576K
}
Event: 2.634 GC heap before
{Heap before GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 29691K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fef98,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 5242K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 7% used [0x00000000c0000000,0x00000000c051eba0,0x00000000c4300000)
 Metaspace       used 20596K, committed 21056K, reserved 1114112K
  class space    used 2090K, committed 2304K, reserved 1048576K
}
Event: 2.637 GC heap after
{Heap after GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 4076K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfb1e0,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 6287K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 9% used [0x00000000c0000000,0x00000000c0623c10,0x00000000c4300000)
 Metaspace       used 20596K, committed 21056K, reserved 1114112K
  class space    used 2090K, committed 2304K, reserved 1048576K
}
Event: 2.693 GC heap before
{Heap before GC invocations=5 (full 0):
 PSYoungGen      total 29696K, used 8343K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 16% used [0x00000000eab00000,0x00000000eaf2ac78,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfb1e0,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 6287K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 9% used [0x00000000c0000000,0x00000000c0623c10,0x00000000c4300000)
 Metaspace       used 21102K, committed 21504K, reserved 1114112K
  class space    used 2145K, committed 2304K, reserved 1048576K
}
Event: 2.696 GC heap after
{Heap after GC invocations=5 (full 0):
 PSYoungGen      total 29696K, used 4088K [0x00000000eab00000, 0x00000000ece00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fe238,0x00000000ec800000)
  to   space 5120K, 0% used [0x00000000ec900000,0x00000000ec900000,0x00000000ece00000)
 ParOldGen       total 68608K, used 6703K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 9% used [0x00000000c0000000,0x00000000c068be28,0x00000000c4300000)
 Metaspace       used 21102K, committed 21504K, reserved 1114112K
  class space    used 2145K, committed 2304K, reserved 1048576K
}
Event: 2.696 GC heap before
{Heap before GC invocations=6 (full 1):
 PSYoungGen      total 29696K, used 4088K [0x00000000eab00000, 0x00000000ece00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fe238,0x00000000ec800000)
  to   space 5120K, 0% used [0x00000000ec900000,0x00000000ec900000,0x00000000ece00000)
 ParOldGen       total 68608K, used 6703K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 9% used [0x00000000c0000000,0x00000000c068be28,0x00000000c4300000)
 Metaspace       used 21102K, committed 21504K, reserved 1114112K
  class space    used 2145K, committed 2304K, reserved 1048576K
}
Event: 2.715 GC heap after
{Heap after GC invocations=6 (full 1):
 PSYoungGen      total 29696K, used 0K [0x00000000eab00000, 0x00000000ece00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
  to   space 5120K, 0% used [0x00000000ec900000,0x00000000ec900000,0x00000000ece00000)
 ParOldGen       total 68608K, used 10548K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 15% used [0x00000000c0000000,0x00000000c0a4d3d8,0x00000000c4300000)
 Metaspace       used 21089K, committed 21504K, reserved 1114112K
  class space    used 2142K, committed 2304K, reserved 1048576K
}
Event: 2.885 GC heap before
{Heap before GC invocations=7 (full 1):
 PSYoungGen      total 29696K, used 25600K [0x00000000eab00000, 0x00000000ece00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
  to   space 5120K, 0% used [0x00000000ec900000,0x00000000ec900000,0x00000000ece00000)
 ParOldGen       total 68608K, used 10548K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 15% used [0x00000000c0000000,0x00000000c0a4d3d8,0x00000000c4300000)
 Metaspace       used 21874K, committed 22400K, reserved 1114112K
  class space    used 2224K, committed 2432K, reserved 1048576K
}
Event: 2.893 GC heap after
{Heap after GC invocations=7 (full 1):
 PSYoungGen      total 25088K, used 5120K [0x00000000eab00000, 0x00000000ed880000, 0x0000000100000000)
  eden space 19968K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebe80000)
  from space 5120K, 100% used [0x00000000ec900000,0x00000000ece00000,0x00000000ece00000)
  to   space 10752K, 0% used [0x00000000ebe80000,0x00000000ebe80000,0x00000000ec900000)
 ParOldGen       total 68608K, used 15204K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 22% used [0x00000000c0000000,0x00000000c0ed93d8,0x00000000c4300000)
 Metaspace       used 21874K, committed 22400K, reserved 1114112K
  class space    used 2224K, committed 2432K, reserved 1048576K
}
Event: 4.299 GC heap before
{Heap before GC invocations=8 (full 1):
 PSYoungGen      total 25088K, used 25061K [0x00000000eab00000, 0x00000000ed880000, 0x0000000100000000)
  eden space 19968K, 99% used [0x00000000eab00000,0x00000000ebe795a8,0x00000000ebe80000)
  from space 5120K, 100% used [0x00000000ec900000,0x00000000ece00000,0x00000000ece00000)
  to   space 10752K, 0% used [0x00000000ebe80000,0x00000000ebe80000,0x00000000ec900000)
 ParOldGen       total 68608K, used 15204K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 22% used [0x00000000c0000000,0x00000000c0ed93d8,0x00000000c4300000)
 Metaspace       used 23867K, committed 24384K, reserved 1114112K
  class space    used 2432K, committed 2624K, reserved 1048576K
}
Event: 4.303 GC heap after
{Heap after GC invocations=8 (full 1):
 PSYoungGen      total 27136K, used 6992K [0x00000000eab00000, 0x00000000ece80000, 0x0000000100000000)
  eden space 19968K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebe80000)
  from space 7168K, 97% used [0x00000000ebe80000,0x00000000ec5540c8,0x00000000ec580000)
  to   space 8192K, 0% used [0x00000000ec680000,0x00000000ec680000,0x00000000ece80000)
 ParOldGen       total 68608K, used 15212K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 22% used [0x00000000c0000000,0x00000000c0edb3d8,0x00000000c4300000)
 Metaspace       used 23867K, committed 24384K, reserved 1114112K
  class space    used 2432K, committed 2624K, reserved 1048576K
}
Event: 4.587 GC heap before
{Heap before GC invocations=9 (full 1):
 PSYoungGen      total 27136K, used 26960K [0x00000000eab00000, 0x00000000ece80000, 0x0000000100000000)
  eden space 19968K, 100% used [0x00000000eab00000,0x00000000ebe80000,0x00000000ebe80000)
  from space 7168K, 97% used [0x00000000ebe80000,0x00000000ec5540c8,0x00000000ec580000)
  to   space 8192K, 0% used [0x00000000ec680000,0x00000000ec680000,0x00000000ece80000)
 ParOldGen       total 68608K, used 15212K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 22% used [0x00000000c0000000,0x00000000c0edb3d8,0x00000000c4300000)
 Metaspace       used 25697K, committed 26304K, reserved 1114112K
  class space    used 2620K, committed 2880K, reserved 1048576K
}
Event: 4.592 GC heap after
{Heap after GC invocations=9 (full 1):
 PSYoungGen      total 28160K, used 7857K [0x00000000eab00000, 0x00000000ece80000, 0x0000000100000000)
  eden space 19968K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebe80000)
  from space 8192K, 95% used [0x00000000ec680000,0x00000000ece2c650,0x00000000ece80000)
  to   space 8192K, 0% used [0x00000000ebe80000,0x00000000ebe80000,0x00000000ec680000)
 ParOldGen       total 68608K, used 15220K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 22% used [0x00000000c0000000,0x00000000c0edd3d8,0x00000000c4300000)
 Metaspace       used 25697K, committed 26304K, reserved 1114112K
  class space    used 2620K, committed 2880K, reserved 1048576K
}
Event: 4.902 GC heap before
{Heap before GC invocations=10 (full 1):
 PSYoungGen      total 28160K, used 27825K [0x00000000eab00000, 0x00000000ece80000, 0x0000000100000000)
  eden space 19968K, 100% used [0x00000000eab00000,0x00000000ebe80000,0x00000000ebe80000)
  from space 8192K, 95% used [0x00000000ec680000,0x00000000ece2c650,0x00000000ece80000)
  to   space 8192K, 0% used [0x00000000ebe80000,0x00000000ebe80000,0x00000000ec680000)
 ParOldGen       total 68608K, used 15220K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 22% used [0x00000000c0000000,0x00000000c0edd3d8,0x00000000c4300000)
 Metaspace       used 28679K, committed 29376K, reserved 1114112K
  class space    used 2919K, committed 3200K, reserved 1048576K
}
Event: 4.908 GC heap after
{Heap after GC invocations=10 (full 1):
 PSYoungGen      total 24576K, used 4150K [0x00000000eab00000, 0x00000000ec980000, 0x0000000100000000)
  eden space 19968K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebe80000)
  from space 4608K, 90% used [0x00000000ebe80000,0x00000000ec28dbd8,0x00000000ec300000)
  to   space 5632K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec980000)
 ParOldGen       total 68608K, used 20388K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 29% used [0x00000000c0000000,0x00000000c13e93d8,0x00000000c4300000)
 Metaspace       used 28679K, committed 29376K, reserved 1114112K
  class space    used 2919K, committed 3200K, reserved 1048576K
}
Event: 5.103 GC heap before
{Heap before GC invocations=11 (full 1):
 PSYoungGen      total 24576K, used 24118K [0x00000000eab00000, 0x00000000ec980000, 0x0000000100000000)
  eden space 19968K, 100% used [0x00000000eab00000,0x00000000ebe80000,0x00000000ebe80000)
  from space 4608K, 90% used [0x00000000ebe80000,0x00000000ec28dbd8,0x00000000ec300000)
  to   space 5632K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec980000)
 ParOldGen       total 68608K, used 20388K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 29% used [0x00000000c0000000,0x00000000c13e93d8,0x00000000c4300000)
 Metaspace       used 30571K, committed 31296K, reserved 1114112K
  class space    used 3024K, committed 3328K, reserved 1048576K
}
Event: 5.107 GC heap after
{Heap after GC invocations=11 (full 1):
 PSYoungGen      total 22016K, used 1670K [0x00000000eab00000, 0x00000000ec600000, 0x0000000100000000)
  eden space 19968K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebe80000)
  from space 2048K, 81% used [0x00000000ec400000,0x00000000ec5a1898,0x00000000ec600000)
  to   space 3072K, 0% used [0x00000000ec000000,0x00000000ec000000,0x00000000ec300000)
 ParOldGen       total 68608K, used 22916K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 33% used [0x00000000c0000000,0x00000000c1661218,0x00000000c4300000)
 Metaspace       used 30571K, committed 31296K, reserved 1114112K
  class space    used 3024K, committed 3328K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.011 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\java.dll
Event: 0.359 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\zip.dll
Event: 0.367 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\instrument.dll
Event: 0.396 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\net.dll
Event: 0.401 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\nio.dll
Event: 0.417 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\zip.dll
Event: 0.453 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\jimage.dll
Event: 0.546 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\verify.dll
Event: 1.612 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.34.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1100.v20240722-2106\eclipse_11904.dll
Event: 3.783 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-76263555\jna1353000902942624307.dll

Deoptimization events (20 events):
Event: 4.632 Thread 0x000001e0374aabe0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001e0267755a8 relative=0x0000000000000308
Event: 4.632 Thread 0x000001e0374aabe0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e0267755a8 method=java.lang.AbstractStringBuilder.isLatin1()Z @ 10 c2
Event: 4.632 Thread 0x000001e0374aabe0 DEOPT PACKING pc=0x000001e0267755a8 sp=0x00000085459f2a70
Event: 4.632 Thread 0x000001e0374aabe0 DEOPT UNPACKING pc=0x000001e0261e66a3 sp=0x00000085459f2a00 mode 2
Event: 4.632 Thread 0x000001e0374aabe0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001e0267b855c relative=0x000000000000005c
Event: 4.632 Thread 0x000001e0374aabe0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e0267b855c method=java.lang.AbstractStringBuilder.isLatin1()Z @ 10 c2
Event: 4.632 Thread 0x000001e0374aabe0 DEOPT PACKING pc=0x000001e0267b855c sp=0x00000085459f2a60
Event: 4.632 Thread 0x000001e0374aabe0 DEOPT UNPACKING pc=0x000001e0261e66a3 sp=0x00000085459f29f0 mode 2
Event: 4.635 Thread 0x000001e0374aabe0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001e026752b38 relative=0x0000000000000038
Event: 4.635 Thread 0x000001e0374aabe0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e026752b38 method=java.lang.StringLatin1.canEncode(I)Z @ 4 c2
Event: 4.635 Thread 0x000001e0374aabe0 DEOPT PACKING pc=0x000001e026752b38 sp=0x00000085459f2a00
Event: 4.635 Thread 0x000001e0374aabe0 DEOPT UNPACKING pc=0x000001e0261e66a3 sp=0x00000085459f2990 mode 2
Event: 4.682 Thread 0x000001e0374aabe0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001e0268797f4 relative=0x00000000000000d4
Event: 4.682 Thread 0x000001e0374aabe0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e0268797f4 method=org.eclipse.core.runtime.Path.canonicalize(Z[Ljava/lang/String;)[Ljava/lang/String; @ 24 c2
Event: 4.682 Thread 0x000001e0374aabe0 DEOPT PACKING pc=0x000001e0268797f4 sp=0x00000085459f4ae0
Event: 4.682 Thread 0x000001e0374aabe0 DEOPT UNPACKING pc=0x000001e0261e66a3 sp=0x00000085459f4a68 mode 2
Event: 4.711 Thread 0x000001e0374aabe0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001e026801bf0 relative=0x0000000000000dd0
Event: 4.711 Thread 0x000001e0374aabe0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001e026801bf0 method=java.util.Properties$LineReader.readLine()I @ 479 c2
Event: 4.711 Thread 0x000001e0374aabe0 DEOPT PACKING pc=0x000001e026801bf0 sp=0x00000085459f45d0
Event: 4.711 Thread 0x000001e0374aabe0 DEOPT UNPACKING pc=0x000001e0261e66a3 sp=0x00000085459f4560 mode 2

Classes loaded (20 events):
Event: 4.856 Loading class java/util/zip/GZIPOutputStream
Event: 4.856 Loading class java/util/zip/GZIPOutputStream done
Event: 4.857 Loading class java/util/zip/GZIPInputStream
Event: 4.857 Loading class java/util/zip/GZIPInputStream done
Event: 4.861 Loading class java/net/SocketTimeoutException
Event: 4.861 Loading class java/io/InterruptedIOException
Event: 4.861 Loading class java/io/InterruptedIOException done
Event: 4.861 Loading class java/net/SocketTimeoutException done
Event: 4.861 Loading class java/net/SocketException
Event: 4.861 Loading class java/net/SocketException done
Event: 4.862 Loading class java/net/UnknownHostException
Event: 4.862 Loading class java/net/UnknownHostException done
Event: 4.862 Loading class java/net/ProtocolException
Event: 4.862 Loading class java/net/ProtocolException done
Event: 4.891 Loading class java/lang/ThreadLocal$SuppliedThreadLocal
Event: 4.891 Loading class java/lang/ThreadLocal$SuppliedThreadLocal done
Event: 4.900 Loading class java/io/FileWriter
Event: 4.901 Loading class java/io/FileWriter done
Event: 4.981 Loading class java/lang/NegativeArraySizeException
Event: 4.981 Loading class java/lang/NegativeArraySizeException done

Classes unloaded (7 events):
Event: 2.700 Thread 0x000001e01a878770 Unloading class 0x000000010024c400 'java/lang/invoke/LambdaForm$MH+0x000000010024c400'
Event: 2.700 Thread 0x000001e01a878770 Unloading class 0x000000010024c000 'java/lang/invoke/LambdaForm$MH+0x000000010024c000'
Event: 2.700 Thread 0x000001e01a878770 Unloading class 0x000000010024bc00 'java/lang/invoke/LambdaForm$MH+0x000000010024bc00'
Event: 2.700 Thread 0x000001e01a878770 Unloading class 0x000000010024b800 'java/lang/invoke/LambdaForm$MH+0x000000010024b800'
Event: 2.700 Thread 0x000001e01a878770 Unloading class 0x000000010024b400 'java/lang/invoke/LambdaForm$BMH+0x000000010024b400'
Event: 2.700 Thread 0x000001e01a878770 Unloading class 0x000000010024b000 'java/lang/invoke/LambdaForm$DMH+0x000000010024b000'
Event: 2.700 Thread 0x000001e01a878770 Unloading class 0x0000000100248400 'java/lang/invoke/LambdaForm$DMH+0x0000000100248400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 3.135 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb065038}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int, int, int, int)'> (0x00000000eb065038) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.919 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb59ea10}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000000eb59ea10) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.920 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb5a6bc8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x00000000eb5a6bc8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.920 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb5ab168}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x00000000eb5ab168) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.920 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb5aefe0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000000eb5aefe0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.939 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb62fc30}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eb62fc30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.956 Thread 0x000001e0374aabe0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eb6e5ec0}: Found class java.lang.Object, but interface was expected> (0x00000000eb6e5ec0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 826]
Event: 4.236 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebcfd830}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000ebcfd830) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.238 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebd05f28}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000ebd05f28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.241 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebd0a380}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000ebd0a380) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.245 Thread 0x000001e037b314f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb5eda48}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000eb5eda48) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.518 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb74e670}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, long, java.lang.Object, java.lang.Object)'> (0x00000000eb74e670) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.599 Thread 0x000001e0374aabe0 Exception <a 'java/io/FileNotFoundException'{0x00000000eab686f0}> (0x00000000eab686f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 4.600 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eab71768}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000eab71768) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.687 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb348d28}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000eb348d28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.806 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb9c5818}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000eb9c5818) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.807 Thread 0x000001e0374aabe0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb9c9130}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000eb9c9130) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.985 Thread 0x000001e0374aabe0 Exception <a 'java/io/FileNotFoundException'{0x00000000eb760b38}> (0x00000000eb760b38) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 4.985 Thread 0x000001e0374aabe0 Exception <a 'java/io/FileNotFoundException'{0x00000000eb761950}> (0x00000000eb761950) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 4.985 Thread 0x000001e0374aabe0 Exception <a 'java/io/FileNotFoundException'{0x00000000eb7628b0}> (0x00000000eb7628b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]

VM Operations (20 events):
Event: 3.116 Executing VM operation: HandshakeAllThreads
Event: 3.116 Executing VM operation: HandshakeAllThreads done
Event: 3.801 Executing VM operation: HandshakeAllThreads
Event: 3.801 Executing VM operation: HandshakeAllThreads done
Event: 4.299 Executing VM operation: ParallelGCFailedAllocation
Event: 4.303 Executing VM operation: ParallelGCFailedAllocation done
Event: 4.354 Executing VM operation: HandshakeAllThreads
Event: 4.354 Executing VM operation: HandshakeAllThreads done
Event: 4.392 Executing VM operation: HandshakeAllThreads
Event: 4.392 Executing VM operation: HandshakeAllThreads done
Event: 4.399 Executing VM operation: HandshakeAllThreads
Event: 4.399 Executing VM operation: HandshakeAllThreads done
Event: 4.587 Executing VM operation: ParallelGCFailedAllocation
Event: 4.592 Executing VM operation: ParallelGCFailedAllocation done
Event: 4.746 Executing VM operation: HandshakeAllThreads
Event: 4.746 Executing VM operation: HandshakeAllThreads done
Event: 4.902 Executing VM operation: ParallelGCFailedAllocation
Event: 4.908 Executing VM operation: ParallelGCFailedAllocation done
Event: 5.103 Executing VM operation: ParallelGCFailedAllocation
Event: 5.107 Executing VM operation: ParallelGCFailedAllocation done

Events (20 events):
Event: 0.088 Thread 0x000001e0305d9bf0 Thread added: 0x000001e0305d9bf0
Event: 0.138 Thread 0x000001e01a8a8100 Thread added: 0x000001e01a8a8100
Event: 0.138 Thread 0x000001e01a8a8e90 Thread added: 0x000001e01a8a8e90
Event: 0.139 Thread 0x000001e01a8a9c20 Thread added: 0x000001e01a8a9c20
Event: 0.139 Thread 0x000001e01a8ada10 Thread added: 0x000001e01a8ada10
Event: 0.140 Thread 0x000001e0359377b0 Thread added: 0x000001e0359377b0
Event: 0.141 Thread 0x000001e03593dda0 Thread added: 0x000001e03593dda0
Event: 0.141 Thread 0x000001e03593ee50 Thread added: 0x000001e03593ee50
Event: 0.274 Thread 0x000001e01a8506e0 Thread added: 0x000001e01a8506e0
Event: 0.692 Thread 0x000001e036f922a0 Thread added: 0x000001e036f922a0
Event: 1.480 Thread 0x000001e037412c30 Thread added: 0x000001e037412c30
Event: 1.603 Thread 0x000001e0374aa030 Thread added: 0x000001e0374aa030
Event: 1.606 Thread 0x000001e0374aabe0 Thread added: 0x000001e0374aabe0
Event: 1.983 Thread 0x000001e037584150 Thread added: 0x000001e037584150
Event: 2.269 Thread 0x000001e037526c60 Thread added: 0x000001e037526c60
Event: 2.356 Thread 0x000001e03764e120 Thread added: 0x000001e03764e120
Event: 2.604 Thread 0x000001e037b32420 Thread added: 0x000001e037b32420
Event: 3.830 Thread 0x000001e037b30ad0 Thread added: 0x000001e037b30ad0
Event: 3.928 Thread 0x000001e037b314f0 Thread added: 0x000001e037b314f0
Event: 4.246 Thread 0x000001e037b32e40 Thread added: 0x000001e037b32e40


Dynamic libraries:
0x00007ff676320000 - 0x00007ff67632e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\java.exe
0x00007ffa60830000 - 0x00007ffa60a28000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa5f6a0000 - 0x00007ffa5f761000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa5e240000 - 0x00007ffa5e53d000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa5e5e0000 - 0x00007ffa5e6e0000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa3ca00000 - 0x00007ffa3ca17000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\jli.dll
0x00007ffa60280000 - 0x00007ffa6041d000 	C:\Windows\System32\USER32.dll
0x00007ffa5e540000 - 0x00007ffa5e562000 	C:\Windows\System32\win32u.dll
0x00007ffa5f100000 - 0x00007ffa5f12b000 	C:\Windows\System32\GDI32.dll
0x00007ffa5df70000 - 0x00007ffa5e087000 	C:\Windows\System32\gdi32full.dll
0x00007ffa5e790000 - 0x00007ffa5e82d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa390f0000 - 0x00007ffa3910b000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffa3ac90000 - 0x00007ffa3af2a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16\COMCTL32.dll
0x00007ffa5ee90000 - 0x00007ffa5ef2e000 	C:\Windows\System32\msvcrt.dll
0x00007ffa5f820000 - 0x00007ffa5f84f000 	C:\Windows\System32\IMM32.DLL
0x00007ffa536f0000 - 0x00007ffa536fc000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffa13810000 - 0x00007ffa1389d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\msvcp140.dll
0x00007ff9ea650000 - 0x00007ff9eb2ba000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\server\jvm.dll
0x00007ffa5f190000 - 0x00007ffa5f240000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa5e980000 - 0x00007ffa5ea20000 	C:\Windows\System32\sechost.dll
0x00007ffa60150000 - 0x00007ffa60273000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa5e830000 - 0x00007ffa5e857000 	C:\Windows\System32\bcrypt.dll
0x00007ffa60780000 - 0x00007ffa607eb000 	C:\Windows\System32\WS2_32.dll
0x00007ffa5dc10000 - 0x00007ffa5dc5b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffa4f290000 - 0x00007ffa4f2b7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa4f2d0000 - 0x00007ffa4f2da000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa5da40000 - 0x00007ffa5da52000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffa5c6b0000 - 0x00007ffa5c6c2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa51220000 - 0x00007ffa5122a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\jimage.dll
0x00007ffa5b960000 - 0x00007ffa5bb44000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa37390000 - 0x00007ffa373c4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa5dee0000 - 0x00007ffa5df62000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa52da0000 - 0x00007ffa52dae000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\instrument.dll
0x00007ffa32580000 - 0x00007ffa325a5000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\java.dll
0x00007ffa32010000 - 0x00007ffa32028000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\zip.dll
0x00007ffa5f9d0000 - 0x00007ffa6013f000 	C:\Windows\System32\SHELL32.dll
0x00007ffa5bf00000 - 0x00007ffa5c69f000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffa60420000 - 0x00007ffa60773000 	C:\Windows\System32\combase.dll
0x00007ffa5d750000 - 0x00007ffa5d77e000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffa5f4a0000 - 0x00007ffa5f56d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffa5e860000 - 0x00007ffa5e90d000 	C:\Windows\System32\SHCORE.dll
0x00007ffa5f130000 - 0x00007ffa5f185000 	C:\Windows\System32\shlwapi.dll
0x00007ffa5de10000 - 0x00007ffa5de34000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffa366b0000 - 0x00007ffa366c9000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\net.dll
0x00007ffa59d80000 - 0x00007ffa59e8d000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffa5d530000 - 0x00007ffa5d59a000 	C:\Windows\system32\mswsock.dll
0x00007ffa358e0000 - 0x00007ffa358f6000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\nio.dll
0x00007ffa3e7c0000 - 0x00007ffa3e7d0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\verify.dll
0x00007ffa3d950000 - 0x00007ffa3d995000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.34.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1100.v20240722-2106\eclipse_11904.dll
0x00007ffa5f570000 - 0x00007ffa5f69b000 	C:\Windows\System32\ole32.dll
0x00007ffa5d790000 - 0x00007ffa5d7a8000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffa5ce70000 - 0x00007ffa5cea4000 	C:\Windows\system32\rsaenh.dll
0x00007ffa5ddc0000 - 0x00007ffa5ddee000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffa5d6c0000 - 0x00007ffa5d6cc000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffa5d230000 - 0x00007ffa5d26b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffa5e970000 - 0x00007ffa5e978000 	C:\Windows\System32\NSI.dll
0x00007ffa53f50000 - 0x00007ffa53f67000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa54400000 - 0x00007ffa5441d000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffa5d270000 - 0x00007ffa5d33a000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffa3d900000 - 0x00007ffa3d945000 	C:\Users\<USER>\AppData\Local\Temp\jna-76263555\jna1353000902942624307.dll
0x00007ffa60140000 - 0x00007ffa60148000 	C:\Windows\System32\PSAPI.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16;c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.34.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1100.v20240722-2106;C:\Users\<USER>\AppData\Local\Temp\jna-76263555

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\lombok\lombok-1.18.33.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\97c2628cb9069fc2c03a6e5dc60642b0\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.900.v20240613-2009.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.34.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\97c2628cb9069fc2c03a6e5dc60642b0\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-0a044a6e5b1799973af834e0c9f191d2-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.900.v20240613-2009.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\97c2628cb9069fc2c03a6e5dc60642b0\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-17.0.10.7-hotspot\
PATH=C:\Program Files\Microsoft\jdk-17.0.10.7-hotspot\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Yarn\bin;%NVM_HOME%;%NVM_SYMLINK%;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs
USERNAME=PMYLS
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 154 Stepping 4, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.4717)
OS uptime: 4 days 2:27 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 154 stepping 4 microcode 0x424, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv
Processor Information for processor 0
  Max Mhz: 1300, Current Mhz: 1300, Mhz Limit: 1300
Processor Information for processor 1
  Max Mhz: 1300, Current Mhz: 1300, Mhz Limit: 1300
Processor Information for processor 2
  Max Mhz: 1300, Current Mhz: 1300, Mhz Limit: 1300
Processor Information for processor 3
  Max Mhz: 1300, Current Mhz: 1300, Mhz Limit: 1300
Processor Information for processor 4
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 5
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 6
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 7
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 8
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 9
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 10
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 11
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900

Memory: 4k page, system-wide physical 7916M (263M free)
TotalPageFile size 30861M (AvailPageFile size 21M)
current process WorkingSet (physical memory assigned to process): 147M, peak: 147M
current process commit charge ("private bytes"): 243M, peak: 249M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7) for windows-amd64 JRE (17.0.12+7), built on Jul 16 2024 22:08:24 by "admin" with MS VC++ 16.10 / 16.11 (VS2019)

END.
