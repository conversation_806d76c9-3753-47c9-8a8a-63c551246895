import { StyleSheet } from 'react-native';
import React ,{useEffect}from 'react';
import Appnavigation from './src/navigation/Appnavigation';
import './src/Language/i18n/i18n'; // Import the i18n configuration
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import mobileAds from 'react-native-google-mobile-ads';

const App = () => {
   useEffect(() => {
    mobileAds().initialize();
  }, []);
  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaView style={styles.container}>
        <Appnavigation />
      </SafeAreaView>
    </GestureHandlerRootView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
