#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1119056 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=66756, tid=58600
#
# JRE version: OpenJDK Runtime Environment Temurin-17.0.12+7 (17.0.12+7) (build 17.0.12+7)
# Java VM: OpenJDK 64-Bit Server VM Temurin-17.0.12+7 (17.0.12+7, mixed mode, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\lombok\lombok-1.18.33.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\97c2628cb9069fc2c03a6e5dc60642b0\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.900.v20240613-2009.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.34.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\97c2628cb9069fc2c03a6e5dc60642b0\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-6aaa45458e1860a971781b8ca307b2cf-sock

Host: 12th Gen Intel(R) Core(TM) i5-1235U, 12 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.4717)
Time: Tue Sep 10 15:06:22 2024 Pakistan Standard Time elapsed time: 59.404632 seconds (0d 0h 0m 59s)

---------------  T H R E A D  ---------------

Current thread (0x0000022c7ce99120):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=58600, stack(0x000000f4aa400000,0x000000f4aa500000)]


Current CompileTask:
C2:  59404 7647       4       org.gradle.tooling.internal.provider.StreamedValueConsumer::dispatch (48 bytes)

Stack: [0x000000f4aa400000,0x000000f4aa500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x67f929]
V  [jvm.dll+0x8371ba]
V  [jvm.dll+0x838c7e]
V  [jvm.dll+0x8392e3]
V  [jvm.dll+0x24834f]
V  [jvm.dll+0xac9d4]
V  [jvm.dll+0xad01c]
V  [jvm.dll+0x367452]
V  [jvm.dll+0x33197a]
V  [jvm.dll+0x330e1a]
V  [jvm.dll+0x21a8b1]
V  [jvm.dll+0x219cf1]
V  [jvm.dll+0x1a58bd]
V  [jvm.dll+0x229a2d]
V  [jvm.dll+0x227bdc]
V  [jvm.dll+0x7ec1f7]
V  [jvm.dll+0x7e65dc]
V  [jvm.dll+0x67e7f7]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000022c01309340, length=48, elements={
0x0000022c63cc0360, 0x0000022c63d756e0, 0x0000022c7ce752a0, 0x0000022c63d7cf20,
0x0000022c63d7dcb0, 0x0000022c63d7ea40, 0x0000022c7ce98520, 0x0000022c7ce99120,
0x0000022c7ceee560, 0x0000022c7cf03220, 0x0000022c63d22190, 0x0000022c7e4190a0,
0x0000022c7e9b7640, 0x0000022c7e967520, 0x0000022c7e968210, 0x0000022c7f1f3690,
0x0000022c7f3caf80, 0x0000022c7f353180, 0x0000022c7f350e10, 0x0000022c7f351320,
0x0000022c7f352250, 0x0000022c7f3503f0, 0x0000022c7f350900, 0x0000022c7f352760,
0x0000022c7f352c70, 0x0000022c7f353690, 0x0000022c007fcd30, 0x0000022c007fe170,
0x0000022c007ffac0, 0x0000022c007fffd0, 0x0000022c008004e0, 0x0000022c007fbe00,
0x0000022c007fc820, 0x0000022c007f9070, 0x0000022c007fb3e0, 0x0000022c007fa9c0,
0x0000022c007fe680, 0x0000022c007feb90, 0x0000022c007fd240, 0x0000022c007fb8f0,
0x0000022c007fdc60, 0x0000022c008009f0, 0x0000022c007f9580, 0x0000022c007f9fa0,
0x0000022c007fa4b0, 0x0000022c007faed0, 0x0000022c007fc310, 0x0000022c007ff0a0
}

Java Threads: ( => current thread )
  0x0000022c63cc0360 JavaThread "main" [_thread_blocked, id=68288, stack(0x000000f4a9b00000,0x000000f4a9c00000)]
  0x0000022c63d756e0 JavaThread "Reference Handler" daemon [_thread_blocked, id=76928, stack(0x000000f4a9e00000,0x000000f4a9f00000)]
  0x0000022c7ce752a0 JavaThread "Finalizer" daemon [_thread_blocked, id=60804, stack(0x000000f4a9f00000,0x000000f4aa000000)]
  0x0000022c63d7cf20 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=81912, stack(0x000000f4aa000000,0x000000f4aa100000)]
  0x0000022c63d7dcb0 JavaThread "Attach Listener" daemon [_thread_blocked, id=30548, stack(0x000000f4aa100000,0x000000f4aa200000)]
  0x0000022c63d7ea40 JavaThread "Service Thread" daemon [_thread_blocked, id=59300, stack(0x000000f4aa200000,0x000000f4aa300000)]
  0x0000022c7ce98520 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=55964, stack(0x000000f4aa300000,0x000000f4aa400000)]
=>0x0000022c7ce99120 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=58600, stack(0x000000f4aa400000,0x000000f4aa500000)]
  0x0000022c7ceee560 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=53580, stack(0x000000f4aa500000,0x000000f4aa600000)]
  0x0000022c7cf03220 JavaThread "Sweeper thread" daemon [_thread_blocked, id=81892, stack(0x000000f4aa600000,0x000000f4aa700000)]
  0x0000022c63d22190 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=75964, stack(0x000000f4aa700000,0x000000f4aa800000)]
  0x0000022c7e4190a0 JavaThread "Notification Thread" daemon [_thread_blocked, id=45840, stack(0x000000f4aa800000,0x000000f4aa900000)]
  0x0000022c7e9b7640 JavaThread "Active Thread: Equinox Container: 532bf836-3b1e-4d93-9ae2-65e11f266244" [_thread_blocked, id=75340, stack(0x000000f4ab000000,0x000000f4ab100000)]
  0x0000022c7e967520 JavaThread "Framework Event Dispatcher: Equinox Container: 532bf836-3b1e-4d93-9ae2-65e11f266244" daemon [_thread_blocked, id=43608, stack(0x000000f4ab100000,0x000000f4ab200000)]
  0x0000022c7e968210 JavaThread "Start Level: Equinox Container: 532bf836-3b1e-4d93-9ae2-65e11f266244" daemon [_thread_blocked, id=65900, stack(0x000000f4ab200000,0x000000f4ab300000)]
  0x0000022c7f1f3690 JavaThread "Bundle File Closer" daemon [_thread_blocked, id=55516, stack(0x000000f4ab400000,0x000000f4ab500000)]
  0x0000022c7f3caf80 JavaThread "SCR Component Actor" daemon [_thread_blocked, id=81624, stack(0x000000f4ab600000,0x000000f4ab700000)]
  0x0000022c7f353180 JavaThread "Worker-JM" [_thread_blocked, id=61500, stack(0x000000f4ab800000,0x000000f4ab900000)]
  0x0000022c7f350e10 JavaThread "JNA Cleaner" daemon [_thread_blocked, id=11880, stack(0x000000f4aba00000,0x000000f4abb00000)]
  0x0000022c7f351320 JavaThread "Worker-0" [_thread_blocked, id=75804, stack(0x000000f4abb00000,0x000000f4abc00000)]
  0x0000022c7f352250 JavaThread "Worker-1" [_thread_blocked, id=67624, stack(0x000000f4abc00000,0x000000f4abd00000)]
  0x0000022c7f3503f0 JavaThread "Worker-2" [_thread_blocked, id=73824, stack(0x000000f4abd00000,0x000000f4abe00000)]
  0x0000022c7f350900 JavaThread "Java indexing" daemon [_thread_blocked, id=81552, stack(0x000000f4abe00000,0x000000f4abf00000)]
  0x0000022c7f352760 JavaThread "Worker-3: Initialize Workspace" [_thread_blocked, id=81896, stack(0x000000f4abf00000,0x000000f4ac000000)]
  0x0000022c7f352c70 JavaThread "Thread-2" daemon [_thread_in_native, id=368, stack(0x000000f4ac200000,0x000000f4ac300000)]
  0x0000022c7f353690 JavaThread "Thread-3" daemon [_thread_in_native, id=79192, stack(0x000000f4ac300000,0x000000f4ac400000)]
  0x0000022c007fcd30 JavaThread "Thread-4" daemon [_thread_in_native, id=75652, stack(0x000000f4ac400000,0x000000f4ac500000)]
  0x0000022c007fe170 JavaThread "Thread-5" daemon [_thread_in_native, id=74612, stack(0x000000f4ac500000,0x000000f4ac600000)]
  0x0000022c007ffac0 JavaThread "Thread-6" daemon [_thread_in_native, id=81404, stack(0x000000f4ac600000,0x000000f4ac700000)]
  0x0000022c007fffd0 JavaThread "Thread-7" daemon [_thread_in_native, id=76112, stack(0x000000f4ac700000,0x000000f4ac800000)]
  0x0000022c008004e0 JavaThread "Thread-8" daemon [_thread_in_native, id=77492, stack(0x000000f4ac800000,0x000000f4ac900000)]
  0x0000022c007fbe00 JavaThread "Thread-9" daemon [_thread_in_native, id=43704, stack(0x000000f4ac900000,0x000000f4aca00000)]
  0x0000022c007fc820 JavaThread "Thread-10" daemon [_thread_in_native, id=10412, stack(0x000000f4aca00000,0x000000f4acb00000)]
  0x0000022c007f9070 JavaThread "Thread-11" daemon [_thread_in_native, id=57304, stack(0x000000f4acb00000,0x000000f4acc00000)]
  0x0000022c007fb3e0 JavaThread "Thread-12" daemon [_thread_in_native, id=62780, stack(0x000000f4acc00000,0x000000f4acd00000)]
  0x0000022c007fa9c0 JavaThread "Thread-13" daemon [_thread_in_native, id=38240, stack(0x000000f4acd00000,0x000000f4ace00000)]
  0x0000022c007fe680 JavaThread "Thread-14" daemon [_thread_in_native, id=80284, stack(0x000000f4ace00000,0x000000f4acf00000)]
  0x0000022c007feb90 JavaThread "pool-2-thread-1" [_thread_blocked, id=80240, stack(0x000000f4acf00000,0x000000f4ad000000)]
  0x0000022c007fd240 JavaThread "WorkspaceEventsHandler" [_thread_blocked, id=81504, stack(0x000000f4ad000000,0x000000f4ad100000)]
  0x0000022c007fb8f0 JavaThread "pool-1-thread-1" [_thread_blocked, id=61296, stack(0x000000f4ad100000,0x000000f4ad200000)]
  0x0000022c007fdc60 JavaThread "Timer-0" [_thread_blocked, id=77664, stack(0x000000f4ab700000,0x000000f4ab800000)]
  0x0000022c008009f0 JavaThread "Exec process" [_thread_blocked, id=79288, stack(0x000000f4ad300000,0x000000f4ad400000)]
  0x0000022c007f9580 JavaThread "Exec process Thread 2" [_thread_blocked, id=74376, stack(0x000000f4ad400000,0x000000f4ad500000)]
  0x0000022c007f9fa0 JavaThread "Exec process Thread 3" [_thread_blocked, id=33460, stack(0x000000f4ad500000,0x000000f4ad600000)]
  0x0000022c007fa4b0 JavaThread "Timer-1" [_thread_blocked, id=81156, stack(0x000000f4ad200000,0x000000f4ad300000)]
  0x0000022c007faed0 JavaThread "Connection worker" [_thread_in_native, id=54204, stack(0x000000f4ad600000,0x000000f4ad700000)]
  0x0000022c007fc310 JavaThread "pool-3-thread-1" [_thread_blocked, id=80332, stack(0x000000f4ad700000,0x000000f4ad800000)]
  0x0000022c007ff0a0 JavaThread "Forward input" [_thread_blocked, id=79284, stack(0x000000f4ad900000,0x000000f4ada00000)]

Other Threads:
  0x0000022c7ce51fd0 VMThread "VM Thread" [stack: 0x000000f4a9d00000,0x000000f4a9e00000] [id=66052]
  0x0000022c7d1ed8f0 WatcherThread [stack: 0x000000f4aa900000,0x000000f4aaa00000] [id=56096]
  0x0000022c63cd4650 GCTaskThread "GC Thread#0" [stack: 0x000000f4a9c00000,0x000000f4a9d00000] [id=77112]
  0x0000022c7e4e2480 GCTaskThread "GC Thread#1" [stack: 0x000000f4aaa00000,0x000000f4aab00000] [id=55640]
  0x0000022c7eb59900 GCTaskThread "GC Thread#2" [stack: 0x000000f4aab00000,0x000000f4aac00000] [id=80488]
  0x0000022c7eb59bc0 GCTaskThread "GC Thread#3" [stack: 0x000000f4aac00000,0x000000f4aad00000] [id=80084]
  0x0000022c7eb59e80 GCTaskThread "GC Thread#4" [stack: 0x000000f4aad00000,0x000000f4aae00000] [id=8672]
  0x0000022c7eb5a140 GCTaskThread "GC Thread#5" [stack: 0x000000f4aae00000,0x000000f4aaf00000] [id=78344]
  0x0000022c7e652ba0 GCTaskThread "GC Thread#6" [stack: 0x000000f4aaf00000,0x000000f4ab000000] [id=31952]
  0x0000022c7f21d010 GCTaskThread "GC Thread#7" [stack: 0x000000f4ab300000,0x000000f4ab400000] [id=81740]
  0x0000022c7f283030 GCTaskThread "GC Thread#8" [stack: 0x000000f4ab500000,0x000000f4ab600000] [id=63664]
  0x0000022c7ebd4c30 GCTaskThread "GC Thread#9" [stack: 0x000000f4ab900000,0x000000f4aba00000] [id=72128]

Threads with active compile tasks:
C2 CompilerThread0    59443 7647       4       org.gradle.tooling.internal.provider.StreamedValueConsumer::dispatch (48 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x0000000140000000, reserved size: **********
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x140000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 7916M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 10

Heap:
 PSYoungGen      total 11776K, used 4562K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 38% used [0x00000000eab00000,0x00000000eaf34ba0,0x00000000eb600000)
  from space 512K, 50% used [0x00000000eba80000,0x00000000ebac0000,0x00000000ebb00000)
  to   space 512K, 0% used [0x00000000ebb00000,0x00000000ebb00000,0x00000000ebb80000)
 ParOldGen       total 68608K, used 47428K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 69% used [0x00000000c0000000,0x00000000c2e51178,0x00000000c4300000)
 Metaspace       used 62994K, committed 64064K, reserved 1114112K
  class space    used 7309K, committed 7744K, reserved 1048576K

Card table byte_map: [0x0000022c63660000,0x0000022c63870000] _byte_map_base: 0x0000022c63060000

Marking Bits: (ParMarkBitMap*) 0x00007ffa06ce58b0
 Begin Bits: [0x0000022c75960000, 0x0000022c76960000)
 End Bits:   [0x0000022c76960000, 0x0000022c77960000)

Polling page: 0x0000022c61b40000

Metaspace:

Usage:
  Non-class:     54.38 MB used.
      Class:      7.14 MB used.
       Both:     61.52 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      55.00 MB ( 86%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       7.56 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      62.56 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  8.41 MB
       Class:  8.47 MB
        Both:  16.88 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.31 MB
CDS: off
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 690.
num_arena_deaths: 42.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 1001.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 59.
num_chunks_taken_from_freelist: 3083.
num_chunk_merges: 17.
num_chunk_splits: 1957.
num_chunks_enlarged: 1237.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=3949Kb max_used=3949Kb free=116050Kb
 bounds [0x0000022c6e430000, 0x0000022c6e810000, 0x0000022c75960000]
CodeHeap 'profiled nmethods': size=120000Kb used=13269Kb max_used=13269Kb free=106730Kb
 bounds [0x0000022c66960000, 0x0000022c67660000, 0x0000022c6de90000]
CodeHeap 'non-nmethods': size=5760Kb used=1326Kb max_used=1383Kb free=4433Kb
 bounds [0x0000022c6de90000, 0x0000022c6e100000, 0x0000022c6e430000]
 total_blobs=7387 nmethods=6724 adapters=575
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 58.739 Thread 0x0000022c7ce99120 nmethod 7638 0x0000022c6e803990 code [0x0000022c6e803d00, 0x0000022c6e8065c0]
Event: 58.948 Thread 0x0000022c7ceee560 7640       3       java.util.concurrent.locks.AbstractQueuedSynchronizer$ExclusiveNode::<init> (5 bytes)
Event: 58.949 Thread 0x0000022c7ceee560 nmethod 7640 0x0000022c67655710 code [0x0000022c676558c0, 0x0000022c67655a78]
Event: 58.989 Thread 0x0000022c7ceee560 7641       3       org.eclipse.core.runtime.SubMonitor::cleanupActiveChild (22 bytes)
Event: 58.990 Thread 0x0000022c7ceee560 nmethod 7641 0x0000022c67655b90 code [0x0000022c67655d40, 0x0000022c67655f28]
Event: 58.990 Thread 0x0000022c7ce99120 7642       4       org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler::invoke (184 bytes)
Event: 58.993 Thread 0x0000022c7ceee560 7645       3       org.eclipse.core.runtime.SubMonitor::consume (134 bytes)
Event: 58.995 Thread 0x0000022c7ceee560 nmethod 7645 0x0000022c67656010 code [0x0000022c676561e0, 0x0000022c67656698]
Event: 59.002 Thread 0x0000022c7ce99120 nmethod 7642 0x0000022c6e808590 code [0x0000022c6e8087a0, 0x0000022c6e808fc8]
Event: 59.003 Thread 0x0000022c7ce99120 7644       4       org.gradle.internal.operations.OperationIdentifier::equals (47 bytes)
Event: 59.005 Thread 0x0000022c7ce99120 nmethod 7644 0x0000022c6e809710 code [0x0000022c6e809880, 0x0000022c6e809978]
Event: 59.005 Thread 0x0000022c7ce99120 7643       4       org.gradle.internal.event.ListenerBroadcast::getSource (35 bytes)
Event: 59.017 Thread 0x0000022c7ce99120 nmethod 7643 0x0000022c6e809a90 code [0x0000022c6e809c80, 0x0000022c6e80a140]
Event: 59.017 Thread 0x0000022c7ce99120 7646       4       org.gradle.internal.event.ListenerBroadcast::dispatch (9 bytes)
Event: 59.026 Thread 0x0000022c7ce99120 nmethod 7646 0x0000022c6e80a690 code [0x0000022c6e80a880, 0x0000022c6e80abe8]
Event: 59.305 Thread 0x0000022c7ce99120 7647       4       org.gradle.tooling.internal.provider.StreamedValueConsumer::dispatch (48 bytes)
Event: 59.306 Thread 0x0000022c7ceee560 7648       1       org.gradle.internal.logging.sink.OutputEventListenerManager::access$000 (5 bytes)
Event: 59.306 Thread 0x0000022c7ceee560 nmethod 7648 0x0000022c6e80b110 code [0x0000022c6e80b2a0, 0x0000022c6e80b358]
Event: 59.306 Thread 0x0000022c7ceee560 7649       1       org.gradle.internal.logging.sink.OutputEventListenerManager::access$100 (5 bytes)
Event: 59.306 Thread 0x0000022c7ceee560 nmethod 7649 0x0000022c6e80b410 code [0x0000022c6e80b5a0, 0x0000022c6e80b658]

GC Heap History (20 events):
Event: 26.517 GC heap before
{Heap before GC invocations=35 (full 3):
 PSYoungGen      total 11776K, used 320K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb600000)
  from space 512K, 62% used [0x00000000ebb00000,0x00000000ebb50000,0x00000000ebb80000)
  to   space 512K, 0% used [0x00000000eba80000,0x00000000eba80000,0x00000000ebb00000)
 ParOldGen       total 68608K, used 47266K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 68% used [0x00000000c0000000,0x00000000c2e28a58,0x00000000c4300000)
 Metaspace       used 58817K, committed 59776K, reserved 1114112K
  class space    used 6718K, committed 7104K, reserved 1048576K
}
Event: 26.554 GC heap after
{Heap after GC invocations=35 (full 3):
 PSYoungGen      total 11776K, used 0K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb600000)
  from space 512K, 0% used [0x00000000ebb00000,0x00000000ebb00000,0x00000000ebb80000)
  to   space 512K, 0% used [0x00000000eba80000,0x00000000eba80000,0x00000000ebb00000)
 ParOldGen       total 68608K, used 45459K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 66% used [0x00000000c0000000,0x00000000c2c64f20,0x00000000c4300000)
 Metaspace       used 58817K, committed 59776K, reserved 1114112K
  class space    used 6718K, committed 7104K, reserved 1048576K
}
Event: 26.660 GC heap before
{Heap before GC invocations=36 (full 3):
 PSYoungGen      total 11776K, used 11264K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 100% used [0x00000000eab00000,0x00000000eb600000,0x00000000eb600000)
  from space 512K, 0% used [0x00000000ebb00000,0x00000000ebb00000,0x00000000ebb80000)
  to   space 512K, 0% used [0x00000000eba80000,0x00000000eba80000,0x00000000ebb00000)
 ParOldGen       total 68608K, used 45459K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 66% used [0x00000000c0000000,0x00000000c2c64f20,0x00000000c4300000)
 Metaspace       used 59895K, committed 60864K, reserved 1114112K
  class space    used 6917K, committed 7296K, reserved 1048576K
}
Event: 26.661 GC heap after
{Heap after GC invocations=36 (full 3):
 PSYoungGen      total 11776K, used 480K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb600000)
  from space 512K, 93% used [0x00000000eba80000,0x00000000ebaf8000,0x00000000ebb00000)
  to   space 512K, 0% used [0x00000000ebb00000,0x00000000ebb00000,0x00000000ebb80000)
 ParOldGen       total 68608K, used 45467K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 66% used [0x00000000c0000000,0x00000000c2c66f20,0x00000000c4300000)
 Metaspace       used 59895K, committed 60864K, reserved 1114112K
  class space    used 6917K, committed 7296K, reserved 1048576K
}
Event: 27.067 GC heap before
{Heap before GC invocations=37 (full 3):
 PSYoungGen      total 11776K, used 11744K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 100% used [0x00000000eab00000,0x00000000eb600000,0x00000000eb600000)
  from space 512K, 93% used [0x00000000eba80000,0x00000000ebaf8000,0x00000000ebb00000)
  to   space 512K, 0% used [0x00000000ebb00000,0x00000000ebb00000,0x00000000ebb80000)
 ParOldGen       total 68608K, used 45467K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 66% used [0x00000000c0000000,0x00000000c2c66f20,0x00000000c4300000)
 Metaspace       used 61077K, committed 62144K, reserved 1114112K
  class space    used 7071K, committed 7488K, reserved 1048576K
}
Event: 27.068 GC heap after
{Heap after GC invocations=37 (full 3):
 PSYoungGen      total 11776K, used 448K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb600000)
  from space 512K, 87% used [0x00000000ebb00000,0x00000000ebb70000,0x00000000ebb80000)
  to   space 512K, 0% used [0x00000000eba80000,0x00000000eba80000,0x00000000ebb00000)
 ParOldGen       total 68608K, used 45755K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 66% used [0x00000000c0000000,0x00000000c2caec30,0x00000000c4300000)
 Metaspace       used 61077K, committed 62144K, reserved 1114112K
  class space    used 7071K, committed 7488K, reserved 1048576K
}
Event: 32.313 GC heap before
{Heap before GC invocations=38 (full 3):
 PSYoungGen      total 11776K, used 11712K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 100% used [0x00000000eab00000,0x00000000eb600000,0x00000000eb600000)
  from space 512K, 87% used [0x00000000ebb00000,0x00000000ebb70000,0x00000000ebb80000)
  to   space 512K, 0% used [0x00000000eba80000,0x00000000eba80000,0x00000000ebb00000)
 ParOldGen       total 68608K, used 45755K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 66% used [0x00000000c0000000,0x00000000c2caec30,0x00000000c4300000)
 Metaspace       used 62362K, committed 63424K, reserved 1114112K
  class space    used 7248K, committed 7680K, reserved 1048576K
}
Event: 32.316 GC heap after
{Heap after GC invocations=38 (full 3):
 PSYoungGen      total 11776K, used 512K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb600000)
  from space 512K, 100% used [0x00000000eba80000,0x00000000ebb00000,0x00000000ebb00000)
  to   space 512K, 0% used [0x00000000ebb00000,0x00000000ebb00000,0x00000000ebb80000)
 ParOldGen       total 68608K, used 46363K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 67% used [0x00000000c0000000,0x00000000c2d46c90,0x00000000c4300000)
 Metaspace       used 62362K, committed 63424K, reserved 1114112K
  class space    used 7248K, committed 7680K, reserved 1048576K
}
Event: 51.078 GC heap before
{Heap before GC invocations=39 (full 3):
 PSYoungGen      total 11776K, used 11776K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 100% used [0x00000000eab00000,0x00000000eb600000,0x00000000eb600000)
  from space 512K, 100% used [0x00000000eba80000,0x00000000ebb00000,0x00000000ebb00000)
  to   space 512K, 0% used [0x00000000ebb00000,0x00000000ebb00000,0x00000000ebb80000)
 ParOldGen       total 68608K, used 46363K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 67% used [0x00000000c0000000,0x00000000c2d46c90,0x00000000c4300000)
 Metaspace       used 62799K, committed 63872K, reserved 1114112K
  class space    used 7275K, committed 7680K, reserved 1048576K
}
Event: 51.080 GC heap after
{Heap after GC invocations=39 (full 3):
 PSYoungGen      total 11776K, used 480K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb600000)
  from space 512K, 93% used [0x00000000ebb00000,0x00000000ebb78000,0x00000000ebb80000)
  to   space 1024K, 0% used [0x00000000eb980000,0x00000000eb980000,0x00000000eba80000)
 ParOldGen       total 68608K, used 46895K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 68% used [0x00000000c0000000,0x00000000c2dcbce0,0x00000000c4300000)
 Metaspace       used 62799K, committed 63872K, reserved 1114112K
  class space    used 7275K, committed 7680K, reserved 1048576K
}
Event: 52.334 GC heap before
{Heap before GC invocations=40 (full 3):
 PSYoungGen      total 11776K, used 11744K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 100% used [0x00000000eab00000,0x00000000eb600000,0x00000000eb600000)
  from space 512K, 93% used [0x00000000ebb00000,0x00000000ebb78000,0x00000000ebb80000)
  to   space 1024K, 0% used [0x00000000eb980000,0x00000000eb980000,0x00000000eba80000)
 ParOldGen       total 68608K, used 46895K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 68% used [0x00000000c0000000,0x00000000c2dcbce0,0x00000000c4300000)
 Metaspace       used 62963K, committed 64064K, reserved 1114112K
  class space    used 7303K, committed 7744K, reserved 1048576K
}
Event: 52.335 GC heap after
{Heap after GC invocations=40 (full 3):
 PSYoungGen      total 12288K, used 320K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb600000)
  from space 1024K, 31% used [0x00000000eb980000,0x00000000eb9d0000,0x00000000eba80000)
  to   space 512K, 0% used [0x00000000ebb00000,0x00000000ebb00000,0x00000000ebb80000)
 ParOldGen       total 68608K, used 47220K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 68% used [0x00000000c0000000,0x00000000c2e1d178,0x00000000c4300000)
 Metaspace       used 62963K, committed 64064K, reserved 1114112K
  class space    used 7303K, committed 7744K, reserved 1048576K
}
Event: 53.313 GC heap before
{Heap before GC invocations=41 (full 3):
 PSYoungGen      total 12288K, used 11584K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 100% used [0x00000000eab00000,0x00000000eb600000,0x00000000eb600000)
  from space 1024K, 31% used [0x00000000eb980000,0x00000000eb9d0000,0x00000000eba80000)
  to   space 512K, 0% used [0x00000000ebb00000,0x00000000ebb00000,0x00000000ebb80000)
 ParOldGen       total 68608K, used 47220K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 68% used [0x00000000c0000000,0x00000000c2e1d178,0x00000000c4300000)
 Metaspace       used 62983K, committed 64064K, reserved 1114112K
  class space    used 7309K, committed 7744K, reserved 1048576K
}
Event: 53.314 GC heap after
{Heap after GC invocations=41 (full 3):
 PSYoungGen      total 11776K, used 256K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb600000)
  from space 512K, 50% used [0x00000000ebb00000,0x00000000ebb40000,0x00000000ebb80000)
  to   space 512K, 0% used [0x00000000eba80000,0x00000000eba80000,0x00000000ebb00000)
 ParOldGen       total 68608K, used 47340K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 69% used [0x00000000c0000000,0x00000000c2e3b178,0x00000000c4300000)
 Metaspace       used 62983K, committed 64064K, reserved 1114112K
  class space    used 7309K, committed 7744K, reserved 1048576K
}
Event: 57.355 GC heap before
{Heap before GC invocations=42 (full 3):
 PSYoungGen      total 11776K, used 11520K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 100% used [0x00000000eab00000,0x00000000eb600000,0x00000000eb600000)
  from space 512K, 50% used [0x00000000ebb00000,0x00000000ebb40000,0x00000000ebb80000)
  to   space 512K, 0% used [0x00000000eba80000,0x00000000eba80000,0x00000000ebb00000)
 ParOldGen       total 68608K, used 47340K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 69% used [0x00000000c0000000,0x00000000c2e3b178,0x00000000c4300000)
 Metaspace       used 62991K, committed 64064K, reserved 1114112K
  class space    used 7309K, committed 7744K, reserved 1048576K
}
Event: 57.355 GC heap after
{Heap after GC invocations=42 (full 3):
 PSYoungGen      total 11776K, used 128K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb600000)
  from space 512K, 25% used [0x00000000eba80000,0x00000000ebaa0000,0x00000000ebb00000)
  to   space 512K, 0% used [0x00000000ebb00000,0x00000000ebb00000,0x00000000ebb80000)
 ParOldGen       total 68608K, used 47380K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 69% used [0x00000000c0000000,0x00000000c2e45178,0x00000000c4300000)
 Metaspace       used 62991K, committed 64064K, reserved 1114112K
  class space    used 7309K, committed 7744K, reserved 1048576K
}
Event: 58.011 GC heap before
{Heap before GC invocations=43 (full 3):
 PSYoungGen      total 11776K, used 11392K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 100% used [0x00000000eab00000,0x00000000eb600000,0x00000000eb600000)
  from space 512K, 25% used [0x00000000eba80000,0x00000000ebaa0000,0x00000000ebb00000)
  to   space 512K, 0% used [0x00000000ebb00000,0x00000000ebb00000,0x00000000ebb80000)
 ParOldGen       total 68608K, used 47380K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 69% used [0x00000000c0000000,0x00000000c2e45178,0x00000000c4300000)
 Metaspace       used 62991K, committed 64064K, reserved 1114112K
  class space    used 7309K, committed 7744K, reserved 1048576K
}
Event: 58.012 GC heap after
{Heap after GC invocations=43 (full 3):
 PSYoungGen      total 11776K, used 192K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb600000)
  from space 512K, 37% used [0x00000000ebb00000,0x00000000ebb30000,0x00000000ebb80000)
  to   space 512K, 0% used [0x00000000eba80000,0x00000000eba80000,0x00000000ebb00000)
 ParOldGen       total 68608K, used 47420K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 69% used [0x00000000c0000000,0x00000000c2e4f178,0x00000000c4300000)
 Metaspace       used 62991K, committed 64064K, reserved 1114112K
  class space    used 7309K, committed 7744K, reserved 1048576K
}
Event: 58.675 GC heap before
{Heap before GC invocations=44 (full 3):
 PSYoungGen      total 11776K, used 11456K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 100% used [0x00000000eab00000,0x00000000eb600000,0x00000000eb600000)
  from space 512K, 37% used [0x00000000ebb00000,0x00000000ebb30000,0x00000000ebb80000)
  to   space 512K, 0% used [0x00000000eba80000,0x00000000eba80000,0x00000000ebb00000)
 ParOldGen       total 68608K, used 47420K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 69% used [0x00000000c0000000,0x00000000c2e4f178,0x00000000c4300000)
 Metaspace       used 62992K, committed 64064K, reserved 1114112K
  class space    used 7309K, committed 7744K, reserved 1048576K
}
Event: 58.676 GC heap after
{Heap after GC invocations=44 (full 3):
 PSYoungGen      total 11776K, used 256K [0x00000000eab00000, 0x00000000ebb80000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb600000)
  from space 512K, 50% used [0x00000000eba80000,0x00000000ebac0000,0x00000000ebb00000)
  to   space 512K, 0% used [0x00000000ebb00000,0x00000000ebb00000,0x00000000ebb80000)
 ParOldGen       total 68608K, used 47428K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 69% used [0x00000000c0000000,0x00000000c2e51178,0x00000000c4300000)
 Metaspace       used 62992K, committed 64064K, reserved 1114112K
  class space    used 7309K, committed 7744K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.015 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\java.dll
Event: 0.396 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\zip.dll
Event: 0.402 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\instrument.dll
Event: 0.422 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\net.dll
Event: 0.424 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\nio.dll
Event: 0.434 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\zip.dll
Event: 0.463 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\jimage.dll
Event: 0.564 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\verify.dll
Event: 2.711 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.34.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1100.v20240722-2106\eclipse_11904.dll
Event: 5.458 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-76263555\jna8147376856636392865.dll
Event: 20.420 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 26.471 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\management.dll
Event: 26.474 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\management_ext.dll
Event: 27.073 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\extnet.dll
Event: 27.132 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 49.809 Thread 0x0000022c007faed0 DEOPT PACKING pc=0x0000022c6e77f364 sp=0x000000f4ad6fd610
Event: 49.809 Thread 0x0000022c007faed0 DEOPT UNPACKING pc=0x0000022c6dee66a3 sp=0x000000f4ad6fd5c8 mode 2
Event: 49.810 Thread 0x0000022c007faed0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000022c6e77f364 relative=0x00000000000002c4
Event: 49.810 Thread 0x0000022c007faed0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000022c6e77f364 method=java.io.DataOutputStream.writeInt(I)V @ 53 c2
Event: 49.810 Thread 0x0000022c007faed0 DEOPT PACKING pc=0x0000022c6e77f364 sp=0x000000f4ad6fd610
Event: 49.810 Thread 0x0000022c007faed0 DEOPT UNPACKING pc=0x0000022c6dee66a3 sp=0x000000f4ad6fd5c8 mode 2
Event: 49.810 Thread 0x0000022c007faed0 DEOPT PACKING pc=0x0000022c67541d98 sp=0x000000f4ad6fd510
Event: 49.810 Thread 0x0000022c007faed0 DEOPT UNPACKING pc=0x0000022c6dee6e43 sp=0x000000f4ad6fca58 mode 0
Event: 49.952 Thread 0x0000022c007faed0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000022c6e793b2c relative=0x000000000000010c
Event: 49.952 Thread 0x0000022c007faed0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000022c6e793b2c method=com.esotericsoftware.kryo.io.Input.read([BII)I @ 64 c2
Event: 49.952 Thread 0x0000022c007faed0 DEOPT PACKING pc=0x0000022c6e793b2c sp=0x000000f4ad6fd220
Event: 49.952 Thread 0x0000022c007faed0 DEOPT UNPACKING pc=0x0000022c6dee66a3 sp=0x000000f4ad6fd158 mode 2
Event: 50.138 Thread 0x0000022c007faed0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000022c6e799404 relative=0x0000000000000344
Event: 50.138 Thread 0x0000022c007faed0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000022c6e799404 method=java.io.ObjectInputStream$PeekInputStream.read([BII)I @ 10 c2
Event: 50.138 Thread 0x0000022c007faed0 DEOPT PACKING pc=0x0000022c6e799404 sp=0x000000f4ad6fc6d0
Event: 50.138 Thread 0x0000022c007faed0 DEOPT UNPACKING pc=0x0000022c6dee66a3 sp=0x000000f4ad6fc698 mode 2
Event: 52.074 Thread 0x0000022c007faed0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000022c6e7c23f0 relative=0x00000000000021f0
Event: 52.074 Thread 0x0000022c007faed0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000022c6e7c23f0 method=java.io.ObjectInputStream.readObject0(Ljava/lang/Class;Z)Ljava/lang/Object; @ 113 c2
Event: 52.074 Thread 0x0000022c007faed0 DEOPT PACKING pc=0x0000022c6e7c23f0 sp=0x000000f4ad6fd3f0
Event: 52.075 Thread 0x0000022c007faed0 DEOPT UNPACKING pc=0x0000022c6dee66a3 sp=0x000000f4ad6fd3e8 mode 2

Classes loaded (20 events):
Event: 32.305 Loading class sun/util/resources/cldr/TimeZoneNames
Event: 32.306 Loading class sun/util/resources/TimeZoneNamesBundle
Event: 32.306 Loading class sun/util/resources/OpenListResourceBundle
Event: 32.307 Loading class sun/util/resources/OpenListResourceBundle done
Event: 32.307 Loading class sun/util/resources/TimeZoneNamesBundle done
Event: 32.307 Loading class sun/util/resources/cldr/TimeZoneNames done
Event: 32.307 Loading class sun/util/resources/cldr/TimeZoneNames_en
Event: 32.311 Loading class sun/util/resources/cldr/TimeZoneNames_en done
Event: 32.311 Loading class sun/util/resources/cldr/TimeZoneNames_en_US
Event: 32.311 Loading class sun/util/resources/cldr/TimeZoneNames_en_US done
Event: 32.318 Loading class sun/util/cldr/CLDRBaseLocaleDataMetaInfo$TZCanonicalIDMapHolder
Event: 32.321 Loading class sun/util/cldr/CLDRBaseLocaleDataMetaInfo$TZCanonicalIDMapHolder done
Event: 32.324 Loading class sun/util/resources/TimeZoneNames
Event: 32.327 Loading class sun/util/resources/TimeZoneNames done
Event: 32.327 Loading class sun/util/resources/TimeZoneNames_en
Event: 32.327 Loading class sun/util/resources/TimeZoneNames_en done
Event: 51.066 Loading class java/time/Ser
Event: 51.068 Loading class java/time/Ser done
Event: 52.075 Loading class java/io/ObjectStreamClass$ExceptionInfo
Event: 52.075 Loading class java/io/ObjectStreamClass$ExceptionInfo done

Classes unloaded (20 events):
Event: 4.496 Thread 0x0000022c7ce51fd0 Unloading class 0x000000010024c000 'java/lang/invoke/LambdaForm$MH+0x000000010024c000'
Event: 4.496 Thread 0x0000022c7ce51fd0 Unloading class 0x000000010024bc00 'java/lang/invoke/LambdaForm$MH+0x000000010024bc00'
Event: 4.496 Thread 0x0000022c7ce51fd0 Unloading class 0x000000010024b800 'java/lang/invoke/LambdaForm$MH+0x000000010024b800'
Event: 4.496 Thread 0x0000022c7ce51fd0 Unloading class 0x000000010024b400 'java/lang/invoke/LambdaForm$BMH+0x000000010024b400'
Event: 4.496 Thread 0x0000022c7ce51fd0 Unloading class 0x000000010024b000 'java/lang/invoke/LambdaForm$DMH+0x000000010024b000'
Event: 4.496 Thread 0x0000022c7ce51fd0 Unloading class 0x0000000100248400 'java/lang/invoke/LambdaForm$DMH+0x0000000100248400'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x000000010038b000 'java/lang/invoke/LambdaForm$MH+0x000000010038b000'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x000000010038a400 'java/lang/invoke/LambdaForm$MH+0x000000010038a400'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x0000000100389c00 'java/lang/invoke/LambdaForm$MH+0x0000000100389c00'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x0000000100389400 'java/lang/invoke/LambdaForm$MH+0x0000000100389400'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x0000000100389000 'java/lang/invoke/LambdaForm$DMH+0x0000000100389000'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x0000000100388400 'java/lang/invoke/LambdaForm$MH+0x0000000100388400'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x0000000100386c00 'java/lang/invoke/LambdaForm$MH+0x0000000100386c00'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x0000000100382c00 'java/lang/invoke/LambdaForm$MH+0x0000000100382c00'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x0000000100382400 'java/lang/invoke/LambdaForm$MH+0x0000000100382400'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x0000000100382000 'java/lang/invoke/LambdaForm$MH+0x0000000100382000'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x0000000100381800 'java/lang/invoke/LambdaForm$MH+0x0000000100381800'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x0000000100380c00 'java/lang/invoke/LambdaForm$MH+0x0000000100380c00'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x0000000100380400 'java/lang/invoke/LambdaForm$MH+0x0000000100380400'
Event: 7.909 Thread 0x0000022c7ce51fd0 Unloading class 0x0000000100380000 'java/lang/invoke/LambdaForm$MH+0x0000000100380000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 27.785 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb1db388}: static Lorg/gradle/internal/operations/OperationIdentifier;.<clinit>()V> (0x00000000eb1db388) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 28.044 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb275798}: static Lorg/gradle/internal/build/event/types/DefaultOperationFinishedProgressEvent;.<clinit>()V> (0x00000000eb275798) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 28.045 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb291dc0}: static Lorg/gradle/internal/build/event/types/AbstractResult;.<clinit>()V> (0x00000000eb291dc0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 28.045 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb29f388}: static Lorg/gradle/internal/build/event/types/AbstractOperationResult;.<clinit>()V> (0x00000000eb29f388) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 28.046 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb2ad268}: static Lorg/gradle/internal/build/event/types/DefaultSuccessResult;.<clinit>()V> (0x00000000eb2ad268) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 32.272 Thread 0x0000022c7e9b7640 Implicit null exception at 0x0000022c6e687388 to 0x0000022c6e687737
Event: 49.809 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ead11280}: static Lorg/gradle/internal/build/event/types/DefaultBuildPhaseDescriptor;.<clinit>()V> (0x00000000ead11280) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 50.136 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eadcd620}: static Lorg/gradle/internal/build/event/types/DefaultProjectConfigurationDescriptor;.<clinit>()V> (0x00000000eadcd620) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 51.056 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb4d0318}: static Lorg/gradle/internal/build/event/types/AbstractProjectConfigurationResult;.<clinit>()V> (0x00000000eb4d0318) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 51.057 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb4df8b8}: static Lorg/gradle/internal/build/event/types/DefaultProjectConfigurationSuccessResult;.<clinit>()V> (0x00000000eb4df8b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 51.063 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb514f30}: static Lorg/gradle/internal/build/event/types/DefaultPluginApplicationResult;.<clinit>()V> (0x00000000eb514f30) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 51.066 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb534038}: static Lorg/gradle/internal/build/event/types/DefaultBinaryPluginIdentifier;.<clinit>()V> (0x00000000eb534038) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 51.071 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb5596f0}: static Lorg/gradle/internal/build/event/types/DefaultScriptPluginIdentifier;.<clinit>()V> (0x00000000eb5596f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 52.053 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ead708c8}: static Lorg/gradle/internal/build/event/types/DefaultTaskStartedProgressEvent;.<clinit>()V> (0x00000000ead708c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 52.058 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ead91cb0}: static Lorg/gradle/internal/build/event/types/DefaultTaskDescriptor;.<clinit>()V> (0x00000000ead91cb0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 52.070 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eadfbb48}: static Lorg/gradle/internal/build/event/types/DefaultTaskFinishedProgressEvent;.<clinit>()V> (0x00000000eadfbb48) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 52.072 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eae208e0}: static Lorg/gradle/internal/build/event/types/AbstractTaskResult;.<clinit>()V> (0x00000000eae208e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 52.073 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eae2fa80}: static Lorg/gradle/internal/build/event/types/DefaultTaskSuccessResult;.<clinit>()V> (0x00000000eae2fa80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 52.147 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb088608}: static Lorg/gradle/internal/build/event/types/DefaultTaskSkippedResult;.<clinit>()V> (0x00000000eb088608) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 52.338 Thread 0x0000022c007faed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eab0da58}: static Lorg/gradle/internal/build/event/types/DefaultTransformDescriptor;.<clinit>()V> (0x00000000eab0da58) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]

VM Operations (20 events):
Event: 50.368 Executing VM operation: Cleanup
Event: 50.368 Executing VM operation: Cleanup done
Event: 51.078 Executing VM operation: ParallelGCFailedAllocation
Event: 51.080 Executing VM operation: ParallelGCFailedAllocation done
Event: 52.056 Executing VM operation: HandshakeAllThreads
Event: 52.056 Executing VM operation: HandshakeAllThreads done
Event: 52.333 Executing VM operation: ParallelGCFailedAllocation
Event: 52.335 Executing VM operation: ParallelGCFailedAllocation done
Event: 53.313 Executing VM operation: ParallelGCFailedAllocation
Event: 53.314 Executing VM operation: ParallelGCFailedAllocation done
Event: 56.314 Executing VM operation: Cleanup
Event: 56.314 Executing VM operation: Cleanup done
Event: 57.314 Executing VM operation: Cleanup
Event: 57.314 Executing VM operation: Cleanup done
Event: 57.355 Executing VM operation: ParallelGCFailedAllocation
Event: 57.355 Executing VM operation: ParallelGCFailedAllocation done
Event: 58.011 Executing VM operation: ParallelGCFailedAllocation
Event: 58.012 Executing VM operation: ParallelGCFailedAllocation done
Event: 58.675 Executing VM operation: ParallelGCFailedAllocation
Event: 58.676 Executing VM operation: ParallelGCFailedAllocation done

Events (20 events):
Event: 9.923 Thread 0x0000022c7cf03220 flushing nmethod 0x0000022c673e1490
Event: 9.923 Thread 0x0000022c7cf03220 flushing nmethod 0x0000022c673e3290
Event: 9.923 Thread 0x0000022c7cf03220 flushing nmethod 0x0000022c673fde90
Event: 9.923 Thread 0x0000022c7cf03220 flushing nmethod 0x0000022c67474c10
Event: 11.755 Thread 0x0000022c7ed91360 Thread exited: 0x0000022c7ed91360
Event: 11.825 Thread 0x0000022c007fdc60 Thread added: 0x0000022c007fdc60
Event: 12.005 Thread 0x0000022c007ff0a0 Thread added: 0x0000022c007ff0a0
Event: 26.703 Thread 0x0000022c008009f0 Thread added: 0x0000022c008009f0
Event: 26.719 Thread 0x0000022c7cf03220 flushing nmethod 0x0000022c6e5bf390
Event: 26.722 Thread 0x0000022c007f9580 Thread added: 0x0000022c007f9580
Event: 26.722 Thread 0x0000022c007f9fa0 Thread added: 0x0000022c007f9fa0
Event: 26.725 Thread 0x0000022c7cf03220 flushing nmethod 0x0000022c6725f390
Event: 26.726 Thread 0x0000022c7cf03220 flushing nmethod 0x0000022c673dfa10
Event: 26.862 Thread 0x0000022c007ff0a0 Thread exited: 0x0000022c007ff0a0
Event: 26.943 Thread 0x0000022c007fa4b0 Thread added: 0x0000022c007fa4b0
Event: 26.946 Thread 0x0000022c007faed0 Thread added: 0x0000022c007faed0
Event: 26.986 Thread 0x0000022c007fc310 Thread added: 0x0000022c007fc310
Event: 27.351 Thread 0x0000022c007ff0a0 Thread added: 0x0000022c007ff0a0
Event: 27.351 Thread 0x0000022c007ff0a0 Thread exited: 0x0000022c007ff0a0
Event: 27.353 Thread 0x0000022c007ff0a0 Thread added: 0x0000022c007ff0a0


Dynamic libraries:
0x00007ff78c590000 - 0x00007ff78c59e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\java.exe
0x00007ffa60830000 - 0x00007ffa60a28000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa5f6a0000 - 0x00007ffa5f761000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa5e240000 - 0x00007ffa5e53d000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa5e5e0000 - 0x00007ffa5e6e0000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa3d9b0000 - 0x00007ffa3d9cb000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffa53740000 - 0x00007ffa53757000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\jli.dll
0x00007ffa60280000 - 0x00007ffa6041d000 	C:\Windows\System32\USER32.dll
0x00007ffa5e540000 - 0x00007ffa5e562000 	C:\Windows\System32\win32u.dll
0x00007ffa3ac90000 - 0x00007ffa3af2a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16\COMCTL32.dll
0x00007ffa5f100000 - 0x00007ffa5f12b000 	C:\Windows\System32\GDI32.dll
0x00007ffa5ee90000 - 0x00007ffa5ef2e000 	C:\Windows\System32\msvcrt.dll
0x00007ffa5df70000 - 0x00007ffa5e087000 	C:\Windows\System32\gdi32full.dll
0x00007ffa5e790000 - 0x00007ffa5e82d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa5f820000 - 0x00007ffa5f84f000 	C:\Windows\System32\IMM32.DLL
0x00007ffa53770000 - 0x00007ffa5377c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffa2d240000 - 0x00007ffa2d2cd000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\msvcp140.dll
0x00007ffa06140000 - 0x00007ffa06daa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\server\jvm.dll
0x00007ffa5f190000 - 0x00007ffa5f240000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa5e980000 - 0x00007ffa5ea20000 	C:\Windows\System32\sechost.dll
0x00007ffa60150000 - 0x00007ffa60273000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa5e830000 - 0x00007ffa5e857000 	C:\Windows\System32\bcrypt.dll
0x00007ffa60780000 - 0x00007ffa607eb000 	C:\Windows\System32\WS2_32.dll
0x00007ffa5dc10000 - 0x00007ffa5dc5b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffa4f290000 - 0x00007ffa4f2b7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa4f2d0000 - 0x00007ffa4f2da000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa5da40000 - 0x00007ffa5da52000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffa5c6b0000 - 0x00007ffa5c6c2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa53730000 - 0x00007ffa5373a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\jimage.dll
0x00007ffa5b960000 - 0x00007ffa5bb44000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa37390000 - 0x00007ffa373c4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa5dee0000 - 0x00007ffa5df62000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa56f60000 - 0x00007ffa56f6e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\instrument.dll
0x00007ffa32b60000 - 0x00007ffa32b85000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\java.dll
0x00007ffa39850000 - 0x00007ffa39868000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\zip.dll
0x00007ffa5f9d0000 - 0x00007ffa6013f000 	C:\Windows\System32\SHELL32.dll
0x00007ffa5bf00000 - 0x00007ffa5c69f000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffa60420000 - 0x00007ffa60773000 	C:\Windows\System32\combase.dll
0x00007ffa5d750000 - 0x00007ffa5d77e000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffa5f4a0000 - 0x00007ffa5f56d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffa5e860000 - 0x00007ffa5e90d000 	C:\Windows\System32\SHCORE.dll
0x00007ffa5f130000 - 0x00007ffa5f185000 	C:\Windows\System32\shlwapi.dll
0x00007ffa5de10000 - 0x00007ffa5de34000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffa3a9a0000 - 0x00007ffa3a9b9000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\net.dll
0x00007ffa59d80000 - 0x00007ffa59e8d000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffa5d530000 - 0x00007ffa5d59a000 	C:\Windows\system32\mswsock.dll
0x00007ffa36440000 - 0x00007ffa36456000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\nio.dll
0x00007ffa56660000 - 0x00007ffa56670000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\verify.dll
0x00007ffa3da40000 - 0x00007ffa3da85000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.34.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1100.v20240722-2106\eclipse_11904.dll
0x00007ffa5f570000 - 0x00007ffa5f69b000 	C:\Windows\System32\ole32.dll
0x00007ffa5d790000 - 0x00007ffa5d7a8000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffa5ce70000 - 0x00007ffa5cea4000 	C:\Windows\system32\rsaenh.dll
0x00007ffa5ddc0000 - 0x00007ffa5ddee000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffa5d6c0000 - 0x00007ffa5d6cc000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffa5d230000 - 0x00007ffa5d26b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffa5e970000 - 0x00007ffa5e978000 	C:\Windows\System32\NSI.dll
0x00007ffa53f50000 - 0x00007ffa53f67000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa54400000 - 0x00007ffa5441d000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffa5d270000 - 0x00007ffa5d33a000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffa2cf60000 - 0x00007ffa2cfa5000 	C:\Users\<USER>\AppData\Local\Temp\jna-76263555\jna8147376856636392865.dll
0x00007ffa60140000 - 0x00007ffa60148000 	C:\Windows\System32\PSAPI.DLL
0x00007ffa32b90000 - 0x00007ffa32bb7000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffa4c980000 - 0x00007ffa4c989000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\management.dll
0x00007ffa3d9a0000 - 0x00007ffa3d9ab000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\management_ext.dll
0x00007ffa3d500000 - 0x00007ffa3d509000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\extnet.dll
0x00007ffa3d780000 - 0x00007ffa3d78e000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\sunmscapi.dll
0x00007ffa5e0e0000 - 0x00007ffa5e23e000 	C:\Windows\System32\CRYPT32.dll
0x00007ffa5d900000 - 0x00007ffa5d929000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffa5d8c0000 - 0x00007ffa5d8fb000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffa50f90000 - 0x00007ffa50f97000 	C:\Windows\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16;c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\jre\17.0.12-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.34.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1100.v20240722-2106;C:\Users\<USER>\AppData\Local\Temp\jna-76263555;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\lombok\lombok-1.18.33.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\97c2628cb9069fc2c03a6e5dc60642b0\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.900.v20240613-2009.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.34.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\97c2628cb9069fc2c03a6e5dc60642b0\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-6aaa45458e1860a971781b8ca307b2cf-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.34.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.900.v20240613-2009.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\97c2628cb9069fc2c03a6e5dc60642b0\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = **********                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = **********                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-17.0.10.7-hotspot\
PATH=C:\Program Files\Microsoft\jdk-17.0.10.7-hotspot\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Yarn\bin;%NVM_HOME%;%NVM_SYMLINK%;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs
USERNAME=PMYLS
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 154 Stepping 4, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.4717)
OS uptime: 4 days 4:27 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 154 stepping 4 microcode 0x424, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv
Processor Information for processor 0
  Max Mhz: 1300, Current Mhz: 1300, Mhz Limit: 1300
Processor Information for processor 1
  Max Mhz: 1300, Current Mhz: 1300, Mhz Limit: 1300
Processor Information for processor 2
  Max Mhz: 1300, Current Mhz: 1300, Mhz Limit: 1300
Processor Information for processor 3
  Max Mhz: 1300, Current Mhz: 1300, Mhz Limit: 1300
Processor Information for processor 4
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 5
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 6
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 7
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 8
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 9
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 10
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900
Processor Information for processor 11
  Max Mhz: 900, Current Mhz: 900, Mhz Limit: 900

Memory: 4k page, system-wide physical 7916M (254M free)
TotalPageFile size 30861M (AvailPageFile size 208M)
current process WorkingSet (physical memory assigned to process): 115M, peak: 240M
current process commit charge ("private bytes"): 303M, peak: 317M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7) for windows-amd64 JRE (17.0.12+7), built on Jul 16 2024 22:08:24 by "admin" with MS VC++ 16.10 / 16.11 (VS2019)

END.
