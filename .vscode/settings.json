{"eslint.format.enable": false, "prettier.bracketSameLine": true, "editor.fontLigatures": true, "peacock.color": "#F1C50D", "workbench.colorCustomizations": {"activityBar.activeBackground": "#f5d13c", "activityBar.background": "#f5d13c", "activityBar.foreground": "#15202b", "activityBar.inactiveForeground": "#15202b99", "activityBarBadge.background": "#08987c", "activityBarBadge.foreground": "#e7e7e7", "commandCenter.border": "#15202b99", "sash.hoverBorder": "#f5d13c", "statusBar.background": "#f1c50d", "statusBar.foreground": "#15202b", "statusBarItem.hoverBackground": "#c19d0a", "statusBarItem.remoteBackground": "#f1c50d", "statusBarItem.remoteForeground": "#15202b", "titleBar.activeBackground": "#f1c50d", "titleBar.activeForeground": "#15202b", "titleBar.inactiveBackground": "#f1c50d99", "titleBar.inactiveForeground": "#15202b99"}, "java.configuration.updateBuildConfiguration": "interactive"}