JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 1
JvmtiExport can_post_on_exceptions 0
# 457 ciObject found
instanceKlass org/lombokweb/asm/ClassReader
ciInstanceKlass java/util/Collections$UnmodifiableCollection$1 1 1 60 1 7 1 1 7 1 7 1 100 1 7 1 1 12 1 1 1 1 1 1 1 1 12 9 1 12 10 1 1 12 9 1 7 11 12 9 1 1 12 11 1 1 1 12 11 1 1 100 10 1 1 1 12 11 1 1 1 1 1
ciInstanceKlass java/util/Arrays$ArrayList 1 1 119 1 7 1 1 7 1 100 1 100 1 7 1 1 7 1 1 1 5 0 1 1 1 1 1 1 1 12 10 1 7 1 1 12 10 7 12 9 1 1 1 1 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 100 1 1 12 10 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 1 1 1 100 1 12 10 1 1 1 1 100 1 1 12 11 1 1 1 1 100 1 12 11 1 1 1 1 12 10 1 1 1 12 10 1 1 1 1 1 1
ciInstanceKlass java/lang/Void 1 1 25 1 7 1 100 1 1 1 1 1 12 10 1 1 8 1 7 1 1 12 10 12 9 1 1
staticfield java/lang/Void TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/util/ArrayList$ListItr
ciInstanceKlass java/util/ArrayList$Itr 1 1 88 1 7 1 1 7 1 100 1 7 1 1 1 1 1 1 1 1 1 12 9 1 12 10 12 9 1 12 9 12 9 1 1 12 9 1 12 9 1 1 1 1 12 10 1 100 10 1 1 12 9 1 100 10 100 1 1 100 1 100 10 1 12 10 1 1 1 1 100 1 1 12 10 1 1 12 10 1 100 1 1 12 11 1 1 1 1 1
instanceKlass java/util/Collections$UnmodifiableList
instanceKlass java/util/Collections$UnmodifiableSet
ciInstanceKlass java/util/Collections$UnmodifiableCollection 1 1 110 1 7 1 1 7 1 7 1 100 1 100 1 1 7 1 1 5 0 1 1 1 1 1 1 1 12 10 1 100 10 12 9 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 10 1 1 1 1 12 10 1 1 1 100 10 1 1 1 1 12 11 1 1 1 1 1 1 1 1 12 11 1 1 1 1 1 1 12 11 1 1 1 12 11 1 12 11 1 1 1 1 1 1
instanceKlass org/eclipse/jdt/core/search/MethodNameMatch
instanceKlass org/eclipse/jdt/core/search/IJavaSearchScope
instanceKlass org/eclipse/jdt/internal/compiler/ASTVisitor
instanceKlass org/eclipse/jdt/core/search/TypeNameMatch
instanceKlass org/eclipse/jdt/core/search/SearchParticipant
instanceKlass org/eclipse/jdt/internal/core/search/IndexQueryRequestor
instanceKlass org/eclipse/jdt/core/search/SearchPattern
instanceKlass org/eclipse/jdt/core/search/IParallelizable
instanceKlass org/eclipse/jdt/internal/core/search/BasicSearchEngine
instanceKlass org/eclipse/jdt/core/ISourceRange
instanceKlass org/eclipse/jdt/core/ITypeParameter
instanceKlass org/eclipse/jdt/core/IAnnotation
instanceKlass org/eclipse/jdt/internal/core/AbstractModule
instanceKlass org/eclipse/jdt/core/IModuleDescription
instanceKlass org/eclipse/jdt/internal/core/NameLookup
instanceKlass org/eclipse/jface/text/IDocument
instanceKlass org/eclipse/jdt/internal/core/JavaModelCache$1
instanceKlass org/eclipse/jdt/internal/compiler/env/IBinaryInfo
instanceKlass org/eclipse/jdt/internal/core/JavaModelCache
instanceKlass org/eclipse/jdt/core/IType
instanceKlass org/eclipse/jdt/core/IAnnotatable
instanceKlass org/eclipse/jdt/core/IMember
instanceKlass org/eclipse/jdt/internal/core/hierarchy/HierarchyBuilder
instanceKlass org/eclipse/jdt/internal/core/hierarchy/TypeHierarchy
instanceKlass org/eclipse/jdt/core/ITypeHierarchy
instanceKlass org/eclipse/jdt/core/dom/ASTNode
instanceKlass org/eclipse/jdt/core/dom/StructuralPropertyDescriptor
instanceKlass org/eclipse/jdt/internal/core/dom/rewrite/RewriteEvent
instanceKlass org/eclipse/jdt/internal/core/dom/rewrite/RewriteEventStore
instanceKlass org/eclipse/jdt/core/dom/ASTVisitor
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessor
instanceKlass org/eclipse/jdt/internal/codeassist/CompletionEngine$1
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ParameterNonNullDefaultProvider
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$3
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$2
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReductionResult
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ElementValuePair
instanceKlass org/eclipse/jdt/internal/compiler/lookup/AnnotationBinding
instanceKlass org/eclipse/jdt/internal/compiler/env/IUpdatableModule
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeBindingVisitor
instanceKlass org/eclipse/jdt/internal/compiler/parser/ScannerHelper
instanceKlass org/eclipse/jdt/core/Signature
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeConstants$CloseMethodRecord
instanceKlass org/eclipse/jdt/internal/compiler/lookup/Substitution
instanceKlass org/eclipse/jdt/internal/core/IJavaElementRequestor
instanceKlass org/eclipse/jdt/core/CompletionRequestor
instanceKlass lombok/patcher/scripts/ReplaceMethodCallScript$1
instanceKlass org/eclipse/jdt/internal/core/INamingRequestor
instanceKlass org/eclipse/jdt/internal/codeassist/UnresolvedReferenceNameFinder$UnresolvedReferenceNameRequestor
instanceKlass org/eclipse/jdt/core/CompletionProposal
instanceKlass org/eclipse/jdt/core/search/SearchRequestor
instanceKlass org/eclipse/jdt/internal/codeassist/complete/CompletionNode
instanceKlass org/eclipse/jdt/internal/compiler/lookup/Scope
instanceKlass org/eclipse/jdt/core/compiler/IProblem
instanceKlass org/eclipse/jdt/core/CompletionContext
instanceKlass org/eclipse/jdt/internal/compiler/lookup/Binding
instanceKlass org/eclipse/jdt/internal/compiler/env/INameEnvironment
instanceKlass org/eclipse/jdt/internal/codeassist/MissingTypesGuesser$GuessedTypeRequestor
instanceKlass org/eclipse/jdt/internal/compiler/lookup/InvocationSite
instanceKlass org/eclipse/jdt/internal/compiler/ast/ASTNode
instanceKlass org/eclipse/jdt/internal/codeassist/impl/Engine
instanceKlass org/eclipse/jdt/internal/codeassist/RelevanceConstants
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeConstants
instanceKlass org/eclipse/jdt/internal/codeassist/ISearchRequestor
instanceKlass org/eclipse/jdt/internal/compiler/impl/ReferenceContext
instanceKlass org/eclipse/jdt/internal/compiler/ICompilerRequestor
instanceKlass org/eclipse/jdt/internal/compiler/Compiler
instanceKlass org/eclipse/jdt/internal/compiler/problem/ProblemSeverities
instanceKlass org/eclipse/jdt/internal/compiler/impl/ITypeRequestor
instanceKlass org/eclipse/jdt/internal/core/builder/ClasspathLocation
instanceKlass org/eclipse/jdt/core/IBuffer
instanceKlass org/eclipse/jdt/core/IBufferFactory
instanceKlass org/eclipse/jdt/internal/core/BufferManager
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$8
instanceKlass org/eclipse/jdt/internal/core/search/indexing/IndexNamesRegistry
instanceKlass org/eclipse/jdt/internal/compiler/util/SimpleLookupTable
instanceKlass org/eclipse/jdt/internal/core/index/IndexLocation
instanceKlass org/eclipse/jdt/internal/core/search/indexing/IndexRequest
instanceKlass org/eclipse/jdt/internal/compiler/parser/Parser
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeIds
instanceKlass org/eclipse/jdt/internal/compiler/ast/OperatorIds
instanceKlass org/eclipse/jdt/internal/compiler/parser/ConflictedParser
instanceKlass org/eclipse/jdt/internal/compiler/parser/ParserBasicInformation
instanceKlass org/eclipse/jdt/internal/compiler/parser/TerminalTokens
instanceKlass org/eclipse/jdt/internal/compiler/IProblemFactory
instanceKlass org/eclipse/jdt/internal/core/search/processing/JobManager
instanceKlass org/eclipse/jdt/internal/core/search/indexing/IIndexConstants
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$3
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$2
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$EclipsePreferencesListener
instanceKlass org/eclipse/jdt/core/IElementChangedListener
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessingState
instanceKlass org/eclipse/jdt/internal/core/ExternalFoldersManager
instanceKlass org/eclipse/jdt/internal/core/util/Util$Comparer
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$CompilationParticipants
instanceKlass org/eclipse/jdt/internal/core/BatchInitializationMonitor
instanceKlass org/eclipse/core/internal/jobs/JobQueue$2
instanceKlass org/eclipse/jdt/internal/core/JavaModelOperation
instanceKlass org/eclipse/jdt/internal/codeassist/ISelectionRequestor
instanceKlass org/eclipse/jdt/internal/core/JavaElementInfo
instanceKlass org/eclipse/jdt/core/IJavaModelStatus
instanceKlass org/eclipse/jdt/internal/compiler/env/IElementInfo
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$1
instanceKlass org/eclipse/jdt/internal/core/search/processing/IJob
instanceKlass org/eclipse/jdt/core/IClassFile
instanceKlass org/eclipse/jdt/core/IPackageFragment
instanceKlass org/eclipse/jdt/core/IAccessRule
instanceKlass org/eclipse/jdt/internal/core/util/LRUCache
instanceKlass org/eclipse/jdt/internal/core/search/IRestrictedAccessTypeRequestor
instanceKlass org/eclipse/jdt/core/IJavaElementDelta
instanceKlass org/eclipse/jdt/internal/compiler/util/SuffixConstants
instanceKlass org/eclipse/jdt/internal/compiler/env/ICompilationUnit
instanceKlass org/eclipse/jdt/internal/compiler/env/IDependent
instanceKlass org/eclipse/jdt/core/ICompilationUnit
instanceKlass org/eclipse/jdt/core/ISourceManipulation
instanceKlass org/eclipse/jdt/core/ITypeRoot
instanceKlass org/eclipse/jdt/core/ICodeAssist
instanceKlass org/eclipse/jdt/core/ISourceReference
instanceKlass org/lombokweb/asm/Handle
instanceKlass org/eclipse/jdt/core/IJavaProject
instanceKlass org/eclipse/jdt/core/IBufferChangedListener
instanceKlass org/eclipse/jdt/core/IPackageFragmentRoot
instanceKlass org/eclipse/jdt/internal/compiler/util/Util$Displayable
instanceKlass org/eclipse/jdt/core/IClasspathContainer
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager
instanceKlass java/util/function/ToDoubleFunction
instanceKlass org/eclipse/jdt/core/compiler/CharOperation
instanceKlass org/eclipse/jdt/internal/compiler/impl/CompilerOptions
instanceKlass org/eclipse/jdt/core/IClasspathAttribute
instanceKlass org/eclipse/jdt/internal/compiler/env/IModule
instanceKlass org/eclipse/jdt/core/IWorkingCopy
instanceKlass org/eclipse/jdt/core/IClasspathEntry
instanceKlass org/eclipse/jdt/core/search/TypeNameRequestor
instanceKlass org/eclipse/jdt/core/IRegion
instanceKlass org/eclipse/jdt/core/IJavaModel
instanceKlass org/eclipse/jdt/core/IParent
instanceKlass org/eclipse/jdt/core/IOpenable
instanceKlass org/eclipse/jdt/core/IJavaElement
instanceKlass org/eclipse/core/resources/IWorkspaceRunnable
instanceKlass org/eclipse/jdt/core/WorkingCopyOwner
instanceKlass java/nio/channels/AsynchronousByteChannel
instanceKlass java/nio/channels/AsynchronousChannel
instanceKlass java/net/SocketAddress
instanceKlass org/eclipse/jdt/ls/core/internal/lsp/JavaProtocolExtensions
instanceKlass org/eclipse/jdt/ls/core/internal/syntaxserver/IExtendedProtocol
instanceKlass org/eclipse/lsp4j/services/WorkspaceService
instanceKlass org/eclipse/lsp4j/services/TextDocumentService
instanceKlass org/eclipse/lsp4j/services/LanguageServer
instanceKlass org/eclipse/core/internal/events/NodeIDMap
instanceKlass org/eclipse/core/internal/events/ResourceDeltaInfo
instanceKlass org/eclipse/core/internal/dtree/NodeComparison
instanceKlass org/eclipse/core/internal/events/ResourceComparator
instanceKlass org/eclipse/core/internal/events/ResourceDeltaFactory
instanceKlass org/eclipse/jdt/ls/core/internal/BaseJDTLanguageServer
instanceKlass org/eclipse/core/resources/IMarkerDelta
instanceKlass java/net/Authenticator
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/PreferenceManager
instanceKlass org/eclipse/core/runtime/SubMonitor$RootInfo
instanceKlass org/eclipse/core/resources/team/ResourceRuleFactory
instanceKlass org/eclipse/m2e/core/internal/repository/IRepositoryIndexer
instanceKlass org/apache/maven/wagon/authentication/AuthenticationInfo
instanceKlass org/eclipse/m2e/core/internal/repository/IRepositoryDiscoverer
instanceKlass org/eclipse/m2e/core/internal/repository/RepositoryInfo
instanceKlass org/eclipse/m2e/core/repository/IRepository
instanceKlass org/eclipse/m2e/core/internal/repository/RepositoryRegistry
instanceKlass org/eclipse/m2e/core/repository/IRepositoryRegistry
instanceKlass org/eclipse/m2e/core/internal/project/WorkspaceStateWriter
instanceKlass org/eclipse/m2e/core/project/configurator/ILifecycleMapping
instanceKlass org/eclipse/m2e/core/project/ResolverConfiguration
instanceKlass org/eclipse/m2e/core/project/IProjectCreationListener
instanceKlass org/eclipse/m2e/core/project/ProjectImportConfiguration
instanceKlass org/eclipse/m2e/core/project/MavenProjectInfo
instanceKlass org/eclipse/aether/graph/DependencyNode
instanceKlass ch/qos/logback/classic/spi/EventArgUtil
instanceKlass ch/qos/logback/classic/spi/IThrowableProxy
instanceKlass ch/qos/logback/classic/spi/LoggingEvent
instanceKlass org/eclipse/m2e/core/internal/project/registry/MavenProjectManager
instanceKlass org/eclipse/m2e/core/project/IMavenProjectRegistry
instanceKlass org/eclipse/aether/graph/DependencyVisitor
instanceKlass org/eclipse/aether/graph/DependencyFilter
instanceKlass org/eclipse/aether/collection/DependencyGraphTransformer
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$VersionSelector
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeSelector
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$OptionalitySelector
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeDeriver
instanceKlass javax/xml/transform/Source
instanceKlass javax/xml/transform/Result
instanceKlass org/w3c/dom/Node
instanceKlass org/eclipse/m2e/core/embedder/MavenModelManager
instanceKlass org/eclipse/m2e/core/project/configurator/ILifecycleMappingConfiguration
instanceKlass org/eclipse/m2e/core/internal/project/ProjectConfigurationManager
instanceKlass org/eclipse/m2e/core/project/IProjectConfigurationManager
instanceKlass org/eclipse/core/runtime/jobs/IJobFunction
instanceKlass org/eclipse/m2e/core/project/MavenUpdateRequest
instanceKlass org/eclipse/m2e/core/internal/project/registry/ProjectRegistryReader$MavenProjectManagerImplReplace
instanceKlass org/eclipse/m2e/core/internal/project/registry/ProjectRegistryReader$IFileReplace
instanceKlass org/eclipse/m2e/core/internal/project/registry/ProjectRegistryReader$IPathReplace
instanceKlass java/util/HashMap$UnsafeHolder
instanceKlass java/io/SerialCallbackContext
instanceKlass java/io/ObjectInputStream$GetField
instanceKlass java/io/ObjectStreamClass$ClassDataSlot
instanceKlass java/io/ObjectStreamClass$FieldReflector
instanceKlass java/io/ObjectStreamClass$FieldReflectorKey
instanceKlass java/io/ObjectStreamClass$2
instanceKlass java/io/ClassCache
instanceKlass java/io/ObjectStreamClass$Caches
instanceKlass sun/reflect/misc/ReflectUtil
instanceKlass java/io/ObjectStreamClass
instanceKlass java/io/Bits
instanceKlass jdk/internal/access/JavaObjectInputFilterAccess
instanceKlass java/io/ObjectInputFilter$Config$BuiltinFilterFactory
instanceKlass java/io/ObjectInputFilter
instanceKlass java/io/ObjectInputFilter$Config
instanceKlass java/io/ObjectInputStream$ValidationList
instanceKlass java/io/ObjectInputStream$HandleTable$HandleList
instanceKlass java/io/ObjectInputStream$HandleTable
instanceKlass jdk/internal/access/JavaObjectInputStreamReadString
instanceKlass jdk/internal/access/JavaObjectInputStreamAccess
instanceKlass org/eclipse/m2e/core/project/IMavenProjectChangedListener
instanceKlass org/apache/maven/execution/MavenExecutionRequest
instanceKlass org/apache/maven/project/ProjectBuildingResult
instanceKlass org/eclipse/core/runtime/SubMonitor
instanceKlass org/eclipse/m2e/core/internal/project/registry/DependencyResolutionContext
instanceKlass org/eclipse/m2e/core/internal/project/registry/MavenProjectFacade
instanceKlass com/google/common/cache/LocalCache$AbstractReferenceEntry
instanceKlass java/util/concurrent/atomic/AtomicReferenceArray
instanceKlass com/google/common/cache/LocalCache$LoadingValueReference
instanceKlass com/google/common/cache/Weigher
instanceKlass com/google/common/base/Predicate
instanceKlass com/google/common/base/Equivalence
instanceKlass java/util/function/BiPredicate
instanceKlass com/google/common/base/MoreObjects
instanceKlass com/google/common/cache/LocalCache$1
instanceKlass com/google/common/cache/ReferenceEntry
instanceKlass com/google/common/cache/LocalCache$ValueReference
instanceKlass com/google/common/cache/LocalCache$LocalManualCache
instanceKlass com/google/common/cache/CacheLoader
instanceKlass org/eclipse/m2e/core/internal/project/registry/MavenProjectCache$CacheLine
instanceKlass com/google/common/cache/RemovalListener
instanceKlass com/google/common/cache/CacheBuilder$2
instanceKlass com/google/common/base/Preconditions
instanceKlass com/google/common/cache/CacheStats
instanceKlass com/google/common/base/Suppliers$SupplierOfInstance
instanceKlass com/google/common/base/Suppliers
instanceKlass com/google/common/cache/CacheBuilder$1
instanceKlass com/google/common/cache/AbstractCache$StatsCounter
instanceKlass com/google/common/base/Ticker
instanceKlass com/google/common/base/Supplier
instanceKlass com/google/common/cache/CacheBuilder
instanceKlass com/google/common/cache/LoadingCache
instanceKlass com/google/common/base/Function
instanceKlass com/google/common/cache/Cache
instanceKlass org/eclipse/m2e/core/internal/preferences/MavenPreferenceConstants
instanceKlass org/eclipse/m2e/core/embedder/ILocalRepositoryListener
instanceKlass org/apache/maven/wagon/proxy/ProxyInfo
instanceKlass org/apache/maven/execution/MavenSession
instanceKlass org/apache/maven/project/ProjectBuildingRequest
instanceKlass org/apache/maven/model/ModelBase
instanceKlass org/eclipse/m2e/core/internal/embedder/MavenExecutionContext
instanceKlass org/apache/maven/model/ConfigurationContainer
instanceKlass org/apache/maven/model/InputLocationTracker
instanceKlass org/apache/maven/settings/TrackableBase
instanceKlass org/apache/maven/lifecycle/MavenExecutionPlan
instanceKlass org/apache/maven/plugin/MojoExecution
instanceKlass org/eclipse/m2e/core/internal/embedder/MavenImpl$MavenSettings
instanceKlass org/apache/maven/model/building/ModelProblemCollectorRequest
instanceKlass org/apache/maven/artifact/Artifact
instanceKlass org/eclipse/m2e/core/embedder/ICallable
instanceKlass org/eclipse/m2e/core/embedder/ISettingsChangeListener
instanceKlass org/eclipse/sisu/inject/MildKeys
instanceKlass org/eclipse/sisu/inject/Weak
instanceKlass java/text/DigitList
instanceKlass sun/util/locale/provider/CalendarDataUtility$CalendarWeekParameterGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool$LocalizedObjectGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool
instanceKlass java/util/Calendar$Builder
instanceKlass com/google/inject/matcher/AbstractMatcher
instanceKlass com/google/inject/matcher/Matcher
instanceKlass com/google/inject/Module
instanceKlass com/google/inject/spi/TypeConverter
instanceKlass org/codehaus/plexus/component/configurator/converters/ParameterizedConfigurationConverter
instanceKlass org/codehaus/plexus/component/configurator/converters/AbstractConfigurationConverter
instanceKlass org/codehaus/plexus/component/configurator/converters/ConfigurationConverter
instanceKlass org/codehaus/plexus/component/configurator/converters/lookup/DefaultConverterLookup
instanceKlass org/apache/maven/project/MavenProject
instanceKlass org/eclipse/m2e/core/internal/markers/SourceLocation
instanceKlass org/eclipse/m2e/core/internal/markers/MavenProblemInfo
instanceKlass org/eclipse/aether/artifact/Artifact
instanceKlass org/eclipse/m2e/core/internal/markers/MavenMarkerManager
instanceKlass org/apache/maven/extension/internal/CoreExtensionEntry
instanceKlass org/eclipse/m2e/core/internal/embedder/MavenProperties
instanceKlass org/apache/felix/scr/impl/ComponentRegistry$2
instanceKlass org/eclipse/m2e/core/internal/preferences/MavenConfigurationImpl$1
instanceKlass org/eclipse/core/runtime/preferences/IPreferenceFilter
instanceKlass org/apache/felix/scr/impl/ComponentRegistry$Entry
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogEntryImpl
instanceKlass org/eclipse/equinox/log/ExtendedLogEntry
instanceKlass org/eclipse/osgi/internal/log/Arguments
instanceKlass java/lang/StackTraceElement$HashedModules
instanceKlass java/text/AttributedCharacterIterator$Attribute
instanceKlass java/text/FieldPosition
instanceKlass java/text/Format
instanceKlass org/eclipse/m2e/core/internal/embedder/EclipseLogger
instanceKlass org/codehaus/plexus/logging/Logger
instanceKlass org/codehaus/plexus/logging/AbstractLoggerManager
instanceKlass org/codehaus/plexus/logging/LoggerManager
instanceKlass org/eclipse/m2e/core/internal/embedder/IMavenPlexusContainer
instanceKlass org/codehaus/plexus/classworlds/ClassWorldListener
instanceKlass org/codehaus/plexus/classworlds/ClassWorld
instanceKlass org/codehaus/plexus/PlexusContainer
instanceKlass org/apache/maven/plugin/version/PluginVersionRequest
instanceKlass org/codehaus/plexus/component/configurator/expression/ExpressionEvaluator
instanceKlass org/codehaus/plexus/configuration/PlexusConfiguration
instanceKlass org/apache/maven/settings/crypto/SettingsDecryptionRequest
instanceKlass org/apache/maven/settings/building/SettingsBuildingRequest
instanceKlass org/apache/maven/execution/MavenExecutionResult
instanceKlass org/apache/maven/model/building/ModelBuildingRequest
instanceKlass org/eclipse/aether/RepositorySystemSession
instanceKlass org/codehaus/plexus/component/configurator/converters/lookup/ConverterLookup
instanceKlass org/eclipse/m2e/core/internal/embedder/MavenImpl
instanceKlass org/eclipse/m2e/core/embedder/IMavenConfigurationChangeListener
instanceKlass org/eclipse/m2e/core/internal/preferences/MavenConfigurationImpl
instanceKlass org/eclipse/m2e/core/internal/project/registry/MavenProjectCache
instanceKlass org/eclipse/m2e/core/internal/project/registry/ProjectRegistryReader
instanceKlass org/eclipse/m2e/core/internal/embedder/PlexusContainerManager
instanceKlass org/eclipse/m2e/core/internal/markers/IMavenMarkerManager
instanceKlass org/eclipse/m2e/core/internal/project/registry/BasicProjectRegistry
instanceKlass org/eclipse/m2e/core/project/IProjectConfiguration
instanceKlass org/eclipse/m2e/core/internal/project/registry/AbstractMavenDependencyResolver
instanceKlass org/eclipse/aether/repository/WorkspaceReader
instanceKlass org/apache/maven/artifact/repository/MavenArtifactRepository
instanceKlass org/apache/maven/artifact/repository/ArtifactRepository
instanceKlass org/eclipse/m2e/core/embedder/IMavenExecutionContext
instanceKlass org/eclipse/m2e/core/internal/project/registry/IProjectRegistry
instanceKlass org/eclipse/m2e/core/internal/project/registry/Capability
instanceKlass org/eclipse/m2e/core/project/IMavenProjectFacade
instanceKlass org/eclipse/m2e/core/embedder/IMavenExecutableLocation
instanceKlass org/eclipse/m2e/core/embedder/IMaven
instanceKlass org/eclipse/m2e/core/embedder/IComponentLookup
instanceKlass org/eclipse/m2e/core/embedder/IMavenConfiguration
instanceKlass org/eclipse/m2e/core/internal/project/registry/ProjectRegistryManager
instanceKlass ch/qos/logback/classic/util/LoggerNameUtil
instanceKlass org/slf4j/helpers/Reporter
instanceKlass ch/qos/logback/classic/util/LogbackMDCAdapter
instanceKlass ch/qos/logback/core/util/CachingDateFormatter$CacheTuple
instanceKlass ch/qos/logback/core/util/CachingDateFormatter
instanceKlass ch/qos/logback/core/util/StatusPrinter
instanceKlass ch/qos/logback/core/status/StatusUtil
instanceKlass ch/qos/logback/core/Appender
instanceKlass ch/qos/logback/core/spi/FilterAttachable
instanceKlass ch/qos/logback/core/spi/ContextAwareBase
instanceKlass ch/qos/logback/classic/util/ClassicEnvUtil
instanceKlass ch/qos/logback/core/status/StatusListener
instanceKlass ch/qos/logback/core/util/StatusListenerConfigHelper
instanceKlass ch/qos/logback/core/status/StatusBase
instanceKlass ch/qos/logback/core/util/EnvUtil
instanceKlass java/security/Policy$PolicyInfo
instanceKlass ch/qos/logback/core/util/Loader$1
instanceKlass ch/qos/logback/core/util/OptionHelper
instanceKlass ch/qos/logback/core/util/Loader
instanceKlass ch/qos/logback/classic/spi/Configurator
instanceKlass ch/qos/logback/core/spi/ContextAwareImpl
instanceKlass ch/qos/logback/classic/util/ContextInitializer$1
instanceKlass ch/qos/logback/core/spi/ContextAware
instanceKlass ch/qos/logback/classic/util/ContextInitializer
instanceKlass ch/qos/logback/classic/Level
instanceKlass ch/qos/logback/classic/spi/ILoggingEvent
instanceKlass ch/qos/logback/core/spi/DeferredProcessingAware
instanceKlass org/slf4j/spi/LoggingEventBuilder
instanceKlass ch/qos/logback/classic/Logger
instanceKlass ch/qos/logback/core/spi/AppenderAttachable
instanceKlass org/slf4j/spi/LoggingEventAware
instanceKlass org/slf4j/spi/LocationAwareLogger
instanceKlass ch/qos/logback/classic/spi/LoggerContextVO
instanceKlass ch/qos/logback/core/spi/LogbackLock
instanceKlass ch/qos/logback/core/helpers/CyclicBuffer
instanceKlass ch/qos/logback/core/BasicStatusManager
instanceKlass ch/qos/logback/core/status/Status
instanceKlass ch/qos/logback/core/status/StatusManager
instanceKlass ch/qos/logback/core/ContextBase
instanceKlass ch/qos/logback/core/spi/LifeCycle
instanceKlass ch/qos/logback/core/Context
instanceKlass ch/qos/logback/core/spi/PropertyContainer
instanceKlass org/apache/aries/spifly/Util$7
instanceKlass org/apache/aries/spifly/Util$5
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$1
instanceKlass org/eclipse/osgi/container/ModuleResolutionReport
instanceKlass org/apache/felix/resolver/WireImpl
instanceKlass org/apache/felix/resolver/util/ArrayMap$1$1
instanceKlass org/apache/felix/resolver/ResolverImpl$UsedBlames
instanceKlass org/apache/felix/resolver/ResolverImpl$6
instanceKlass org/apache/felix/resolver/ResolverImpl$5
instanceKlass org/apache/felix/resolver/ResolverImpl$4
instanceKlass org/apache/felix/resolver/ResolverImpl$Blame
instanceKlass org/apache/felix/resolver/ResolverImpl$3
instanceKlass org/apache/felix/resolver/ResolverImpl$Packages
instanceKlass org/apache/felix/resolver/WrappedRequirement
instanceKlass org/apache/felix/resolver/ResolverImpl$WireCandidate
instanceKlass org/apache/felix/resolver/ResolverImpl$EnhancedExecutor$1
instanceKlass org/apache/felix/resolver/ResolverImpl$1Computer
instanceKlass org/apache/felix/resolver/ResolverImpl$EnhancedExecutor
instanceKlass org/apache/felix/resolver/WrappedResource
instanceKlass org/apache/felix/resolver/util/OpenHashMap$MapEntry
instanceKlass org/apache/felix/resolver/util/OpenHashMap$MapIterator
instanceKlass org/apache/felix/resolver/util/OpenHashMap$1
instanceKlass org/apache/felix/resolver/Candidates$PopulateResult
instanceKlass org/apache/felix/resolver/util/CopyOnWriteSet
instanceKlass org/apache/felix/resolver/util/CandidateSelector
instanceKlass org/apache/felix/resolver/Util
instanceKlass java/lang/StrictMath
instanceKlass org/osgi/service/resolver/HostedCapability
instanceKlass org/apache/felix/resolver/Candidates
instanceKlass org/apache/felix/resolver/ResolverImpl$ResolveSession
instanceKlass org/apache/felix/resolver/ResolverImpl$DumbExecutor
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$CoreResolverHookFactory$CoreResolverHook
instanceKlass org/eclipse/osgi/report/resolution/ResolutionReport$Listener
instanceKlass org/eclipse/osgi/container/ModuleResolutionReport$Builder
instanceKlass org/osgi/service/resolver/ResolveContext
instanceKlass org/eclipse/osgi/container/ModuleDatabase$1
instanceKlass org/eclipse/osgi/container/ModuleContainer$ResolutionLock$Permits
instanceKlass org/slf4j/helpers/Util
instanceKlass org/slf4j/helpers/NOPMDCAdapter
instanceKlass org/slf4j/helpers/NOPLoggerFactory
instanceKlass org/slf4j/helpers/NOP_FallbackServiceProvider
instanceKlass org/slf4j/helpers/ThreadLocalMapOfStacks
instanceKlass org/objectweb/asm/tree/ParameterNode
instanceKlass org/slf4j/helpers/BasicMDCAdapter
instanceKlass org/slf4j/Marker
instanceKlass org/slf4j/helpers/BasicMarkerFactory
instanceKlass org/slf4j/Logger
instanceKlass org/slf4j/helpers/SubstituteLoggerFactory
instanceKlass org/slf4j/ILoggerFactory
instanceKlass org/slf4j/spi/MDCAdapter
instanceKlass org/slf4j/IMarkerFactory
instanceKlass org/slf4j/helpers/SubstituteServiceProvider
instanceKlass org/slf4j/event/LoggingEvent
instanceKlass org/slf4j/LoggerFactory
instanceKlass java/util/function/UnaryOperator
instanceKlass org/eclipse/osgi/container/builders/OSGiManifestBuilderFactory
instanceKlass org/objectweb/asm/Handler
instanceKlass org/objectweb/asm/tree/TryCatchBlockNode
instanceKlass org/objectweb/asm/Handle
instanceKlass org/objectweb/asm/Edge
instanceKlass org/objectweb/asm/tree/LocalVariableNode
instanceKlass org/objectweb/asm/tree/InsnList
instanceKlass org/objectweb/asm/tree/Util
instanceKlass org/objectweb/asm/tree/AbstractInsnNode
instanceKlass org/objectweb/asm/commons/Method
instanceKlass org/objectweb/asm/Label
instanceKlass org/objectweb/asm/Frame
instanceKlass org/objectweb/asm/Context
instanceKlass org/objectweb/asm/Attribute
instanceKlass org/objectweb/asm/Type
instanceKlass org/objectweb/asm/ByteVector
instanceKlass org/objectweb/asm/Symbol
instanceKlass org/objectweb/asm/SymbolTable
instanceKlass org/objectweb/asm/MethodVisitor
instanceKlass org/objectweb/asm/FieldVisitor
instanceKlass org/objectweb/asm/ModuleVisitor
instanceKlass org/objectweb/asm/AnnotationVisitor
instanceKlass org/objectweb/asm/RecordComponentVisitor
instanceKlass org/objectweb/asm/ClassReader
instanceKlass org/eclipse/m2e/core/internal/URLConnectionCaches
instanceKlass org/eclipse/m2e/core/internal/jobs/IBackgroundProcessingQueue
instanceKlass org/eclipse/core/resources/IResourceChangeEvent
instanceKlass java/util/concurrent/atomic/Striped64$1
instanceKlass jdk/internal/util/random/RandomSupport
instanceKlass org/eclipse/core/internal/runtime/Log
instanceKlass org/eclipse/core/internal/preferences/BundleStateScope
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$Resolved
instanceKlass org/apache/felix/scr/impl/inject/field/FieldUtils$FieldSearchResult
instanceKlass org/apache/felix/scr/impl/inject/field/FieldUtils$1
instanceKlass org/apache/felix/scr/impl/inject/field/FieldUtils
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$1
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$ReferenceMethodImpl
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$NotResolved
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$State
instanceKlass org/apache/felix/scr/impl/inject/InitReferenceMethod
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler
instanceKlass org/apache/felix/scr/impl/inject/field/FieldMethods
instanceKlass org/eclipse/core/internal/resources/CheckMissingNaturesListener
instanceKlass org/eclipse/core/internal/resources/ResourceChangeListenerRegistrar
instanceKlass org/eclipse/core/internal/resources/Rules
instanceKlass org/eclipse/core/internal/filesystem/FileStoreUtil
instanceKlass org/eclipse/core/internal/resources/AliasManager$LocationMap
instanceKlass org/eclipse/core/internal/resources/AliasManager
instanceKlass org/eclipse/core/resources/refresh/IRefreshMonitor
instanceKlass org/eclipse/core/internal/refresh/MonitorManager
instanceKlass org/eclipse/core/resources/IResourceDeltaVisitor
instanceKlass org/eclipse/core/resources/IPathVariableChangeListener
instanceKlass org/eclipse/core/internal/refresh/RefreshManager
instanceKlass org/eclipse/core/resources/refresh/IRefreshResult
instanceKlass org/eclipse/core/internal/resources/ProjectContentTypes
instanceKlass org/eclipse/core/internal/utils/Cache
instanceKlass org/eclipse/core/internal/resources/ContentDescriptionManager
instanceKlass java/util/function/LongUnaryOperator
instanceKlass org/eclipse/core/internal/jobs/JobChangeEvent
instanceKlass org/eclipse/core/internal/resources/CharsetDeltaJob$ICharsetListenerFilter
instanceKlass org/eclipse/core/runtime/PerformanceStats
instanceKlass org/eclipse/core/internal/events/ResourceStats
instanceKlass org/eclipse/core/internal/events/ResourceChangeListenerList$ListenerEntry
instanceKlass org/eclipse/core/internal/resources/CharsetManager$ResourceChangeListener
instanceKlass org/eclipse/core/resources/IResourceChangeListener
instanceKlass org/eclipse/core/internal/resources/CharsetManager
instanceKlass org/eclipse/core/internal/localstore/Bucket$Entry
instanceKlass org/eclipse/core/internal/localstore/BucketTree
instanceKlass org/eclipse/core/internal/localstore/Bucket$Visitor
instanceKlass org/eclipse/core/internal/localstore/Bucket
instanceKlass org/eclipse/core/internal/properties/PropertyManager2
instanceKlass org/eclipse/core/internal/events/LifecycleEvent
instanceKlass org/eclipse/core/internal/resources/LinkDescription
instanceKlass java/lang/Short$ShortCache
instanceKlass com/sun/jna/NativeMappedConverter
instanceKlass com/sun/jna/Klass
instanceKlass com/sun/jna/Native$Buffers
instanceKlass com/sun/jna/VarArgsChecker
instanceKlass com/sun/jna/NativeLibrary$NativeLibraryDisposer
instanceKlass com/sun/jna/NativeLibrary$1
instanceKlass com/sun/jna/SymbolProvider
instanceKlass com/sun/jna/NativeLibrary
instanceKlass org/eclipse/core/internal/filesystem/local/Win32Handler$FileAPIh
instanceKlass com/sun/jna/Memory$MemoryDisposer
instanceKlass com/sun/jna/internal/Cleaner$Cleanable
instanceKlass com/sun/jna/internal/Cleaner
instanceKlass com/sun/jna/WeakMemoryHolder
instanceKlass org/eclipse/core/filesystem/provider/FileInfo
instanceKlass com/sun/jna/Structure$StructField
instanceKlass com/sun/jna/Structure$LayoutInfo
instanceKlass java/lang/Class$AnnotationData
instanceKlass java/lang/annotation/Documented
instanceKlass com/sun/jna/Structure$FieldOrder
instanceKlass com/sun/jna/platform/win32/WinBase
instanceKlass com/sun/jna/platform/win32/BaseTSD
instanceKlass com/sun/jna/platform/win32/WinDef
instanceKlass com/sun/jna/Library
instanceKlass com/sun/jna/win32/W32APITypeMapper$2
instanceKlass com/sun/jna/DefaultTypeMapper$Entry
instanceKlass com/sun/jna/win32/W32APITypeMapper$1
instanceKlass com/sun/jna/TypeConverter
instanceKlass com/sun/jna/DefaultTypeMapper
instanceKlass com/sun/jna/TypeMapper
instanceKlass com/sun/jna/Structure$ByReference
instanceKlass com/sun/jna/Native$2
instanceKlass com/sun/jna/Structure$FFIType$FFITypes
instanceKlass com/sun/jna/Native$ffi_callback
instanceKlass com/sun/jna/JNIEnv
instanceKlass com/sun/jna/PointerType
instanceKlass com/sun/jna/NativeMapped
instanceKlass com/sun/jna/WString
instanceKlass com/sun/jna/win32/DLLCallback
instanceKlass com/sun/jna/CallbackProxy
instanceKlass com/sun/jna/Callback
instanceKlass com/sun/jna/Structure$ByValue
instanceKlass jdk/internal/loader/NativeLibraries$Unloader
instanceKlass java/io/FileOutputStream$1
instanceKlass sun/security/provider/AbstractDrbg$NonceProvider
instanceKlass sun/nio/fs/BasicFileAttributesHolder
instanceKlass sun/nio/fs/WindowsDirectoryStream$WindowsDirectoryIterator
instanceKlass sun/nio/fs/WindowsDirectoryStream
instanceKlass java/nio/file/DirectoryStream
instanceKlass java/nio/file/Files$AcceptAllFilter
instanceKlass java/nio/file/DirectoryStream$Filter
instanceKlass java/net/NetworkInterface$1
instanceKlass java/net/DefaultInterface
instanceKlass java/net/Inet6Address$Inet6AddressHolder
instanceKlass java/net/InetAddress$PlatformNameService
instanceKlass java/net/InetAddress$NameService
instanceKlass java/net/Inet6AddressImpl
instanceKlass java/net/InetAddressImpl
instanceKlass java/net/InetAddressImplFactory
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Node
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Index
instanceKlass java/util/concurrent/ConcurrentNavigableMap
instanceKlass java/net/InetAddress$InetAddressHolder
instanceKlass java/net/InetAddress$1
instanceKlass jdk/internal/access/JavaNetInetAddressAccess
instanceKlass java/net/InetAddress
instanceKlass java/net/InterfaceAddress
instanceKlass java/net/NetworkInterface
instanceKlass sun/security/provider/SeedGenerator$1
instanceKlass sun/security/provider/SeedGenerator
instanceKlass sun/security/provider/AbstractDrbg$SeederHolder
instanceKlass java/security/DrbgParameters$NextBytes
instanceKlass sun/security/provider/EntropySource
instanceKlass sun/security/provider/AbstractDrbg
instanceKlass java/security/DrbgParameters$Instantiation
instanceKlass java/security/DrbgParameters
instanceKlass sun/security/provider/MoreDrbgParameters
instanceKlass java/security/SecureRandomSpi
instanceKlass java/security/SecureRandomParameters
instanceKlass java/io/File$TempDirectory
instanceKlass com/sun/jna/Native$5
instanceKlass com/sun/jna/Platform
instanceKlass com/sun/jna/Native$1
instanceKlass com/sun/jna/Callback$UncaughtExceptionHandler
instanceKlass com/sun/jna/Native
instanceKlass com/sun/jna/Version
instanceKlass com/sun/jna/FromNativeContext
instanceKlass com/sun/jna/FromNativeConverter
instanceKlass com/sun/jna/ToNativeConverter
instanceKlass com/sun/jna/ToNativeContext
instanceKlass com/sun/jna/Structure
instanceKlass com/sun/jna/Pointer
instanceKlass org/eclipse/core/internal/filesystem/local/NativeHandler
instanceKlass org/eclipse/core/internal/filesystem/local/LocalFileNativesManager
instanceKlass org/eclipse/core/resources/FileInfoMatcherDescription
instanceKlass org/eclipse/core/internal/resources/FilterDescription
instanceKlass javax/xml/parsers/DocumentBuilder
instanceKlass javax/xml/parsers/DocumentBuilderFactory
instanceKlass org/eclipse/core/internal/runtime/XmlProcessorFactory
instanceKlass org/eclipse/core/internal/resources/IModelObjectConstants
instanceKlass org/eclipse/core/filesystem/URIUtil
instanceKlass java/util/AbstractMap$2$1
instanceKlass org/eclipse/core/resources/variableresolvers/PathVariableResolver
instanceKlass org/eclipse/core/internal/resources/ProjectVariableProviderManager$Descriptor
instanceKlass org/eclipse/core/internal/resources/ProjectVariableProviderManager
instanceKlass org/eclipse/core/internal/resources/ProjectPathVariableManager
instanceKlass org/eclipse/jdt/ls/core/internal/filesystem/JLSFsUtils
instanceKlass sun/nio/fs/WindowsUriSupport
instanceKlass org/eclipse/core/filesystem/IFileStore
instanceKlass org/eclipse/jdt/ls/core/internal/filesystem/JDTLSFilesystemActivator$1
instanceKlass org/eclipse/jdt/ls/core/internal/filesystem/JDTLSFilesystemActivator
instanceKlass org/eclipse/core/filesystem/IFileSystem
instanceKlass org/eclipse/core/internal/filesystem/InternalFileSystemCore
instanceKlass org/eclipse/core/filesystem/EFS
instanceKlass org/eclipse/core/internal/localstore/FileStoreRoot
instanceKlass org/eclipse/core/filesystem/IFileInfo
instanceKlass org/eclipse/core/internal/resources/MarkerAttributeMap
instanceKlass org/eclipse/core/internal/resources/MarkerSet
instanceKlass org/eclipse/core/internal/resources/MarkerReader
instanceKlass org/eclipse/core/internal/watson/ElementTree$ChildIDsCache
instanceKlass java/io/FilenameFilter
instanceKlass org/eclipse/core/internal/utils/ObjectMap
instanceKlass org/eclipse/core/internal/dtree/DataTreeLookup
instanceKlass org/eclipse/core/internal/dtree/DataTreeReader
instanceKlass org/eclipse/core/internal/watson/ElementTreeReader$1
instanceKlass org/eclipse/core/internal/dtree/IDataFlattener
instanceKlass org/eclipse/core/internal/watson/ElementTreeReader
instanceKlass org/eclipse/core/resources/IFileState
instanceKlass org/eclipse/core/internal/events/BuilderPersistentInfo
instanceKlass org/eclipse/core/internal/resources/SyncInfoReader
instanceKlass org/eclipse/core/internal/resources/WorkspaceTreeReader
instanceKlass org/eclipse/core/resources/ISaveContext
instanceKlass org/eclipse/core/resources/ISavedState
instanceKlass org/eclipse/core/internal/resources/SaveManager
instanceKlass org/eclipse/core/internal/watson/IElementInfoFlattener
instanceKlass org/eclipse/core/internal/resources/SyncInfoWriter
instanceKlass org/eclipse/core/internal/resources/Synchronizer
instanceKlass org/eclipse/core/internal/resources/MarkerWriter
instanceKlass org/eclipse/core/internal/resources/MarkerDeltaManager
instanceKlass org/eclipse/core/internal/resources/MarkerTypeDefinitionCache$MarkerTypeDefinition
instanceKlass org/eclipse/core/internal/resources/MarkerTypeDefinitionCache
instanceKlass org/eclipse/core/internal/resources/MarkerInfo
instanceKlass org/eclipse/core/internal/resources/IMarkerSetElement
instanceKlass org/eclipse/core/internal/resources/MarkerManager
instanceKlass org/eclipse/core/internal/events/ResourceChangeListenerList
instanceKlass org/eclipse/core/internal/events/NotificationManager
instanceKlass java/util/stream/SortedOps
instanceKlass org/eclipse/core/internal/events/BuildManager$DeltaCache
instanceKlass org/eclipse/core/resources/ICommand
instanceKlass org/eclipse/core/resources/IResourceDelta
instanceKlass org/eclipse/core/internal/events/InternalBuilder
instanceKlass org/eclipse/core/resources/IBuildContext
instanceKlass org/eclipse/core/internal/events/BuildManager
instanceKlass org/eclipse/core/internal/resources/FilterTypeManager$1
instanceKlass org/eclipse/core/internal/resources/FilterDescriptor
instanceKlass org/eclipse/core/resources/IFilterMatcherDescriptor
instanceKlass org/eclipse/core/internal/resources/FilterTypeManager
instanceKlass org/eclipse/core/resources/IProjectNatureDescriptor
instanceKlass org/eclipse/core/internal/resources/NatureManager
instanceKlass org/eclipse/core/internal/events/ILifecycleListener
instanceKlass org/eclipse/core/internal/resources/PathVariableManager
instanceKlass org/eclipse/core/internal/preferences/PreferencesService$1
instanceKlass java/util/function/IntFunction
instanceKlass org/eclipse/core/internal/localstore/RefreshLocalVisitor
instanceKlass org/eclipse/core/internal/localstore/ILocalStoreConstants
instanceKlass org/eclipse/core/internal/localstore/IHistoryStore
instanceKlass org/eclipse/core/internal/localstore/IUnifiedTreeVisitor
instanceKlass org/eclipse/core/internal/localstore/FileSystemResourceManager
instanceKlass org/eclipse/core/runtime/jobs/MultiRule
instanceKlass org/eclipse/core/internal/jobs/OrderedLock
instanceKlass org/eclipse/core/internal/runtime/LocalizationUtils
instanceKlass org/eclipse/core/runtime/Status
instanceKlass org/eclipse/core/internal/resources/WorkManager$NotifyRule
instanceKlass org/eclipse/core/internal/resources/WorkManager
instanceKlass org/eclipse/core/runtime/NullProgressMonitor
instanceKlass org/eclipse/core/runtime/preferences/IExportedPreferences
instanceKlass org/eclipse/core/runtime/Preferences$IPropertyChangeListener
instanceKlass org/eclipse/core/runtime/preferences/IEclipsePreferences$INodeChangeListener
instanceKlass org/eclipse/core/runtime/preferences/IEclipsePreferences$IPreferenceChangeListener
instanceKlass org/eclipse/core/runtime/Preferences
instanceKlass lombok/eclipse/agent/PatchFixesShadowLoaded
instanceKlass lombok/core/LombokNode
instanceKlass lombok/core/DiagnosticsReceiver
instanceKlass lombok/launch/PatchFixesHider$Util
instanceKlass lombok/launch/PatchFixesHider$LombokDeps
instanceKlass lombok/launch/ClassFileMetaData
instanceKlass lombok/launch/PackageShader
instanceKlass java/lang/invoke/ClassSpecializer$Factory$1Var
instanceKlass sun/invoke/util/ValueConversions$1
instanceKlass sun/invoke/util/ValueConversions$WrapperCache
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccessor
instanceKlass java/lang/invoke/MethodHandleImpl$2
instanceKlass java/lang/invoke/MethodHandleImpl$LoopClauses
instanceKlass java/lang/invoke/MethodHandleImpl$CasesHolder
instanceKlass java/io/ObjectOutput
instanceKlass java/io/ObjectStreamConstants
instanceKlass java/io/ObjectInput
instanceKlass org/eclipse/core/internal/runtime/Product
instanceKlass org/eclipse/core/runtime/IProduct
instanceKlass lombok/patcher/scripts/WrapReturnValuesScript$1
instanceKlass org/eclipse/osgi/internal/url/URLStreamHandlerProxy$1
instanceKlass org/eclipse/osgi/internal/url/URLStreamHandlerSetter
instanceKlass org/eclipse/osgi/internal/url/NullURLStreamHandlerService
instanceKlass org/osgi/service/url/URLStreamHandlerSetter
instanceKlass org/eclipse/equinox/internal/app/ProductExtensionBranding
instanceKlass org/eclipse/core/internal/runtime/FindSupport
instanceKlass org/eclipse/core/runtime/FileLocator
instanceKlass org/eclipse/core/runtime/SafeRunner
instanceKlass org/eclipse/core/runtime/IExecutableExtensionFactory
instanceKlass org/eclipse/core/runtime/IExecutableExtension
instanceKlass org/eclipse/core/runtime/preferences/AbstractPreferenceInitializer
instanceKlass org/eclipse/core/internal/dtree/AbstractDataTree
instanceKlass org/eclipse/core/internal/dtree/AbstractDataTreeNode
instanceKlass org/eclipse/core/internal/watson/ElementTree
instanceKlass org/eclipse/core/internal/runtime/DataArea
instanceKlass org/eclipse/core/internal/runtime/MetaDataKeeper
instanceKlass org/eclipse/core/internal/resources/LocalMetaArea
instanceKlass org/eclipse/core/internal/resources/LocationValidator
instanceKlass org/eclipse/core/runtime/Platform$OS
instanceKlass org/eclipse/core/internal/utils/FileUtil
instanceKlass org/eclipse/core/internal/watson/IElementContentVisitor
instanceKlass org/eclipse/core/resources/IResourceFilterDescription
instanceKlass org/eclipse/core/resources/team/IResourceTree
instanceKlass org/eclipse/core/resources/IMarker
instanceKlass org/eclipse/core/resources/IResourceProxy
instanceKlass org/eclipse/osgi/util/NLS
instanceKlass org/eclipse/core/runtime/Platform
instanceKlass org/eclipse/core/internal/resources/InternalTeamHook
instanceKlass org/eclipse/core/internal/watson/IElementComparator
instanceKlass org/eclipse/core/internal/dtree/IComparator
instanceKlass org/eclipse/core/resources/IResourceRuleFactory
instanceKlass org/eclipse/core/resources/ISynchronizer
instanceKlass org/eclipse/core/resources/IBuildConfiguration
instanceKlass org/eclipse/core/internal/resources/ResourceInfo
instanceKlass org/eclipse/core/resources/IProjectDescription
instanceKlass org/eclipse/core/resources/team/IMoveDeleteHook
instanceKlass org/eclipse/core/resources/IProject
instanceKlass org/eclipse/core/internal/resources/ModelObject
instanceKlass org/eclipse/core/resources/IPathVariableManager
instanceKlass org/eclipse/core/internal/properties/IPropertyManager
instanceKlass org/eclipse/core/internal/resources/IManager
instanceKlass org/eclipse/core/resources/IFile
instanceKlass org/eclipse/core/resources/IEncodedStorage
instanceKlass org/eclipse/core/resources/IStorage
instanceKlass org/eclipse/core/resources/IFolder
instanceKlass org/eclipse/core/internal/watson/IPathRequestor
instanceKlass org/eclipse/core/resources/IWorkspaceDescription
instanceKlass org/eclipse/core/internal/utils/IStringPoolParticipant
instanceKlass org/eclipse/core/runtime/ICoreRunnable
instanceKlass org/eclipse/core/internal/watson/IElementTreeData
instanceKlass org/eclipse/core/internal/jobs/WorkerPool
instanceKlass org/eclipse/core/internal/jobs/JobQueue
instanceKlass org/eclipse/core/internal/jobs/DeadlockDetector
instanceKlass org/eclipse/core/internal/jobs/LockManager
instanceKlass org/eclipse/core/runtime/jobs/JobChangeAdapter
instanceKlass java/lang/invoke/MethodHandle$1
instanceKlass org/eclipse/core/internal/jobs/JobListeners$IListenerDoit
instanceKlass org/eclipse/core/runtime/jobs/IJobChangeEvent
instanceKlass org/eclipse/core/internal/jobs/JobListeners
instanceKlass org/eclipse/core/internal/jobs/ImplicitJobs
instanceKlass org/eclipse/core/internal/jobs/JobManager$1
instanceKlass org/eclipse/core/runtime/ProgressMonitorWrapper
instanceKlass org/eclipse/core/runtime/IProgressMonitorWithBlocking
instanceKlass org/eclipse/core/runtime/jobs/ILock
instanceKlass org/eclipse/core/internal/jobs/InternalJobGroup
instanceKlass org/eclipse/core/runtime/jobs/IJobChangeListener
instanceKlass org/eclipse/core/internal/jobs/JobManager
instanceKlass org/eclipse/core/runtime/jobs/IJobManager
instanceKlass org/eclipse/core/internal/jobs/JobOSGiUtils
instanceKlass org/eclipse/core/internal/jobs/JobActivator
instanceKlass org/eclipse/core/resources/IWorkspaceRoot
instanceKlass org/eclipse/core/resources/IContainer
instanceKlass org/eclipse/core/resources/IResource
instanceKlass org/eclipse/core/runtime/jobs/ISchedulingRule
instanceKlass org/eclipse/core/runtime/PlatformObject
instanceKlass org/eclipse/core/internal/resources/ICoreConstants
instanceKlass java/time/Period
instanceKlass java/time/chrono/ChronoPeriod
instanceKlass java/time/format/DateTimeFormatterBuilder$TextPrinterParser
instanceKlass java/time/format/DateTimeTextProvider$1
instanceKlass java/time/format/DateTimeTextProvider
instanceKlass java/time/format/DateTimeTextProvider$LocaleStore
instanceKlass java/time/format/DateTimeFormatterBuilder$InstantPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$StringLiteralPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$ZoneIdPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$OffsetIdPrinterParser
instanceKlass java/time/format/DecimalStyle
instanceKlass java/time/format/DateTimeFormatterBuilder$CompositePrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$CharLiteralPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$NumberPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$DateTimePrinterParser
instanceKlass java/time/temporal/JulianFields
instanceKlass java/time/temporal/IsoFields
instanceKlass java/lang/Character$CharacterCache
instanceKlass java/time/temporal/TemporalQuery
instanceKlass java/time/format/DateTimeFormatterBuilder
instanceKlass java/time/format/DateTimeFormatter
instanceKlass org/eclipse/osgi/internal/debug/EclipseDebugTrace
instanceKlass org/eclipse/core/internal/utils/Policy$1
instanceKlass org/eclipse/core/runtime/IProgressMonitor
instanceKlass org/eclipse/core/internal/utils/Policy
instanceKlass org/eclipse/core/resources/ResourcesPlugin$WorkspaceInitCustomizer
instanceKlass org/eclipse/core/resources/IWorkspace
instanceKlass org/eclipse/core/runtime/IAdaptable
instanceKlass org/eclipse/jdt/ls/core/internal/managers/ProjectsManager
instanceKlass org/eclipse/jdt/ls/core/internal/managers/IProjectsManager
instanceKlass org/eclipse/core/resources/ISaveParticipant
instanceKlass org/eclipse/core/internal/runtime/LogServiceFactory
instanceKlass org/eclipse/equinox/internal/app/AppCommands
instanceKlass org/eclipse/equinox/internal/app/EclipseAppContainer$RegisterService
instanceKlass org/eclipse/core/runtime/spi/RegistryContributor
instanceKlass org/eclipse/equinox/app/IApplicationContext
instanceKlass org/osgi/service/application/ApplicationHandle
instanceKlass org/osgi/service/application/ApplicationDescriptor
instanceKlass org/eclipse/osgi/service/runnable/ApplicationLauncher
instanceKlass org/eclipse/osgi/service/runnable/ApplicationRunnable
instanceKlass org/eclipse/osgi/service/runnable/ParameterizedRunnable
instanceKlass org/eclipse/equinox/internal/app/IBranding
instanceKlass org/eclipse/equinox/internal/app/EclipseAppContainer
instanceKlass org/osgi/service/application/ScheduledApplication
instanceKlass org/eclipse/equinox/internal/app/AppPersistence
instanceKlass org/eclipse/equinox/internal/app/Activator
instanceKlass org/eclipse/equinox/internal/app/CommandLineArgs
instanceKlass org/eclipse/core/internal/preferences/legacy/InitLegacyPreferences
instanceKlass org/eclipse/core/internal/preferences/legacy/ProductPreferencesService
instanceKlass org/eclipse/core/internal/preferences/exchange/IProductPreferencesService
instanceKlass org/eclipse/core/internal/runtime/AuthorizationHandler
instanceKlass org/eclipse/core/runtime/IBundleGroupProvider
instanceKlass java/util/Collections$ReverseComparator2
instanceKlass org/eclipse/core/runtime/ILog
instanceKlass org/eclipse/core/internal/runtime/InternalPlatform
instanceKlass org/eclipse/core/runtime/Plugin
instanceKlass org/eclipse/equinox/internal/frameworkadmin/equinox/Log
instanceKlass java/lang/Process
instanceKlass org/eclipse/equinox/internal/provisional/configuratormanipulator/ConfiguratorManipulator
instanceKlass org/eclipse/equinox/internal/provisional/frameworkadmin/Manipulator
instanceKlass org/eclipse/equinox/internal/frameworkadmin/equinox/EquinoxFwAdminImpl
instanceKlass org/eclipse/equinox/internal/provisional/frameworkadmin/FrameworkAdmin
instanceKlass org/osgi/util/promise/DeferredPromiseImpl$ResolveWith
instanceKlass org/osgi/util/promise/PromiseImpl$Result
instanceKlass org/osgi/util/promise/PromiseFactory$All
instanceKlass org/osgi/util/promise/PromiseImpl$InlineCallback
instanceKlass org/eclipse/core/runtime/QualifiedName
instanceKlass org/apache/felix/scr/impl/inject/methods/ActivateMethod$1
instanceKlass org/apache/felix/scr/impl/inject/MethodResult
instanceKlass org/eclipse/core/internal/content/ContentTypeManager$ContentTypeRegistryChangeListener
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$OpenStatusImpl
instanceKlass org/apache/felix/scr/impl/manager/SingleComponentManager$1
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethod$1
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$Resolved
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$MethodInfo
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$1
instanceKlass org/eclipse/core/internal/content/BasicDescription
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager$IContentTypeChangeListener
instanceKlass org/eclipse/core/internal/preferences/BundleStateScopeServiceFactory
instanceKlass org/eclipse/core/internal/preferences/Activator$1
instanceKlass org/eclipse/core/internal/registry/ExtensionRegistry$ListenerInfo
instanceKlass org/eclipse/core/runtime/IContributor
instanceKlass org/eclipse/core/internal/preferences/PreferenceServiceRegistryHelper
instanceKlass org/eclipse/core/runtime/ListenerList$ListenerListIterator
instanceKlass org/eclipse/core/internal/preferences/OSGiPreferencesServiceManager
instanceKlass org/osgi/service/prefs/PreferencesService
instanceKlass org/eclipse/core/internal/preferences/AbstractScope
instanceKlass org/eclipse/core/runtime/Assert
instanceKlass org/eclipse/core/runtime/Path
instanceKlass org/eclipse/core/runtime/Path$Constants
instanceKlass org/eclipse/core/runtime/IPath
instanceKlass org/eclipse/core/internal/preferences/ImmutableMap
instanceKlass org/eclipse/core/internal/preferences/EclipsePreferences
instanceKlass org/eclipse/core/runtime/preferences/IScope
instanceKlass org/eclipse/core/runtime/preferences/IEclipsePreferences
instanceKlass org/eclipse/core/internal/preferences/PreferencesService
instanceKlass org/eclipse/core/runtime/preferences/IPreferencesService
instanceKlass org/eclipse/core/internal/preferences/exchange/ILegacyPreferences
instanceKlass org/eclipse/core/internal/preferences/PreferencesOSGiUtils
instanceKlass org/eclipse/core/internal/preferences/Activator
instanceKlass org/eclipse/core/runtime/preferences/IScopeContext
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager$ISelectionPolicy
instanceKlass org/eclipse/core/internal/content/ContentTypeBuilder
instanceKlass org/eclipse/core/internal/content/ContentTypeCatalog
instanceKlass org/apache/felix/scr/impl/inject/ValueUtils
instanceKlass org/eclipse/core/internal/content/ILazySource
instanceKlass org/eclipse/core/internal/adapter/AdapterManagerListener
instanceKlass org/eclipse/core/internal/runtime/IAdapterManagerProvider
instanceKlass org/eclipse/core/runtime/IRegistryEventListener
instanceKlass org/eclipse/core/internal/registry/osgi/RegistryCommandProvider
instanceKlass org/eclipse/osgi/framework/console/CommandProvider
instanceKlass org/eclipse/core/internal/registry/RegistryProviderFactory
instanceKlass org/eclipse/core/internal/registry/osgi/RegistryProviderOSGI
instanceKlass org/eclipse/core/internal/registry/osgi/EclipseBundleListener
instanceKlass org/eclipse/core/internal/registry/OffsetTable
instanceKlass org/eclipse/osgi/compatibility/state/ReadOnlyState
instanceKlass org/eclipse/core/internal/registry/HashtableOfStringAndInt
instanceKlass org/eclipse/core/internal/registry/KeyedHashSet
instanceKlass org/eclipse/core/runtime/IConfigurationElement
instanceKlass org/eclipse/core/internal/registry/Handle
instanceKlass org/eclipse/core/internal/registry/RegistryObjectManager
instanceKlass org/eclipse/core/internal/registry/IObjectManager
instanceKlass org/eclipse/core/internal/registry/RegistryTimestamp
instanceKlass org/eclipse/core/internal/registry/TableReader
instanceKlass org/eclipse/core/runtime/ListenerList
instanceKlass org/eclipse/core/internal/registry/ReadWriteMonitor
instanceKlass org/eclipse/core/internal/registry/RegistryObjectFactory
instanceKlass org/eclipse/core/runtime/IExtensionDelta
instanceKlass org/eclipse/core/runtime/IExtensionPoint
instanceKlass org/eclipse/core/runtime/ISafeRunnable
instanceKlass org/eclipse/core/internal/registry/RegistryObject
instanceKlass org/eclipse/core/internal/registry/KeyedElement
instanceKlass org/eclipse/core/runtime/IExtension
instanceKlass org/eclipse/core/internal/registry/ExtensionRegistry
instanceKlass org/eclipse/core/runtime/spi/IDynamicExtensionRegistry
instanceKlass org/eclipse/core/runtime/IExtensionRegistry
instanceKlass org/eclipse/core/runtime/RegistryFactory
instanceKlass org/eclipse/core/internal/registry/ReferenceMap$IEntry
instanceKlass org/eclipse/core/internal/registry/ReferenceMap
instanceKlass org/eclipse/core/internal/registry/osgi/OSGIUtils
instanceKlass org/eclipse/core/internal/registry/osgi/EquinoxUtils
instanceKlass org/eclipse/core/internal/registry/RegistryProperties
instanceKlass org/eclipse/core/runtime/spi/IRegistryProvider
instanceKlass org/eclipse/core/runtime/spi/RegistryStrategy
instanceKlass org/eclipse/core/internal/registry/osgi/Activator
instanceKlass org/eclipse/core/runtime/IRegistryChangeListener
instanceKlass org/eclipse/core/internal/content/IContentTypeInfo
instanceKlass org/eclipse/core/runtime/content/IContentDescription
instanceKlass org/eclipse/core/runtime/content/IContentType
instanceKlass org/eclipse/core/runtime/content/IContentTypeSettings
instanceKlass org/osgi/service/prefs/Preferences
instanceKlass org/apache/felix/scr/impl/inject/internal/ComponentConstructorImpl
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethods$1
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethod
instanceKlass org/apache/felix/scr/impl/inject/methods/BindMethods
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethods
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$NotApplicable
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$NotResolved
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$State
instanceKlass org/apache/felix/scr/impl/inject/BaseParameter
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod
instanceKlass org/eclipse/core/internal/content/ContentTypeMatcher
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager
instanceKlass org/eclipse/core/runtime/content/IContentTypeMatcher
instanceKlass org/apache/felix/scr/impl/helper/ComponentServiceObjectsHelper
instanceKlass org/apache/felix/scr/impl/manager/EdgeInfo
instanceKlass org/apache/felix/scr/impl/manager/ComponentContextImpl$ComponentInstanceImpl
instanceKlass org/osgi/service/component/ComponentInstance
instanceKlass org/apache/felix/scr/impl/manager/ComponentContextImpl
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager$RegStateWrapper
instanceKlass org/apache/felix/scr/impl/BundleComponentActivator$ListenerInfo
instanceKlass org/apache/felix/scr/impl/manager/ServiceTracker$AbstractTracked
instanceKlass org/apache/felix/scr/impl/manager/ExtendedServiceListener
instanceKlass org/apache/felix/scr/impl/manager/ServiceTracker
instanceKlass java/util/Collections$ReverseComparator
instanceKlass org/apache/felix/scr/impl/helper/Coercions
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$AbstractCustomizer
instanceKlass org/apache/felix/scr/impl/inject/RefPair
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$Customizer
instanceKlass org/apache/felix/scr/impl/manager/ServiceTrackerCustomizer
instanceKlass org/apache/felix/scr/impl/inject/OpenStatus
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager
instanceKlass org/apache/felix/scr/impl/manager/ReferenceManager
instanceKlass org/osgi/util/promise/Deferred
instanceKlass org/apache/felix/scr/impl/inject/ScrComponentContext
instanceKlass org/apache/felix/scr/component/ExtComponentContext
instanceKlass org/apache/felix/scr/impl/manager/SingleComponentManager$SetImplementationObject
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager
instanceKlass org/apache/felix/scr/impl/helper/ConfigAdminTracker$1
instanceKlass org/apache/felix/scr/impl/helper/ConfigAdminTracker
instanceKlass java/util/Timer$ThreadReaper
instanceKlass java/util/TaskQueue
instanceKlass java/util/Timer
instanceKlass org/apache/felix/scr/impl/inject/ComponentConstructor
instanceKlass org/apache/felix/scr/impl/inject/LifecycleMethod
instanceKlass org/apache/felix/scr/impl/inject/internal/ComponentMethodsImpl
instanceKlass org/apache/felix/scr/impl/metadata/TargetedPID
instanceKlass java/util/concurrent/CompletionStage
instanceKlass org/osgi/util/promise/PromiseImpl
instanceKlass org/osgi/util/promise/Promise
instanceKlass org/osgi/util/promise/PromiseFactory
instanceKlass org/osgi/util/promise/Promises
instanceKlass org/apache/felix/scr/impl/inject/ComponentMethods
instanceKlass org/osgi/service/component/ComponentFactory
instanceKlass org/apache/felix/scr/impl/manager/AbstractComponentManager
instanceKlass org/apache/felix/scr/impl/manager/ComponentManager
instanceKlass org/apache/felix/scr/impl/manager/ConfigurableComponentHolder
instanceKlass org/apache/felix/scr/impl/manager/ComponentContainer
instanceKlass org/apache/felix/scr/impl/ComponentRegistryKey
instanceKlass org/apache/felix/scr/impl/metadata/PropertyMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ReferenceMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ServiceMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ComponentMetadata
instanceKlass org/apache/felix/scr/impl/xml/XmlConstants
instanceKlass com/sun/org/apache/xerces/internal/impl/Constants$ArrayEnumeration
instanceKlass com/sun/org/apache/xerces/internal/impl/Constants
instanceKlass com/sun/org/apache/xerces/internal/parsers/AbstractSAXParser$LocatorProxy
instanceKlass org/xml/sax/ext/Locator2
instanceKlass org/xml/sax/Locator
instanceKlass com/sun/org/apache/xerces/internal/util/XMLSymbols
instanceKlass com/sun/org/apache/xerces/internal/util/XMLChar
instanceKlass com/sun/xml/internal/stream/Entity
instanceKlass com/sun/xml/internal/stream/util/BufferAllocator
instanceKlass com/sun/xml/internal/stream/util/ThreadLocalBufferAllocator
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager$EncodingInfo
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLLimitAnalyzer
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLInputSource
instanceKlass com/sun/org/apache/xerces/internal/util/ErrorHandlerWrapper
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLErrorHandler
instanceKlass com/sun/org/apache/xerces/internal/impl/ExternalSubsetResolver
instanceKlass com/sun/org/apache/xerces/internal/util/EntityResolverWrapper
instanceKlass org/xml/sax/ext/EntityResolver2
instanceKlass org/xml/sax/InputSource
instanceKlass com/sun/org/apache/xerces/internal/parsers/AbstractSAXParser$AttributesProxy
instanceKlass org/xml/sax/ext/Attributes2
instanceKlass org/xml/sax/Attributes
instanceKlass org/xml/sax/AttributeList
instanceKlass com/sun/org/apache/xerces/internal/util/FeatureState
instanceKlass com/sun/org/apache/xerces/internal/util/PropertyState
instanceKlass com/sun/org/apache/xerces/internal/impl/msg/XMLMessageFormatter
instanceKlass com/sun/org/apache/xerces/internal/util/MessageFormatter
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLVersionDetector
instanceKlass com/sun/org/apache/xerces/internal/impl/validation/ValidationManager
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/NMTOKENDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/NOTATIONDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/ENTITYDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/ListDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/IDREFDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/IDDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/StringDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/DatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/DTDDVFactory
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/DTDGrammarBucket
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLAttributeDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLSimpleType
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLElementDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/validation/ValidationState
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/ValidationContext
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/RevalidationHandler
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDValidatorFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentFilter
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLEntityDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDProcessor
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDContentModelFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDContentModelSource
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDSource
instanceKlass com/sun/org/apache/xerces/internal/xni/grammars/XMLDTDDescription
instanceKlass com/sun/org/apache/xerces/internal/xni/grammars/XMLGrammarDescription
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$TrailingMiscDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$PrologDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$XMLDeclDriver
instanceKlass com/sun/org/apache/xerces/internal/util/NamespaceSupport
instanceKlass com/sun/org/apache/xerces/internal/xni/NamespaceContext
instanceKlass com/sun/org/apache/xerces/internal/util/XMLAttributesImpl$Attribute
instanceKlass com/sun/org/apache/xerces/internal/util/XMLAttributesImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLAttributes
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$FragmentContentDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$Driver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$ElementStack2
instanceKlass com/sun/org/apache/xerces/internal/xni/QName
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$ElementStack
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLString
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLScanner
instanceKlass com/sun/xml/internal/stream/XMLBufferListener
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentSource
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLErrorReporter
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLLocator
instanceKlass com/sun/xml/internal/stream/XMLEntityStorage
instanceKlass com/sun/org/apache/xerces/internal/util/AugmentationsImpl$AugmentationsItemsContainer
instanceKlass com/sun/org/apache/xerces/internal/util/AugmentationsImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/Augmentations
instanceKlass com/sun/org/apache/xerces/internal/util/XMLResourceIdentifierImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLResourceIdentifier
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLEntityResolver
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLComponent
instanceKlass com/sun/org/apache/xerces/internal/util/SymbolTable$Entry
instanceKlass com/sun/org/apache/xerces/internal/util/SymbolTable
instanceKlass jdk/xml/internal/JdkConstants
instanceKlass jdk/xml/internal/JdkXmlUtils
instanceKlass com/sun/org/apache/xerces/internal/util/ParserConfigurationSettings
instanceKlass com/sun/org/apache/xerces/internal/parsers/XML11Configurable
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLPullParserConfiguration
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLParserConfiguration
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLComponentManager
instanceKlass com/sun/org/apache/xerces/internal/parsers/XMLParser
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDTDContentModelHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDTDHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDocumentHandler
instanceKlass org/xml/sax/XMLReader
instanceKlass org/xml/sax/Parser
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager
instanceKlass javax/xml/parsers/SAXParser
instanceKlass com/sun/org/apache/xerces/internal/xs/PSVIProvider
instanceKlass com/sun/org/apache/xerces/internal/jaxp/JAXPConstants
instanceKlass javax/xml/parsers/FactoryFinder$1
instanceKlass jdk/xml/internal/SecuritySupport
instanceKlass javax/xml/parsers/FactoryFinder
instanceKlass javax/xml/parsers/SAXParserFactory
instanceKlass org/xml/sax/helpers/DefaultHandler
instanceKlass org/xml/sax/ErrorHandler
instanceKlass org/xml/sax/ContentHandler
instanceKlass org/xml/sax/DTDHandler
instanceKlass org/xml/sax/EntityResolver
instanceKlass org/apache/felix/scr/impl/BundleComponentActivator
instanceKlass org/apache/felix/scr/impl/manager/ComponentActivator
instanceKlass org/apache/felix/scr/impl/manager/ExtendedServiceListenerContext
instanceKlass org/eclipse/core/internal/runtime/IAdapterFactoryExt
instanceKlass org/eclipse/core/internal/runtime/AdapterFactoryBridge$LazyAdapterFactory
instanceKlass org/eclipse/core/internal/runtime/AdapterFactoryBridge
instanceKlass org/eclipse/core/runtime/IAdapterFactory
instanceKlass org/eclipse/core/internal/runtime/TracingOptions$1
instanceKlass org/eclipse/core/internal/runtime/TracingOptions
instanceKlass java/util/AbstractMap$1$1
instanceKlass org/osgi/service/url/URLStreamHandlerService
instanceKlass org/eclipse/core/runtime/ServiceCaller$ReferenceAndService
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass org/eclipse/core/internal/runtime/AdapterManager
instanceKlass org/eclipse/core/runtime/IAdapterManager
instanceKlass org/eclipse/core/internal/runtime/PlatformURLConverter
instanceKlass org/eclipse/core/internal/runtime/RuntimeLog
instanceKlass org/eclipse/core/runtime/IStatus
instanceKlass org/eclipse/core/internal/runtime/PlatformLogWriter
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceImpl
instanceKlass org/eclipse/core/runtime/ServiceCaller
instanceKlass org/eclipse/core/internal/runtime/Activator
instanceKlass org/apache/felix/scr/impl/config/ScrMetaTypeProviderServiceFactory
instanceKlass org/apache/felix/scr/impl/config/ScrManagedServiceServiceFactory
instanceKlass org/apache/felix/scr/impl/ComponentCommands$2
instanceKlass org/apache/felix/scr/impl/ComponentCommands$1
instanceKlass org/apache/felix/scr/impl/ComponentCommands
instanceKlass org/apache/felix/scr/impl/Activator$ScrExtension
instanceKlass org/apache/felix/scr/impl/ComponentActorThread$1
instanceKlass org/apache/felix/scr/impl/ComponentActorThread
instanceKlass org/apache/felix/scr/impl/runtime/ServiceComponentRuntimeImpl
instanceKlass java/util/TimerTask
instanceKlass org/apache/felix/scr/impl/manager/RegionConfigurationSupport
instanceKlass org/apache/felix/scr/impl/manager/ComponentHolder
instanceKlass org/apache/felix/scr/impl/ComponentRegistry
instanceKlass org/apache/felix/scr/impl/logger/ScrLogManager$1
instanceKlass org/apache/felix/scr/impl/logger/LogManager$LogDomain
instanceKlass org/apache/felix/scr/impl/logger/LogManager$Lock
instanceKlass org/apache/felix/scr/impl/logger/LogManager$LoggerFacade
instanceKlass org/apache/felix/scr/impl/logger/ComponentLogger
instanceKlass org/apache/felix/scr/impl/logger/BundleLogger
instanceKlass org/apache/felix/scr/impl/logger/ScrLogger
instanceKlass org/apache/felix/scr/impl/logger/InternalLogger
instanceKlass org/apache/felix/scr/impl/logger/ScrLoggerFactory
instanceKlass org/osgi/service/component/ComponentContext
instanceKlass org/osgi/service/component/ComponentServiceObjects
instanceKlass org/apache/felix/scr/impl/inject/internal/ClassUtils
instanceKlass org/apache/felix/scr/impl/config/ScrConfigurationImpl
instanceKlass org/osgi/service/component/runtime/ServiceComponentRuntime
instanceKlass org/apache/felix/scr/impl/manager/ScrConfiguration
instanceKlass org/apache/felix/scr/impl/logger/LogConfiguration
instanceKlass org/apache/felix/scr/impl/AbstractExtender
instanceKlass org/eclipse/osgi/internal/loader/buddy/PolicyHandler
instanceKlass org/apache/aries/spifly/WeavingData
instanceKlass org/apache/aries/spifly/ConsumerRestriction
instanceKlass org/apache/aries/spifly/MethodRestriction
instanceKlass org/apache/aries/spifly/ArgRestrictions
instanceKlass org/apache/aries/spifly/BundleDescriptor
instanceKlass org/apache/aries/spifly/ConsumerBundleTrackerCustomizer
instanceKlass ch/qos/logback/classic/spi/LogbackServiceProvider
instanceKlass org/slf4j/spi/SLF4JServiceProvider
instanceKlass java/util/Formattable
instanceKlass java/time/LocalTime$1
instanceKlass java/lang/StringUTF16$CharsSpliterator
instanceKlass java/util/stream/Sink$ChainedInt
instanceKlass java/util/OptionalInt
instanceKlass java/util/stream/Sink$OfInt
instanceKlass java/util/function/IntConsumer
instanceKlass java/util/function/IntPredicate
instanceKlass java/util/stream/IntStream
instanceKlass java/lang/StringLatin1$CharsSpliterator
instanceKlass sun/util/locale/provider/LocaleResources
instanceKlass java/text/DecimalFormatSymbols
instanceKlass sun/util/resources/Bundles$2
instanceKlass sun/util/resources/Bundles$CacheKeyReference
instanceKlass sun/util/resources/LocaleData$LocaleDataResourceBundleProvider
instanceKlass java/util/spi/ResourceBundleProvider
instanceKlass sun/util/resources/Bundles$CacheKey
instanceKlass sun/util/resources/Bundles
instanceKlass sun/util/resources/LocaleData$LocaleDataStrategy
instanceKlass sun/util/resources/Bundles$Strategy
instanceKlass sun/util/resources/LocaleData$1
instanceKlass sun/util/resources/LocaleData
instanceKlass java/util/Locale$Builder
instanceKlass sun/util/locale/provider/CalendarDataUtility
instanceKlass sun/util/locale/provider/AvailableLanguageTags
instanceKlass sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo
instanceKlass jdk/internal/module/ModulePatcher$PatchedModuleReader
instanceKlass sun/util/locale/InternalLocaleBuilder$CaseInsensitiveChar
instanceKlass sun/util/locale/InternalLocaleBuilder
instanceKlass sun/util/locale/StringTokenIterator
instanceKlass sun/util/locale/ParseStatus
instanceKlass sun/util/locale/LanguageTag
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/LocaleDataMetaInfo
instanceKlass sun/util/locale/provider/ResourceBundleBasedAdapter
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$1
instanceKlass sun/util/locale/provider/LocaleProviderAdapter
instanceKlass java/util/spi/LocaleServiceProvider
instanceKlass java/text/DateFormatSymbols
instanceKlass java/time/LocalDate$1
instanceKlass java/time/ZonedDateTime$1
instanceKlass java/util/Calendar
instanceKlass java/util/Formatter$FixedString
instanceKlass java/util/Formatter$DateTime
instanceKlass java/util/Formatter$Flags
instanceKlass java/util/Formatter$FormatSpecifier
instanceKlass java/util/Formatter$FormatString
instanceKlass java/util/Formatter$Conversion
instanceKlass sun/util/locale/BaseLocale$Key
instanceKlass java/util/Formatter
instanceKlass java/util/ResourceBundle$ResourceBundleProviderHelper
instanceKlass java/util/ResourceBundle$3
instanceKlass java/util/ResourceBundle$CacheKeyReference
instanceKlass java/util/ResourceBundle$CacheKey
instanceKlass sun/util/locale/LocaleObjectCache
instanceKlass java/util/ResourceBundle$Control
instanceKlass java/util/logging/Level$RbAccess
instanceKlass java/lang/invoke/LambdaFormEditor$1
instanceKlass java/lang/invoke/MethodHandles$1
instanceKlass java/lang/StackStreamFactory$FrameBuffer
instanceKlass java/lang/StackStreamFactory$1
instanceKlass java/lang/StackStreamFactory
instanceKlass java/util/logging/LogRecord$CallerFinder
instanceKlass java/time/chrono/AbstractChronology
instanceKlass java/time/chrono/Chronology
instanceKlass java/time/zone/Ser
instanceKlass java/io/Externalizable
instanceKlass java/time/zone/ZoneRulesProvider$1
instanceKlass java/time/zone/ZoneRulesProvider
instanceKlass sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
instanceKlass sun/util/calendar/ZoneInfoFile$1
instanceKlass sun/util/calendar/ZoneInfoFile
instanceKlass java/util/TimeZone
instanceKlass java/util/logging/LogManager$CloseOnReset
instanceKlass java/util/logging/StreamHandler$1
instanceKlass java/util/logging/Handler$1
instanceKlass java/util/logging/ErrorManager
instanceKlass jdk/internal/logger/SimpleConsoleLogger$Formatting
instanceKlass java/util/logging/Formatter
instanceKlass java/time/Clock
instanceKlass java/time/InstantSource
instanceKlass java/util/logging/LogRecord
instanceKlass org/apache/aries/spifly/ProviderServiceFactory
instanceKlass ch/qos/logback/classic/servlet/LogbackServletContainerInitializer
instanceKlass jakarta/servlet/ServletContainerInitializer
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle$1
instanceKlass org/apache/aries/spifly/ProviderBundleTrackerCustomizer$ServiceDetails
instanceKlass org/eclipse/osgi/storage/bundlefile/ZipBundleFile$1
instanceKlass org/apache/aries/spifly/Pair
instanceKlass org/osgi/framework/connect/FrameworkUtilHelper
instanceKlass org/osgi/framework/FrameworkUtil
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass sun/invoke/util/VerifyAccess$1
instanceKlass aQute/bnd/stream/EntryPipeline
instanceKlass aQute/bnd/stream/MapStream
instanceKlass org/apache/aries/spifly/SpiFlyConstants
instanceKlass org/apache/aries/spifly/ConsumerHeaderProcessor
instanceKlass aQute/bnd/header/Attrs$DataType
instanceKlass aQute/bnd/header/Attrs
instanceKlass aQute/libg/qtokens/QuotedTokenizer
instanceKlass aQute/bnd/header/OSGiHeader
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$CaseInsentiveEntry
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$EntryIterator
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$KeyIterator
instanceKlass java/util/ResourceBundle$1
instanceKlass jdk/internal/access/JavaUtilResourceBundleAccess
instanceKlass java/util/ResourceBundle
instanceKlass java/util/function/BooleanSupplier
instanceKlass java/util/stream/StreamSpliterators
instanceKlass java/util/stream/AbstractSpinedBuffer
instanceKlass org/eclipse/osgi/internal/container/InternalUtils$1
instanceKlass java/util/stream/StreamSpliterators$AbstractWrappingSpliterator
instanceKlass org/eclipse/osgi/storage/ManifestLocalization$BundleResourceBundle
instanceKlass org/eclipse/osgi/storage/ManifestLocalization
instanceKlass org/apache/aries/spifly/ProviderBundleTrackerCustomizer
instanceKlass org/osgi/util/tracker/BundleTracker
instanceKlass aQute/bnd/header/Parameters
instanceKlass java/util/AbstractList$Itr
instanceKlass org/osgi/framework/hooks/weaving/WovenClassListener
instanceKlass org/apache/aries/spifly/Util
instanceKlass org/objectweb/asm/Opcodes
instanceKlass org/objectweb/asm/ClassVisitor
instanceKlass org/apache/aries/spifly/dynamic/ClientWeavingHook
instanceKlass java/util/logging/Logger$SystemLoggerHelper$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper
instanceKlass java/util/logging/LogManager$4
instanceKlass jdk/internal/logger/BootstrapLogger$BootstrapExecutors
instanceKlass jdk/internal/logger/LoggerFinderLoader
instanceKlass java/util/stream/Streams
instanceKlass java/util/stream/Streams$AbstractStreamBuilderImpl
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$VisitedLoggers
instanceKlass java/util/logging/LogManager$2
instanceKlass java/util/logging/LogManager$LoggingProviderAccess
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/LogManager
instanceKlass java/util/logging/Logger$ConfigurationData
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass org/osgi/util/tracker/BundleTrackerCustomizer
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult
instanceKlass org/apache/aries/spifly/BaseActivator
instanceKlass org/osgi/framework/hooks/weaving/WeavingHook
instanceKlass org/eclipse/osgi/internal/weaving/WeavingHookConfigurator$WovenClassContext
instanceKlass org/eclipse/osgi/internal/weaving/WovenClassImpl
instanceKlass org/osgi/framework/hooks/weaving/WovenClass
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathManager$DefineContext
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$3
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerStartLevel$2
instanceKlass java/util/concurrent/CountDownLatch
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainerAdaptor$1$1
instanceKlass org/osgi/dto/DTO
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager$EventThread$Queued
instanceKlass org/osgi/framework/hooks/bundle/EventHook
instanceKlass org/osgi/framework/hooks/bundle/FindHook
instanceKlass org/eclipse/osgi/framework/util/FilePath
instanceKlass sun/nio/ch/FileChannelImpl$Closer
instanceKlass sun/nio/fs/WindowsChannelFactory$Flags
instanceKlass sun/nio/fs/WindowsChannelFactory$1
instanceKlass sun/nio/fs/WindowsChannelFactory
instanceKlass org/eclipse/core/runtime/internal/adaptor/ConsoleManager
instanceKlass org/eclipse/core/runtime/internal/adaptor/DefaultStartupMonitor
instanceKlass org/eclipse/osgi/service/runnable/StartupMonitor
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceFactoryUse$1
instanceKlass java/util/LinkedList$ListItr
instanceKlass java/util/LinkedList$Node
instanceKlass org/eclipse/osgi/internal/resolver/StateImpl
instanceKlass org/eclipse/osgi/service/resolver/BundleSpecification
instanceKlass org/eclipse/osgi/service/resolver/NativeCodeSpecification
instanceKlass org/eclipse/osgi/service/resolver/ImportPackageSpecification
instanceKlass org/eclipse/osgi/service/resolver/HostSpecification
instanceKlass org/eclipse/osgi/service/resolver/NativeCodeDescription
instanceKlass org/eclipse/osgi/service/resolver/ExportPackageDescription
instanceKlass org/eclipse/osgi/service/resolver/GenericDescription
instanceKlass org/eclipse/osgi/service/resolver/GenericSpecification
instanceKlass org/eclipse/osgi/service/resolver/VersionConstraint
instanceKlass org/eclipse/osgi/service/resolver/BundleDescription
instanceKlass org/eclipse/osgi/service/resolver/BaseDescription
instanceKlass org/eclipse/osgi/internal/resolver/StateObjectFactoryImpl
instanceKlass org/eclipse/osgi/service/resolver/Resolver
instanceKlass org/eclipse/osgi/service/resolver/State
instanceKlass org/eclipse/osgi/service/resolver/StateObjectFactory
instanceKlass org/eclipse/osgi/compatibility/state/PlatformAdminImpl
instanceKlass org/eclipse/osgi/service/resolver/PlatformAdmin
instanceKlass org/eclipse/osgi/compatibility/state/Activator
instanceKlass sun/security/x509/RFC822Name
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer$2
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer$1
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer
instanceKlass org/eclipse/osgi/service/security/TrustEngine
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedContentConstants
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedBundleHook$1
instanceKlass org/eclipse/osgi/internal/framework/XMLParsingServiceFactory
instanceKlass org/eclipse/osgi/storage/BundleLocalizationImpl
instanceKlass org/eclipse/osgi/service/localization/BundleLocalization
instanceKlass org/eclipse/osgi/storage/url/BundleURLConverter
instanceKlass org/eclipse/osgi/service/urlconversion/URLConverter
instanceKlass org/apache/felix/resolver/Logger
instanceKlass org/apache/felix/resolver/util/OpenHashMap
instanceKlass org/apache/felix/resolver/ResolutionError
instanceKlass org/apache/felix/resolver/ResolverImpl
instanceKlass org/osgi/service/resolver/Resolver
instanceKlass org/eclipse/osgi/internal/framework/legacy/StartLevelImpl
instanceKlass org/eclipse/osgi/internal/framework/legacy/PackageAdminImpl
instanceKlass org/osgi/service/condition/ConditionImpl
instanceKlass org/osgi/service/condition/Condition
instanceKlass java/net/ContentHandler
instanceKlass java/net/ContentHandlerFactory
instanceKlass org/eclipse/osgi/internal/url/EquinoxFactoryManager
instanceKlass org/eclipse/osgi/internal/log/ConfigAdminListener
instanceKlass org/eclipse/osgi/internal/log/EventAdminAdapter
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$2
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Snapshot$SnapshotIterator
instanceKlass org/eclipse/osgi/framework/eventmgr/ListenerQueue
instanceKlass org/osgi/framework/hooks/service/EventListenerHook
instanceKlass org/osgi/framework/hooks/service/EventHook
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Snapshot
instanceKlass org/osgi/framework/PrototypeServiceFactory
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$CaseInsensitiveKey
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceReferenceImpl
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceUse
instanceKlass org/eclipse/osgi/internal/log/OrderedExecutor
instanceKlass org/osgi/framework/hooks/service/FindHook
instanceKlass org/eclipse/osgi/internal/serviceregistry/ShrinkableCollection
instanceKlass org/eclipse/osgi/internal/serviceregistry/HookContext
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Entry
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap
instanceKlass org/osgi/framework/UnfilteredServiceListener
instanceKlass org/eclipse/osgi/internal/serviceregistry/FilteredServiceListener
instanceKlass org/osgi/framework/hooks/service/ListenerHook$ListenerInfo
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl$Parser
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl
instanceKlass org/osgi/util/tracker/AbstractTracked
instanceKlass org/osgi/util/tracker/ServiceTracker
instanceKlass org/eclipse/osgi/internal/framework/BundleContextImpl$2
instanceKlass org/osgi/service/startlevel/StartLevel
instanceKlass org/osgi/service/packageadmin/PackageAdmin
instanceKlass org/eclipse/osgi/internal/framework/SystemBundleActivator
instanceKlass org/eclipse/osgi/internal/loader/classpath/TitleVersionVendor
instanceKlass org/eclipse/osgi/internal/loader/classpath/ManifestPackageAttributes
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathEntry$PDEData
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathEntry
instanceKlass org/eclipse/osgi/internal/loader/classpath/FragmentClasspath
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathManager
instanceKlass org/eclipse/osgi/internal/loader/BundleLoaderSources
instanceKlass java/util/concurrent/ForkJoinPool$WorkQueue
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$1
instanceKlass java/util/concurrent/ForkJoinPool$DefaultCommonPoolForkJoinWorkerThreadFactory
instanceKlass java/util/concurrent/ForkJoinPool$1
instanceKlass java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory
instanceKlass java/util/concurrent/ForkJoinPool$ForkJoinWorkerThreadFactory
instanceKlass org/eclipse/osgi/internal/loader/sources/PackageSource
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass org/eclipse/osgi/internal/framework/StorageSaver$StorageSaverTask
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
instanceKlass org/eclipse/osgi/internal/framework/StorageSaver
instanceKlass java/util/concurrent/TimeUnit$1
instanceKlass java/util/concurrent/Executors$RunnableAdapter
instanceKlass java/util/concurrent/FutureTask$WaitNode
instanceKlass java/util/concurrent/FutureTask
instanceKlass java/util/concurrent/RunnableScheduledFuture
instanceKlass java/util/concurrent/ScheduledFuture
instanceKlass java/util/concurrent/Delayed
instanceKlass java/util/concurrent/RunnableFuture
instanceKlass java/util/concurrent/Future
instanceKlass java/util/concurrent/ThreadPoolExecutor$AbortPolicy
instanceKlass java/util/concurrent/AbstractExecutorService
instanceKlass java/util/concurrent/ScheduledExecutorService
instanceKlass java/util/concurrent/ExecutorService
instanceKlass java/util/concurrent/Executors
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder$1
instanceKlass org/osgi/framework/ServiceObjects
instanceKlass org/eclipse/osgi/internal/framework/BundleContextImpl
instanceKlass org/osgi/framework/hooks/service/ListenerHook
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistrationImpl
instanceKlass org/osgi/framework/ServiceRegistration
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistry
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager
instanceKlass org/eclipse/osgi/internal/framework/EquinoxEventPublisher
instanceKlass org/eclipse/osgi/storage/bundlefile/BundleEntry
instanceKlass org/eclipse/osgi/container/ModuleDatabase$2
instanceKlass java/util/ArrayList$SubList$1
instanceKlass java/lang/reflect/AnnotatedType
instanceKlass java/lang/reflect/TypeVariable
instanceKlass org/eclipse/osgi/container/ModuleWiring$LoaderInitializer
instanceKlass org/eclipse/osgi/container/ModuleWiring
instanceKlass org/eclipse/osgi/container/ModuleWire
instanceKlass org/osgi/framework/wiring/BundleWire
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass org/eclipse/osgi/internal/container/Capabilities$NamespaceSet
instanceKlass org/eclipse/osgi/container/ModuleRequirement
instanceKlass org/osgi/framework/wiring/BundleRequirement
instanceKlass org/eclipse/osgi/container/ModuleRevision$2
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$8
instanceKlass org/eclipse/osgi/container/ModuleCapability
instanceKlass org/osgi/framework/wiring/BundleCapability
instanceKlass org/osgi/resource/Capability
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$3
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$2
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$1
instanceKlass org/eclipse/osgi/internal/container/NamespaceList
instanceKlass org/eclipse/osgi/container/ModuleRevision$1
instanceKlass org/osgi/framework/wiring/BundleWiring
instanceKlass org/osgi/resource/Wiring
instanceKlass org/eclipse/osgi/container/ModuleRevision
instanceKlass org/eclipse/osgi/container/ModuleRevisions
instanceKlass org/osgi/framework/wiring/BundleRevisions
instanceKlass org/lombokweb/asm/Opcodes
instanceKlass org/lombokweb/asm/Handler
instanceKlass lombok/patcher/MethodLogistics
instanceKlass org/lombokweb/asm/Label
instanceKlass org/lombokweb/asm/Type
instanceKlass org/lombokweb/asm/Frame
instanceKlass org/lombokweb/asm/Context
instanceKlass org/lombokweb/asm/Attribute
instanceKlass lombok/patcher/scripts/ExitFromMethodEarlyScript$1
instanceKlass org/lombokweb/asm/ByteVector
instanceKlass org/lombokweb/asm/Symbol
instanceKlass org/lombokweb/asm/SymbolTable
instanceKlass org/lombokweb/asm/MethodVisitor
instanceKlass org/lombokweb/asm/FieldVisitor
instanceKlass org/lombokweb/asm/ModuleVisitor
instanceKlass org/lombokweb/asm/AnnotationVisitor
instanceKlass org/lombokweb/asm/RecordComponentVisitor
instanceKlass org/lombokweb/asm/ClassReader
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$2
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder$GenericInfo$1
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder$GenericInfo
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder
instanceKlass org/osgi/resource/Wire
instanceKlass org/eclipse/osgi/container/ModuleDatabase$Persistence
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerStartLevel
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerWiring
instanceKlass org/eclipse/osgi/container/ModuleResolver
instanceKlass org/eclipse/osgi/container/ModuleContainer$ResolutionLock
instanceKlass org/eclipse/osgi/internal/container/LockSet
instanceKlass org/osgi/framework/wiring/FrameworkWiring
instanceKlass org/osgi/resource/Requirement
instanceKlass org/osgi/framework/startlevel/FrameworkStartLevel
instanceKlass org/eclipse/osgi/report/resolution/ResolutionReport
instanceKlass org/eclipse/osgi/container/ModuleContainer
instanceKlass org/eclipse/osgi/internal/container/Capabilities
instanceKlass org/eclipse/osgi/container/Module
instanceKlass org/osgi/framework/startlevel/BundleStartLevel
instanceKlass org/eclipse/osgi/container/ModuleDatabase
instanceKlass java/util/concurrent/LinkedBlockingQueue$Node
instanceKlass java/util/concurrent/RejectedExecutionHandler
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainerAdaptor$1
instanceKlass java/util/concurrent/SynchronousQueue$TransferStack$SNode
instanceKlass java/util/concurrent/ForkJoinPool$ManagedBlocker
instanceKlass java/util/concurrent/SynchronousQueue$Transferer
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass org/eclipse/osgi/internal/container/AtomicLazyInitializer
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$BundleCollisionHook
instanceKlass org/osgi/framework/ServiceReference
instanceKlass org/osgi/framework/hooks/resolver/ResolverHook
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$CoreResolverHookFactory
instanceKlass org/osgi/framework/hooks/resolver/ResolverHookFactory
instanceKlass org/eclipse/osgi/container/ModuleCollisionHook
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor$1
instanceKlass org/eclipse/osgi/container/ModuleLoader
instanceKlass java/util/concurrent/Callable
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass java/util/concurrent/Executor
instanceKlass org/eclipse/osgi/internal/permadmin/SecurityRow
instanceKlass org/eclipse/osgi/internal/permadmin/PermissionAdminTable
instanceKlass org/osgi/service/permissionadmin/PermissionInfo
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionUpdate
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionInfo
instanceKlass org/eclipse/osgi/internal/permadmin/SecurityAdmin
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionAdmin
instanceKlass org/osgi/service/permissionadmin/PermissionAdmin
instanceKlass org/eclipse/osgi/storage/PermissionData
instanceKlass org/eclipse/osgi/storage/BundleInfo$Generation
instanceKlass org/eclipse/osgi/storage/BundleInfo
instanceKlass org/eclipse/osgi/framework/util/ObjectPool
instanceKlass org/eclipse/osgi/storagemanager/StorageManager$Entry
instanceKlass org/eclipse/osgi/framework/internal/reliablefile/ReliableFile$CacheInfo
instanceKlass java/util/ComparableTimSort
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/io/DeleteOnExitHook$1
instanceKlass java/io/DeleteOnExitHook
instanceKlass java/util/function/IntUnaryOperator
instanceKlass org/eclipse/osgi/framework/internal/reliablefile/ReliableFile
instanceKlass sun/nio/ch/FileKey
instanceKlass sun/nio/ch/FileLockTable
instanceKlass sun/nio/ch/NativeThread
instanceKlass java/nio/channels/FileLock
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass sun/nio/ch/IOUtil
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass org/eclipse/osgi/internal/location/Locker_JavaNio
instanceKlass org/eclipse/osgi/storagemanager/StorageManager
instanceKlass java/util/HashMap$HashMapSpliterator
instanceKlass java/lang/Long$LongCache
instanceKlass jdk/internal/vm/annotation/ForceInline
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorFactory
instanceKlass sun/misc/Unsafe
instanceKlass org/eclipse/osgi/internal/url/MultiplexingFactory
instanceKlass org/eclipse/osgi/storage/FrameworkExtensionInstaller
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
instanceKlass java/util/concurrent/locks/Condition
instanceKlass org/eclipse/osgi/storage/bundlefile/MRUBundleFileList
instanceKlass org/eclipse/osgi/framework/eventmgr/EventDispatcher
instanceKlass org/osgi/framework/Filter
instanceKlass org/eclipse/osgi/storage/ContentProvider
instanceKlass org/eclipse/osgi/storage/bundlefile/BundleFile
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor
instanceKlass org/eclipse/osgi/storage/Storage
instanceKlass org/eclipse/osgi/signedcontent/SignedContent
instanceKlass org/eclipse/osgi/internal/hookregistry/StorageHookFactory$StorageHook
instanceKlass org/eclipse/osgi/internal/hookregistry/StorageHookFactory
instanceKlass org/osgi/framework/BundleActivator
instanceKlass org/eclipse/osgi/internal/cds/CDSHookConfigurator
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedBundleHook
instanceKlass org/eclipse/osgi/signedcontent/SignedContentFactory
instanceKlass org/eclipse/osgi/internal/hookregistry/ActivatorHookFactory
instanceKlass org/osgi/framework/wiring/BundleRevision
instanceKlass org/osgi/resource/Resource
instanceKlass org/eclipse/osgi/internal/connect/ConnectHookConfigurator
instanceKlass org/eclipse/osgi/internal/hookregistry/HookConfigurator
instanceKlass java/net/URLClassLoader$3$1
instanceKlass java/net/URLClassLoader$3
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$ConnectModules
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerContext
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogFactory$1
instanceKlass org/eclipse/osgi/framework/log/FrameworkLog
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogFactory
instanceKlass org/osgi/service/log/FormatterLogger
instanceKlass org/eclipse/osgi/internal/log/LoggerImpl
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceImpl
instanceKlass org/eclipse/osgi/internal/log/LogServiceManager$MockSystemBundle
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerAdmin
instanceKlass org/osgi/service/log/admin/LoggerContext
instanceKlass org/eclipse/osgi/internal/log/LoggerContextTargetMap
instanceKlass org/osgi/service/log/admin/LoggerAdmin
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory
instanceKlass org/eclipse/osgi/framework/util/ArrayMap
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock
instanceKlass java/util/concurrent/locks/ReadWriteLock
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceFactory$1
instanceKlass org/osgi/service/log/LogEntry
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceFactory
instanceKlass org/osgi/framework/ServiceFactory
instanceKlass org/eclipse/equinox/log/ExtendedLogReaderService
instanceKlass org/osgi/service/log/LogReaderService
instanceKlass org/eclipse/equinox/log/ExtendedLogService
instanceKlass org/eclipse/equinox/log/Logger
instanceKlass org/osgi/service/log/Logger
instanceKlass org/osgi/service/log/LogService
instanceKlass org/osgi/service/log/LoggerFactory
instanceKlass org/eclipse/osgi/internal/log/LogServiceManager
instanceKlass org/osgi/framework/AllServiceListener
instanceKlass org/osgi/framework/ServiceListener
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogWriter
instanceKlass org/eclipse/equinox/log/LogFilter
instanceKlass org/eclipse/equinox/log/SynchronousLogListener
instanceKlass org/osgi/service/log/LogListener
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogServices
instanceKlass org/eclipse/osgi/util/ManifestElement
instanceKlass org/eclipse/osgi/internal/util/SupplementDebug
instanceKlass org/eclipse/osgi/internal/debug/Debug
instanceKlass org/eclipse/osgi/service/debug/DebugOptionsListener
instanceKlass org/eclipse/osgi/service/debug/DebugTrace
instanceKlass org/eclipse/osgi/internal/debug/FrameworkDebugOptions
instanceKlass org/osgi/util/tracker/ServiceTrackerCustomizer
instanceKlass java/nio/file/FileVisitor
instanceKlass org/eclipse/osgi/storage/StorageUtil
instanceKlass org/eclipse/osgi/internal/location/BasicLocation
instanceKlass org/eclipse/osgi/internal/location/EquinoxLocations
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass java/util/UUID
instanceKlass java/util/Random
instanceKlass java/util/random/RandomGenerator
instanceKlass org/eclipse/osgi/internal/container/InternalUtils
instanceKlass org/osgi/framework/Version
instanceKlass org/eclipse/osgi/internal/location/Locker
instanceKlass org/eclipse/osgi/internal/location/LocationHelper
instanceKlass org/eclipse/osgi/internal/framework/EquinoxConfiguration$ConfigValues
instanceKlass org/eclipse/osgi/internal/util/Tokenizer
instanceKlass java/nio/charset/CoderResult
instanceKlass sun/util/logging/PlatformLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge$LoggerConfiguration
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass jdk/internal/logger/LazyLoggers$LazyLoggerAccessor
instanceKlass jdk/internal/logger/LazyLoggers$LoggerAccessor
instanceKlass jdk/internal/logger/AbstractLoggerWrapper
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass java/util/ServiceLoader$1
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/lang/System$LoggerFinder
instanceKlass jdk/internal/logger/LazyLoggers$LazyLoggerFactories
instanceKlass jdk/internal/logger/LazyLoggers$1
instanceKlass jdk/internal/logger/LazyLoggers
instanceKlass jdk/internal/misc/ThreadTracker
instanceKlass jdk/internal/event/EventHelper$ThreadTrackHolder
instanceKlass java/net/URLClassLoader$2
instanceKlass org/eclipse/osgi/internal/framework/AliasMapper
instanceKlass org/eclipse/osgi/framework/util/KeyedElement
instanceKlass org/eclipse/osgi/internal/hookregistry/ClassLoaderHook
instanceKlass org/eclipse/osgi/internal/hookregistry/HookRegistry
instanceKlass org/eclipse/osgi/service/datalocation/Location
instanceKlass org/eclipse/osgi/service/debug/DebugOptions
instanceKlass org/eclipse/osgi/internal/framework/EquinoxConfiguration
instanceKlass org/eclipse/osgi/service/environment/EnvironmentInfo
instanceKlass java/lang/annotation/Target
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass jdk/internal/org/objectweb/asm/Edge
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass java/lang/PublicMethods
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass java/lang/reflect/Proxy
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass org/eclipse/osgi/framework/util/SecureAction$1
instanceKlass org/eclipse/osgi/framework/util/SecureAction
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer
instanceKlass org/eclipse/osgi/launch/Equinox
instanceKlass java/util/EventObject
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter$InitialBundle
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter$StartupEventListener
instanceKlass org/osgi/framework/FrameworkListener
instanceKlass org/osgi/framework/SynchronousBundleListener
instanceKlass java/util/concurrent/Semaphore
instanceKlass org/osgi/framework/BundleContext
instanceKlass org/osgi/framework/BundleReference
instanceKlass org/osgi/framework/BundleListener
instanceKlass java/util/EventListener
instanceKlass org/osgi/framework/launch/Framework
instanceKlass org/osgi/framework/Bundle
instanceKlass jdk/internal/reflect/ClassDefiner$1
instanceKlass jdk/internal/reflect/ClassDefiner
instanceKlass jdk/internal/reflect/MethodAccessorGenerator$1
instanceKlass jdk/internal/reflect/Label$PatchInfo
instanceKlass jdk/internal/reflect/Label
instanceKlass jdk/internal/reflect/UTF8
instanceKlass jdk/internal/reflect/ClassFileAssembler
instanceKlass jdk/internal/reflect/ByteVectorImpl
instanceKlass jdk/internal/reflect/ByteVector
instanceKlass jdk/internal/reflect/ByteVectorFactory
instanceKlass jdk/internal/reflect/AccessorGenerator
instanceKlass jdk/internal/reflect/ClassFileConstants
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter
instanceKlass java/io/FilePermissionCollection$1
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/io/FilePermission$1
instanceKlass jdk/internal/access/JavaIOFilePermissionAccess
instanceKlass java/net/URLClassLoader$1
instanceKlass org/eclipse/equinox/launcher/Main$Identifier
instanceKlass java/nio/file/FileStore
instanceKlass sun/nio/fs/WindowsSecurity
instanceKlass sun/nio/fs/AbstractAclFileAttributeView
instanceKlass java/nio/file/attribute/AclFileAttributeView
instanceKlass java/nio/file/attribute/FileOwnerAttributeView
instanceKlass sun/nio/fs/WindowsLinkSupport
instanceKlass sun/nio/fs/WindowsFileSystemProvider$1
instanceKlass org/eclipse/equinox/launcher/JNIBridge
instanceKlass java/io/RandomAccessFile$1
instanceKlass sun/net/www/MimeEntry
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/Properties$EntrySet
instanceKlass sun/net/www/MimeTable$DefaultInstanceHolder$1
instanceKlass sun/net/www/MimeTable$DefaultInstanceHolder
instanceKlass sun/net/www/MimeTable$1
instanceKlass sun/net/www/MimeTable
instanceKlass java/net/URLConnection$1
instanceKlass java/net/FileNameMap
instanceKlass java/util/Collections$3
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass java/net/spi/URLStreamHandlerProvider
instanceKlass java/net/URL$1
instanceKlass java/net/URL$2
instanceKlass java/nio/file/attribute/PosixFilePermissions
instanceKlass java/security/Policy
instanceKlass org/eclipse/equinox/launcher/Main
instanceKlass sun/security/util/ManifestEntryVerifier$SunProviderHolder
instanceKlass java/util/Base64$Encoder
instanceKlass java/util/Base64$Decoder
instanceKlass java/util/Base64
instanceKlass javax/crypto/SecretKey
instanceKlass sun/security/util/Length
instanceKlass sun/security/util/KeyUtil
instanceKlass java/security/interfaces/XECKey
instanceKlass java/security/interfaces/ECKey
instanceKlass sun/security/util/JarConstraintsParameters
instanceKlass sun/security/util/ConstraintsParameters
instanceKlass java/security/Timestamp
instanceKlass sun/security/timestamp/TimestampToken
instanceKlass java/security/cert/CertPath
instanceKlass java/math/MutableBigInteger
instanceKlass sun/security/rsa/RSAPadding
instanceKlass sun/security/rsa/RSACore
instanceKlass java/security/interfaces/RSAPrivateCrtKey
instanceKlass sun/security/pkcs/PKCS8Key
instanceKlass java/security/interfaces/RSAPrivateKey
instanceKlass java/security/PrivateKey
instanceKlass javax/security/auth/Destroyable
instanceKlass sun/security/jca/ServiceId
instanceKlass java/security/Signature$1
instanceKlass jdk/internal/access/JavaSecuritySignatureAccess
instanceKlass java/security/SignatureSpi
instanceKlass jdk/internal/icu/util/CodePointTrie$Data
instanceKlass jdk/internal/icu/util/CodePointTrie$1
instanceKlass jdk/internal/icu/util/CodePointMap
instanceKlass jdk/internal/icu/util/VersionInfo
instanceKlass jdk/internal/jimage/decompressor/ZipDecompressor
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressor
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressorFactory
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressorRepository
instanceKlass jdk/internal/jimage/decompressor/CompressedResourceHeader
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressor$StringsProvider
instanceKlass java/util/TimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass java/util/AbstractMap$SimpleEntry
instanceKlass jdk/internal/jimage/ImageBufferCache$2
instanceKlass jdk/internal/jimage/ImageBufferCache
instanceKlass jdk/internal/module/Checks
instanceKlass jdk/internal/icu/impl/ICUBinary$1
instanceKlass jdk/internal/icu/impl/ICUBinary
instanceKlass jdk/internal/icu/impl/NormalizerImpl$IsAcceptable
instanceKlass jdk/internal/icu/impl/ICUBinary$Authenticate
instanceKlass jdk/internal/icu/impl/NormalizerImpl
instanceKlass jdk/internal/icu/impl/Norm2AllModes$Norm2AllModesSingleton
instanceKlass jdk/internal/icu/impl/Norm2AllModes$NFKCSingleton
instanceKlass jdk/internal/icu/impl/Norm2AllModes
instanceKlass jdk/internal/icu/text/Normalizer2
instanceKlass jdk/internal/icu/text/NormalizerBase$ModeImpl
instanceKlass jdk/internal/icu/text/NormalizerBase$NFKDModeImpl
instanceKlass jdk/internal/icu/text/NormalizerBase$1
instanceKlass jdk/internal/icu/text/NormalizerBase$Mode
instanceKlass jdk/internal/icu/text/NormalizerBase
instanceKlass java/text/Normalizer
instanceKlass sun/security/x509/AVAKeyword
instanceKlass java/util/StringJoiner
instanceKlass sun/security/util/SignatureUtil
instanceKlass java/lang/constant/DynamicConstantDesc
instanceKlass java/lang/constant/DirectMethodHandleDesc$1
instanceKlass java/lang/constant/DirectMethodHandleDescImpl$1
instanceKlass java/lang/constant/DirectMethodHandleDescImpl
instanceKlass java/lang/constant/DirectMethodHandleDesc
instanceKlass java/lang/constant/MethodHandleDesc$1
instanceKlass java/lang/constant/MethodHandleDesc
instanceKlass java/lang/constant/MethodTypeDescImpl
instanceKlass java/lang/constant/MethodTypeDesc
instanceKlass java/lang/constant/ReferenceClassDescImpl
instanceKlass java/lang/constant/ConstantUtils
instanceKlass java/lang/constant/ClassDesc
instanceKlass java/lang/constant/ConstantDescs
instanceKlass java/lang/invoke/VarHandle$2
instanceKlass java/lang/invoke/VarHandle$TypesAndInvokers
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass java/lang/invoke/VarHandleByteArrayBase
instanceKlass sun/security/provider/ByteArrayAccess$BE
instanceKlass sun/security/provider/ByteArrayAccess
instanceKlass sun/security/util/MessageDigestSpi2
instanceKlass java/security/MessageDigestSpi
instanceKlass sun/security/pkcs/ESSCertId
instanceKlass sun/security/pkcs/SigningCertificateInfo
instanceKlass java/lang/Byte$ByteCache
instanceKlass sun/security/pkcs/PKCS9Attribute
instanceKlass sun/security/pkcs/PKCS9Attributes
instanceKlass java/time/Instant
instanceKlass java/time/zone/ZoneOffsetTransition
instanceKlass java/time/LocalTime
instanceKlass java/time/temporal/ValueRange
instanceKlass java/time/Duration
instanceKlass java/time/temporal/TemporalAmount
instanceKlass java/time/temporal/TemporalUnit
instanceKlass java/time/temporal/TemporalField
instanceKlass java/time/LocalDate
instanceKlass java/time/chrono/ChronoLocalDate
instanceKlass java/time/ZonedDateTime
instanceKlass java/time/chrono/ChronoZonedDateTime
instanceKlass java/time/LocalDateTime
instanceKlass java/time/chrono/ChronoLocalDateTime
instanceKlass java/time/temporal/Temporal
instanceKlass java/time/zone/ZoneOffsetTransitionRule
instanceKlass java/time/zone/ZoneRules
instanceKlass java/time/temporal/TemporalAdjuster
instanceKlass java/time/temporal/TemporalAccessor
instanceKlass java/time/ZoneId
instanceKlass java/util/regex/CharPredicates
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraints$Holder
instanceKlass sun/security/util/DisabledAlgorithmConstraints$1
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraint
instanceKlass java/util/StringTokenizer
instanceKlass java/security/spec/ECFieldF2m
instanceKlass java/security/spec/ECParameterSpec
instanceKlass java/security/spec/ECPoint
instanceKlass java/security/spec/EllipticCurve
instanceKlass java/security/spec/ECFieldFp
instanceKlass java/security/spec/ECField
instanceKlass sun/security/util/CurveDB
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraints
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass sun/security/util/AbstractAlgorithmConstraints$1
instanceKlass sun/security/util/AlgorithmDecomposer
instanceKlass sun/security/util/DisabledAlgorithmConstraints$JarHolder
instanceKlass java/util/regex/ASCII
instanceKlass sun/security/util/AbstractAlgorithmConstraints
instanceKlass java/security/AlgorithmConstraints
instanceKlass sun/security/pkcs/SignerInfo
instanceKlass java/security/cert/PolicyQualifierInfo
instanceKlass sun/security/x509/CertificatePolicyId
instanceKlass sun/security/x509/PolicyInformation
instanceKlass sun/security/x509/DistributionPoint
instanceKlass sun/security/x509/DNSName
instanceKlass sun/security/x509/URIName
instanceKlass sun/security/x509/GeneralName
instanceKlass sun/security/x509/AccessDescription
instanceKlass sun/security/x509/GeneralNames
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/lang/invoke/VarHandle$1
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/VarHandles
instanceKlass java/lang/System$Logger
instanceKlass jdk/internal/event/EventHelper
instanceKlass sun/security/jca/JCAUtil
instanceKlass sun/security/util/MemoryCache$CacheEntry
instanceKlass sun/security/x509/X509AttributeName
instanceKlass sun/security/x509/KeyIdentifier
instanceKlass java/util/TreeMap$Entry
instanceKlass sun/security/x509/OIDMap$OIDInfo
instanceKlass sun/security/x509/PKIXExtensions
instanceKlass sun/security/x509/OIDMap
instanceKlass sun/security/x509/Extension
instanceKlass java/security/cert/Extension
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass sun/security/x509/CertificateExtensions
instanceKlass sun/security/rsa/RSAUtil
instanceKlass sun/security/util/IOUtils
instanceKlass java/security/interfaces/RSAPublicKey
instanceKlass java/security/interfaces/RSAKey
instanceKlass java/security/spec/PSSParameterSpec
instanceKlass java/security/spec/AlgorithmParameterSpec
instanceKlass java/security/spec/RSAPrivateKeySpec
instanceKlass java/security/spec/RSAPublicKeySpec
instanceKlass java/security/KeyFactorySpi
instanceKlass sun/security/rsa/SunRsaSignEntries
instanceKlass sun/security/jca/ProviderList$ServiceList$1
instanceKlass java/security/KeyFactory
instanceKlass java/security/spec/EncodedKeySpec$1
instanceKlass jdk/internal/access/JavaSecuritySpecAccess
instanceKlass java/security/spec/EncodedKeySpec
instanceKlass java/security/spec/KeySpec
instanceKlass sun/security/util/BitArray
instanceKlass sun/security/x509/X509Key
instanceKlass java/security/PublicKey
instanceKlass java/security/Key
instanceKlass sun/security/x509/CertificateX509Key
instanceKlass java/util/Date
instanceKlass sun/util/calendar/CalendarUtils
instanceKlass sun/util/calendar/CalendarDate
instanceKlass sun/util/calendar/CalendarSystem$GregorianHolder
instanceKlass sun/util/calendar/CalendarSystem
instanceKlass sun/security/x509/CertificateValidity
instanceKlass sun/security/x509/AVA
instanceKlass sun/security/x509/RDN
instanceKlass javax/security/auth/x500/X500Principal
instanceKlass sun/security/x509/X500Name$1
instanceKlass sun/security/x509/X500Name
instanceKlass sun/security/x509/GeneralNameInterface
instanceKlass sun/security/x509/CertificateAlgorithmId
instanceKlass sun/security/x509/SerialNumber
instanceKlass sun/security/x509/CertificateSerialNumber
instanceKlass sun/security/x509/CertificateVersion
instanceKlass sun/security/x509/X509CertInfo
instanceKlass sun/security/x509/CertAttrSet
instanceKlass sun/security/util/Cache$EqualByteArray
instanceKlass java/security/cert/X509Extension
instanceKlass sun/security/jca/GetInstance$Instance
instanceKlass sun/security/util/Cache
instanceKlass jdk/internal/event/Event
instanceKlass sun/security/jca/GetInstance
instanceKlass java/security/cert/CertificateFactorySpi
instanceKlass java/security/cert/CertificateFactory
instanceKlass sun/security/x509/AlgorithmId
instanceKlass sun/security/util/ByteArrayTagOrder
instanceKlass sun/security/util/ByteArrayLexOrder
instanceKlass sun/security/util/DerEncoder
instanceKlass sun/security/util/DerValue
instanceKlass sun/security/util/ObjectIdentifier
instanceKlass sun/security/pkcs/ContentInfo
instanceKlass sun/security/util/DerInputStream
instanceKlass sun/security/pkcs/PKCS7
instanceKlass java/util/Collections$EmptyIterator
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass sun/security/util/SecurityProviderConstants
instanceKlass java/security/Provider$UString
instanceKlass java/security/Provider$Service
instanceKlass sun/security/provider/NativePRNG$NonBlocking
instanceKlass sun/security/provider/NativePRNG$Blocking
instanceKlass sun/security/provider/NativePRNG
instanceKlass sun/security/provider/SunEntries$1
instanceKlass sun/security/provider/SunEntries
instanceKlass sun/security/util/SecurityConstants
instanceKlass java/security/Security$2
instanceKlass jdk/internal/access/JavaSecurityPropertiesAccess
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/io/FileInputStream$1
instanceKlass java/util/Properties$LineReader
instanceKlass java/security/Security$1
instanceKlass java/security/Security
instanceKlass sun/security/jca/ProviderList$2
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
instanceKlass jdk/internal/math/FloatingDecimal
instanceKlass java/security/Provider$EngineDescription
instanceKlass java/security/Provider$ServiceKey
instanceKlass sun/security/jca/ProviderConfig
instanceKlass sun/security/jca/ProviderList
instanceKlass sun/security/jca/Providers
instanceKlass sun/security/util/ManifestDigester$Section
instanceKlass sun/security/util/ManifestDigester$Entry
instanceKlass sun/security/util/ManifestDigester$Position
instanceKlass sun/security/util/ManifestDigester
instanceKlass sun/security/util/ManifestEntryVerifier
instanceKlass java/security/CodeSigner
instanceKlass java/util/jar/JarVerifier
instanceKlass sun/nio/cs/SingleByte
instanceKlass sun/nio/cs/MS1252$Holder
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass sun/launcher/LauncherHelper
instanceKlass lombok/patcher/scripts/ScriptBuilder$SetSymbolDuringMethodCallBuilder
instanceKlass lombok/patcher/scripts/ScriptBuilder$ReplaceMethodCallBuilder
instanceKlass lombok/eclipse/agent/EclipsePatcher$4
instanceKlass lombok/eclipse/agent/EclipsePatcher$3
instanceKlass lombok/patcher/scripts/ScriptBuilder$WrapMethodCallBuilder
instanceKlass lombok/patcher/ScriptManager$WitnessAction
instanceKlass lombok/patcher/scripts/ScriptBuilder$WrapReturnValueBuilder
instanceKlass lombok/patcher/ClassRootFinder
instanceKlass lombok/patcher/scripts/ScriptBuilder$AddFieldBuilder
instanceKlass java/util/Collections$1
instanceKlass lombok/patcher/PatchScript$MethodPatcherFactory
instanceKlass org/lombokweb/asm/ClassVisitor
instanceKlass lombok/patcher/Hook
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass lombok/patcher/MethodTarget
instanceKlass lombok/patcher/scripts/ScriptBuilder$ExitEarlyBuilder
instanceKlass lombok/patcher/scripts/ScriptBuilder
instanceKlass lombok/eclipse/agent/EclipseLoaderPatcher
instanceKlass lombok/eclipse/agent/EclipsePatcher$2
instanceKlass lombok/eclipse/agent/EclipsePatcher$1
instanceKlass java/lang/instrument/ClassDefinition
instanceKlass lombok/patcher/ScriptManager$OurClassFileTransformer
instanceKlass lombok/patcher/Filter$1
instanceKlass lombok/patcher/TransplantMapper$1
instanceKlass java/lang/instrument/ClassFileTransformer
instanceKlass lombok/patcher/ScriptManager
instanceKlass lombok/patcher/TransplantMapper
instanceKlass lombok/patcher/Filter
instanceKlass lombok/patcher/PatchScript
instanceKlass lombok/patcher/TargetMatcher
instanceKlass lombok/eclipse/agent/EclipsePatcher
instanceKlass lombok/core/AgentLauncher$AgentLaunchable
instanceKlass jdk/internal/misc/ScopedMemoryAccess$Scope
instanceKlass lombok/core/AgentLauncher$AgentInfo
instanceKlass lombok/core/AgentLauncher
instanceKlass java/util/IdentityHashMap$IdentityHashMapIterator
instanceKlass lombok/launch/ClassFileMetaData
instanceKlass sun/net/www/MessageHeader
instanceKlass sun/net/www/protocol/jar/JarFileFactory
instanceKlass sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
instanceKlass java/net/URLConnection
instanceKlass java/util/zip/ZipFile$ZipEntryIterator
instanceKlass java/util/WeakHashMap$HashIterator
instanceKlass java/util/BitSet
instanceKlass java/net/URLEncoder
instanceKlass java/net/URLDecoder
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass java/net/URI$Parser
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/nio/file/Paths
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass lombok/launch/PackageShader
instanceKlass java/io/Reader
instanceKlass java/lang/Readable
instanceKlass lombok/launch/Main
instanceKlass sun/instrument/InstrumentationImpl$1
instanceKlass lombok/launch/Agent
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass jdk/internal/loader/Resource
instanceKlass sun/security/action/GetIntegerAction
instanceKlass sun/security/util/Debug
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass java/lang/StringCoding
instanceKlass jdk/internal/util/jar/JarIndex
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass java/lang/Class$1
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/util/Arrays$ArrayItr
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass sun/net/util/URLUtil
instanceKlass sun/instrument/TransformerManager$TransformerInfo
instanceKlass sun/instrument/TransformerManager
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryImpl
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass sun/instrument/InstrumentationImpl
instanceKlass java/lang/instrument/Instrumentation
instanceKlass java/lang/invoke/StringConcatFactory$3
instanceKlass java/lang/invoke/StringConcatFactory$2
instanceKlass java/lang/invoke/StringConcatFactory$1
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass jdk/internal/module/ModuleBootstrap$SafeModuleFinder
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass java/lang/ModuleLayer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/module/ModuleFinder$1
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/lang/module/Resolver
instanceKlass java/lang/module/Configuration
instanceKlass java/util/stream/ForEachOps$ForEachOp
instanceKlass java/util/stream/ForEachOps
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/FindOps
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/function/Consumer
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/ReduceOps
instanceKlass java/util/function/BinaryOperator
instanceKlass java/util/function/BiFunction
instanceKlass java/util/function/BiConsumer
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass java/util/stream/Collector
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/stream/Collectors
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/util/ArrayList$Itr
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass sun/invoke/util/Wrapper$1
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/util/function/Function
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass java/lang/invoke/InnerClassLambdaMetafactory$1
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass jdk/internal/org/objectweb/asm/ClassReader
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass sun/security/action/GetBooleanAction
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass java/util/function/Predicate
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$2
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/reflect/Array
instanceKlass java/lang/invoke/Invokers
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass java/lang/Void
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet
instanceKlass java/lang/invoke/LambdaMetafactory
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass java/lang/Class$3
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/Spliterators$IteratorSpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterator
instanceKlass java/util/Spliterators
instanceKlass jdk/internal/module/DefaultRoots
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass jdk/internal/util/Preconditions
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass java/lang/StringUTF16
instanceKlass java/util/HexFormat
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/lang/Enum
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$all
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass java/lang/reflect/Modifier
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass java/util/Collections
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/lang/ThreadLocal
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass java/io/Writer
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/lang/Integer$IntegerCache
instanceKlass java/lang/CharacterData
instanceKlass java/util/Arrays
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/misc/VM
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass java/lang/ref/ReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/Runtime
instanceKlass java/util/HashMap$Node
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/Map$Entry
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/Math
instanceKlass jdk/internal/reflect/Reflection
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/StringLatin1
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/AbstractCollection
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/AbstractMap
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/invoke/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Dictionary
instanceKlass java/util/Map
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 79 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 7 12 10 12 10 1 7 1 12 10 1 1 12 10 1 8 12 10 1 7 1 1 12 10 12 10 1 1 1 1 7 1 12 10 1 1 100 1 8 1 12 10 3 1 8 5 0 1 1 7 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/Serializable 1 0 5 1 100 1 100
ciInstanceKlass java/lang/CharSequence 1 1 116 1 100 1 100 1 100 1 100 1 1 100 1 100 1 1 100 1 100 1 1 100 1 100 1 1 1 1 1 1 1 12 11 1 1 1 1 1 1 1 16 1 1 12 11 15 16 1 100 1 1 12 10 15 1 1 12 18 1 100 1 1 12 10 1 1 12 11 15 18 1 1 1 100 1 1 12 10 1 1 12 10 1 100 1 1 12 11 1 100 1 1 12 10 12 11 1 1 12 10 1 100 1 1 12 10 10 1 1 12 10 1 1 1 1 1
ciInstanceKlass java/lang/String 1 1 1202 1 7 1 1 7 1 100 1 100 1 7 1 100 1 100 1 7 1 7 1 7 1 1 7 1 7 1 100 1 100 1 1 100 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 1 1 1 3 1 3 1 1 12 10 1 8 12 9 12 9 1 1 12 9 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 100 12 9 1 1 12 10 10 1 1 1 1 100 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 100 1 1 12 10 1 12 10 1 100 1 7 1 1 12 10 1 7 1 7 1 1 12 9 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 7 1 12 9 12 10 1 7 1 12 9 1 1 12 10 1 7 1 1 12 11 1 100 1 12 11 1 1 12 11 1 1 12 10 1 1 12 10 1 100 1 1 12 9 1 1 12 10 1 12 10 1 1 12 11 1 1 12 10 1 100 12 10 1 1 12 10 1 100 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 100 1 12 10 1 1 1 12 10 1 1 1 100 1 12 10 1 1 12 10 1 100 1 100 12 10 1 1 12 10 10 1 1 1 100 1 100 1 12 10 12 10 1 1 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 100 1 12 10 1 100 11 1 12 11 1 12 11 12 10 1 12 10 12 10 1 1 12 10 10 1 100 1 1 12 10 1 100 1 12 10 1 12 10 1 100 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 1 12 10 1 12 10 1 1 1 1 1 12 10 1 100 12 10 12 10 1 12 10 1 1 12 10 1 100 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 1 1 1 1 1 1 1 1 3 1 1 3 1 12 10 12 10 1 1 12 10 12 10 1 12 10 12 10 12 10 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 12 10 12 10 1 12 10 1 12 10 1 12 10 10 1 100 10 1 8 1 1 12 10 1 12 10 1 8 1 1 12 10 12 10 1 12 10 1 8 10 1 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 12 10 1 1 100 10 10 1 1 12 10 1 1 1 1 12 10 12 10 10 1 1 1 12 10 1 12 10 1 12 10 1 100 10 12 10 1 1 1 100 10 12 10 1 1 12 10 1 1 1 12 10 1 12 10 10 1 1 1 12 10 10 1 100 10 12 10 1 1 1 1 12 10 1 1 1 12 10 1 1 1 100 10 1 12 10 1 12 10 1 12 10 12 10 12 10 11 12 11 1 12 10 1 1 1 1 12 10 1 1 1 12 10 10 1 12 10 1 12 10 1 12 9 1 7 1 1 12 11 1 10 12 10 1 1 12 10 10 1 12 10 1 12 10 1 1 12 10 1 1 12 9 1 12 10 10 1 12 10 12 10 10 1 12 10 12 10 10 12 10 10 1 12 10 1 1 12 10 12 10 10 12 10 12 10 12 10 12 10 10 1 12 10 1 1 1 12 10 1 12 10 10 1 1 1 1 12 10 1 7 1 1 12 10 1 1 1 12 10 10 1 1 7 1 12 10 1 11 12 10 1 1 1 1 12 10 1 1 12 10 1 7 12 10 1 12 10 1 1 100 10 12 10 1 12 10 1 12 10 1 100 1 12 10 1 12 10 1 100 1 8 10 10 1 12 10 1 1 1 8 12 10 3 3 1 7 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 7 1 1 12 11 1 7 1 12 10 1 12 10 1 1 1 1 12 10 1 12 10 1 1 8 1 1 12 10 1 12 10 1 1 1 100 1 1 12 11 1 100 1 12 11 1 1 12 11 1 12 10 1 1 1 12 10 10 1 7 1 1 12 10 12 10 1 12 10 10 12 10 1 1 12 10 10 1 12 10 10 1 12 10 10 1 12 10 10 1 1 12 10 1 1 1 1 12 10 10 1 100 1 12 10 1 8 1 12 10 16 1 12 10 15 16 1 100 1 1 12 10 15 1 1 12 18 1 1 12 11 3 1 12 10 15 1 12 18 1 1 12 10 15 1 12 18 1 8 1 100 1 1 12 10 1 1 12 11 12 10 10 1 12 10 10 1 1 1 12 11 1 1 12 10 1 12 11 1 12 10 15 18 1 3 11 1 7 1 12 10 11 11 12 10 1 1 1 12 10 1 8 1 12 10 1 12 10 1 1 12 10 10 12 10 1 1 1 1 100 12 11 1 1 12 10 10 1 7 1 1 12 10 1 10 1 7 10 1 12 10 10 1 1 12 10 1 1 1 8 10 1 12 10 1 1 1 1 8 1 8 1 1 12 10 1 12 10 10 12 10 1 1 7 12 10 1 1 100 12 10 1 1 100 12 10 1 1 8 1 12 10 1 1 12 10 1 1 100 10 12 10 1 8 1 8 10 1 1 8 1 8 1 8 1 8 1 1 12 10 1 12 10 1 8 1 1 1 1 100 1 1 12 10 1 1 1 12 10 1 1 100 12 10 12 10 10 12 10 12 10 1 1 7 12 9 10 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciInstanceKlass java/lang/Class 1 1 1462 1 7 1 1 7 1 100 1 100 1 100 1 100 1 100 1 100 1 7 1 7 1 7 1 100 1 7 1 100 1 7 1 1 1 1 7 1 7 1 1 7 1 7 1 1 1 100 1 7 1 1 100 1 1 100 1 100 1 1 100 1 100 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 1 1 1 7 10 1 1 12 10 1 8 1 12 10 1 8 1 8 1 7 1 1 12 10 1 12 10 12 10 1 10 1 12 10 1 1 12 10 1 1 12 10 1 7 1 12 10 1 12 10 1 12 10 1 12 10 1 8 1 12 10 1 8 1 12 10 1 8 1 8 1 1 12 10 1 7 1 1 12 10 1 16 1 1 12 10 15 16 1 7 1 1 12 10 15 1 1 12 18 1 7 1 1 12 11 1 8 1 8 1 8 1 7 1 1 12 10 1 1 12 11 1 100 1 8 1 12 10 1 1 100 1 1 12 11 1 1 12 10 11 1 100 1 8 1 12 11 15 1 16 18 1 8 1 12 10 1 1 1 1 7 1 1 7 1 12 10 1 7 1 1 12 10 1 1 12 10 1 1 1 7 1 1 12 10 1 100 1 1 12 9 1 100 1 1 12 10 1 1 1 1 7 1 12 10 1 1 12 10 1 16 1 7 1 12 10 15 16 1 1 12 18 1 7 1 1 12 10 1 12 10 1 7 10 1 1 1 7 1 7 1 1 1 1 7 1 100 1 1 12 10 12 9 1 8 1 12 10 1 100 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 10 1 1 12 10 1 12 10 1 100 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 1 1 12 9 1 12 10 1 1 12 10 1 12 10 12 9 1 12 9 1 1 1 12 10 1 100 10 1 1 1 1 12 10 12 10 1 1 1 1 12 10 10 1 100 1 12 9 1 12 10 1 8 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1 12 10 1 1 12 10 1 12 10 1 12 9 7 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 1 1 7 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 100 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 100 1 7 10 10 10 1 1 12 10 1 1 12 10 12 10 1 1 12 10 1 100 1 8 10 1 12 10 7 12 10 1 1 100 1 12 11 1 100 1 12 10 1 12 10 1 1 1 1 12 10 1 100 1 12 10 10 1 1 12 10 1 7 1 8 1 1 12 10 1 1 12 10 1 8 1 1 12 9 1 12 10 12 10 1 12 10 12 10 1 100 1 1 12 9 1 12 10 1 12 9 12 10 1 12 10 1 12 10 10 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 1 1 12 10 1 1 1 7 1 12 10 10 1 7 1 1 12 10 1 1 1 1 12 10 1 1 12 10 10 1 1 1 1 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 12 10 1 1 1 1 7 10 1 100 12 10 1 12 11 1 1 1 1 100 1 1 12 10 1 12 10 1 7 1 1 12 10 1 1 12 10 10 1 1 12 10 1 100 10 1 1 12 10 1 100 1 1 12 10 1 100 1 100 1 12 10 12 10 1 1 10 1 12 10 12 10 1 1 12 10 1 12 10 1 100 1 1 12 10 1 7 1 12 11 1 12 10 1 12 10 1 1 1 12 9 1 12 10 1 12 10 12 9 1 100 10 1 1 12 9 12 10 1 100 1 12 10 1 1 1 12 9 1 100 1 1 12 10 12 10 1 12 10 1 100 1 12 10 1 12 10 12 10 1 1 12 10 1 1 1 1 100 10 1 8 1 12 10 11 1 1 12 11 1 7 1 12 11 1 12 11 1 8 1 12 10 1 1 12 10 1 12 9 12 9 1 7 1 12 10 1 12 9 1 1 12 10 1 10 1 12 10 1 1 12 10 1 1 100 1 12 10 1 100 1 12 10 12 9 12 10 1 12 9 1 12 10 1 1 1 1 1 12 10 1 1 1 1 12 9 1 12 9 100 1 12 10 1 1 12 10 1 12 9 1 100 10 1 1 12 10 1 1 12 10 1 1 100 11 1 1 12 9 1 12 9 1 12 10 1 12 9 1 12 9 1 12 10 1 1 12 10 1 12 9 10 1 1 12 10 10 1 1 12 10 12 10 10 1 12 10 12 9 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 8 1 10 1 1 1 8 1 1 12 10 15 16 18 1 8 1 8 1 1 12 10 1 12 9 1 12 9 12 10 1 7 1 7 12 10 12 9 10 1 1 1 12 10 10 12 9 1 8 12 10 1 12 10 1 1 12 10 1 100 1 1 12 9 1 100 1 8 10 1 7 4 10 12 10 1 1 12 11 1 1 12 10 1 100 1 1 12 10 10 1 8 1 8 1 1 1 1 1 1 1 12 10 1 12 9 12 11 1 7 1 1 12 11 1 1 1 1 12 9 1 100 1 1 12 10 1 1 1 7 1 12 10 1 1 1 1 12 10 1 12 9 9 1 1 12 10 1 1 12 10 12 10 12 10 1 1 12 10 1 12 11 1 12 11 1 100 1 1 12 10 1 12 10 1 100 1 12 11 1 100 1 1 12 10 1 12 10 10 1 12 11 1 1 12 11 1 12 10 1 1 1 12 10 1 1 12 9 1 1 1 1 1 100 1 12 9 12 10 1 100 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 7 1 1 12 10 1 12 10 1 8 12 10 1 12 10 1 12 10 1 1 1 1 1 100 1 12 10 1 100 1 1 12 11 1 1 12 10 1 1 12 10 16 1 12 10 15 16 1 1 12 18 1 1 12 11 1 12 10 15 18 1 12 11 1 16 1 1 12 10 15 16 1 12 18 1 12 11 12 10 1 12 10 1 12 10 12 10 1 8 1 12 10 1 7 12 9 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/Cloneable 1 0 5 1 100 1 100
instanceKlass org/apache/aries/spifly/Util$WrapperCL
instanceKlass org/apache/aries/spifly/MultiDelegationClassloader
instanceKlass lombok/launch/ShadowClassLoader
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$1
instanceKlass lombok/launch/ShadowClassLoader
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 978 1 7 1 7 1 7 1 100 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 7 1 1 12 10 1 7 1 100 1 1 12 9 1 7 1 1 12 10 1 7 1 1 12 10 1 1 12 10 12 9 1 1 12 10 1 100 12 10 1 1 1 12 10 1 7 1 1 12 10 1 100 1 8 1 12 10 1 7 1 1 12 10 1 100 12 10 1 10 10 1 7 1 7 7 1 12 10 1 12 10 12 9 10 1 7 1 1 12 10 12 9 12 9 12 9 12 9 12 9 12 9 1 7 1 12 10 12 9 1 1 12 10 1 1 12 10 12 9 12 9 1 100 12 9 1 12 10 12 9 1 1 12 10 1 7 10 1 8 1 1 12 10 1 12 10 10 1 7 1 1 12 10 1 7 1 1 12 10 1 8 1 12 10 12 10 1 1 12 10 1 1 1 1 7 1 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 10 1 12 10 1 12 10 1 1 1 1 100 1 12 10 1 1 12 10 1 100 12 10 1 12 10 1 12 10 1 100 1 12 10 1 100 1 1 12 10 10 1 1 1 1 1 7 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 100 1 8 10 1 8 1 12 10 1 12 10 1 100 1 8 1 1 12 10 1 1 12 10 10 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 7 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 12 10 1 1 12 10 12 10 1 1 1 7 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 1 1 1 1 12 10 1 1 12 10 1 8 12 9 1 1 12 10 1 8 1 8 1 7 1 12 10 1 100 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 1 7 1 12 10 1 1 7 1 12 10 12 10 1 7 10 1 1 1 1 7 12 10 1 100 1 12 10 10 1 7 1 12 10 1 1 1 1 16 1 1 12 10 15 1 16 1 100 1 1 12 10 15 1 12 18 1 100 1 1 12 10 1 7 1 1 12 10 1 1 1 7 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 1 1 12 10 1 100 1 12 10 1 1 1 12 10 1 1 1 7 1 12 10 1 12 10 1 8 1 100 10 1 12 10 12 9 1 7 1 12 10 1 12 10 1 1 100 1 100 1 8 1 12 10 10 1 8 1 8 1 1 12 10 1 1 12 10 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 100 1 12 10 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 12 10 12 10 1 100 1 1 12 9 1 1 12 10 1 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 8 1 12 10 1 7 16 1 1 12 10 15 1 16 1 1 12 18 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 1 8 1 12 10 1 1 1 12 10 1 16 1 1 12 10 15 16 1 12 18 1 100 1 1 12 11 1 100 1 1 12 10 12 10 10 1 1 1 12 11 10 1 12 10 15 18 1 1 1 12 10 1 100 12 11 16 1 1 12 10 15 16 1 12 18 1 1 12 11 1 1 1 1 12 10 1 1 12 10 12 10 1 100 1 100 1 8 10 1 1 12 10 1 8 1 8 1 100 1 12 10 12 10 1 100 10 1 12 10 1 8 1 8 1 8 1 12 10 1 1 1 1 12 10 1 1 1 12 10 1 1 1 100 1 1 12 10 1 100 1 12 11 1 1 1 100 10 1 11 1 12 10 1 12 10 1 1 12 10 1 100 1 12 9 1 1 12 9 12 9 1 12 9 1 12 9 1 1 1 12 9 8 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 10 12 10 1 1 12 11 1 100 1 1 12 10 1 100 1 12 10 1 12 10 12 10 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/lang/System 1 1 733 1 7 1 100 1 100 1 100 1 100 1 100 1 7 1 100 1 1 1 1 7 1 7 1 1 1 100 1 100 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 9 1 1 1 12 10 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 9 1 7 1 1 12 10 1 100 12 11 1 100 1 100 1 1 1 100 1 100 1 1 12 10 12 10 1 1 12 10 1 100 1 8 1 12 10 1 100 1 1 12 10 1 1 1 1 7 1 1 12 10 1 16 1 7 1 1 12 10 15 16 1 100 1 1 12 10 15 1 1 12 18 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 100 1 1 1 1 1 1 1 12 10 1 7 1 1 12 10 1 1 12 9 1 100 1 1 12 10 1 1 12 11 12 10 1 1 12 10 1 100 10 1 1 12 10 1 8 1 12 10 1 8 1 12 10 1 7 12 9 1 8 1 7 1 1 12 10 1 12 10 1 100 1 8 10 1 100 12 9 1 8 1 1 12 10 1 100 1 1 12 10 1 8 1 12 10 1 12 10 8 1 12 10 1 1 12 10 12 10 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 1 1 1 7 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 1 1 12 10 1 12 10 1 7 12 10 1 12 10 1 1 100 1 8 1 12 10 1 12 10 1 1 12 10 1 100 1 8 10 1 12 10 1 100 1 8 10 1 1 8 1 100 12 10 1 1 8 12 10 1 1 1 100 1 8 10 1 1 12 10 1 7 1 12 10 1 1 100 1 1 12 10 15 1 16 1 12 18 1 100 1 1 12 9 1 12 10 1 1 12 10 1 1 12 10 1 1 1 7 1 1 12 10 12 10 1 12 10 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 1 100 1 7 1 12 10 1 12 10 1 12 10 1 1 1 7 1 1 12 10 1 100 1 8 10 12 9 12 9 1 12 10 1 12 10 1 12 10 1 1 12 10 1 100 1 8 1 1 12 10 1 8 1 1 12 11 12 10 1 1 12 11 1 7 1 1 12 11 1 7 1 12 11 1 12 11 1 12 11 1 12 10 1 8 1 1 12 10 1 8 1 8 1 8 1 8 1 12 11 1 12 10 1 1 12 10 1 12 10 1 7 1 12 10 1 8 1 7 1 7 1 12 9 1 12 10 1 7 12 9 10 12 9 1 7 12 10 1 8 12 10 1 8 1 7 1 12 10 1 12 10 1 7 1 1 12 10 1 1 12 10 1 7 1 1 12 10 12 10 1 1 1 100 1 7 1 1 12 10 12 9 1 8 12 10 1 1 7 1 1 12 10 1 7 1 1 12 10 1 8 10 1 8 1 8 1 8 1 8 10 1 7 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 100 1 1 12 10 1 100 10 1 8 10 10 1 1 12 10 1 1 12 10 1 8 1 12 10 1 8 1 12 10 1 1 12 10 10 1 12 10 1 12 10 12 9 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/BufferedInputStream
staticfield java/lang/System out Ljava/io/PrintStream; java/io/PrintStream
staticfield java/lang/System err Ljava/io/PrintStream; java/io/PrintStream
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 353 1 7 1 7 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 1 8 1 1 8 1 1 8 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 12 9 1 1 12 10 1 12 9 1 1 1 1 12 10 1 100 1 1 1 12 10 1 1 1 1 100 1 100 10 1 8 1 1 12 10 1 8 1 100 1 12 10 10 12 10 1 100 1 8 10 1 1 1 12 10 1 7 1 12 10 12 10 1 8 1 1 100 1 1 12 9 1 12 10 12 10 1 12 10 1 100 10 1 7 1 1 12 10 1 100 1 1 12 11 1 1 12 10 1 1 12 10 1 1 12 10 7 1 8 1 12 10 1 1 12 10 100 1 8 1 1 12 10 12 10 1 8 1 12 9 1 100 1 12 10 1 100 10 1 12 11 1 8 1 8 1 7 1 12 10 1 8 1 12 10 1 8 1 12 10 12 9 1 12 10 1 1 12 10 12 9 1 1 12 10 1 1 1 100 1 8 12 10 1 1 1 100 1 100 1 100 1 12 10 1 1 12 10 1 100 1 100 1 1 12 10 1 12 10 1 100 1 1 12 11 1 100 1 1 12 11 1 12 11 1 1 12 10 10 10 1 1 12 9 1 8 1 1 1 12 10 10 1 100 1 8 10 1 1 12 11 1 8 1 1 1 12 9 1 100 1 12 10 1 11 12 9 1 1 12 11 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
instanceKlass com/google/common/util/concurrent/ExecutionError
instanceKlass java/util/ServiceConfigurationError
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
instanceKlass java/lang/ThreadDeath
ciInstanceKlass java/lang/Error 1 1 27 1 100 1 7 1 1 5 0 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1
ciInstanceKlass java/lang/ThreadDeath 0 0 15 1 100 1 100 1 1 5 0 1 1 12 10 1 1
instanceKlass org/eclipse/jface/text/BadLocationException
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$InvalidBindingException
instanceKlass org/eclipse/jdt/core/compiler/InvalidInputException
instanceKlass org/eclipse/jdt/internal/compiler/classfmt/ClassFormatException
instanceKlass java/text/ParseException
instanceKlass org/codehaus/plexus/component/configurator/expression/ExpressionEvaluationException
instanceKlass org/codehaus/plexus/classworlds/ClassWorldException
instanceKlass org/codehaus/plexus/PlexusContainerException
instanceKlass org/apache/maven/cli/internal/ExtensionResolutionException
instanceKlass org/apache/maven/plugin/version/PluginVersionResolutionException
instanceKlass org/codehaus/plexus/component/configurator/ComponentConfigurationException
instanceKlass org/apache/maven/plugin/InvalidPluginDescriptorException
instanceKlass org/apache/maven/plugin/MojoNotFoundException
instanceKlass org/apache/maven/plugin/PluginDescriptorParsingException
instanceKlass org/apache/maven/artifact/resolver/AbstractArtifactResolutionException
instanceKlass org/apache/maven/plugin/PluginResolutionException
instanceKlass org/apache/maven/plugin/PluginConfigurationException
instanceKlass org/apache/maven/plugin/PluginManagerException
instanceKlass org/apache/maven/settings/building/SettingsBuildingException
instanceKlass org/apache/maven/artifact/InvalidRepositoryException
instanceKlass org/apache/maven/execution/MavenExecutionRequestPopulationException
instanceKlass org/apache/maven/project/ProjectBuildingException
instanceKlass org/eclipse/aether/RepositoryException
instanceKlass org/codehaus/plexus/component/repository/exception/ComponentLookupException
instanceKlass org/apache/maven/project/DuplicateProjectException
instanceKlass org/codehaus/plexus/util/dag/CycleDetectedException
instanceKlass ch/qos/logback/core/util/DynamicClassLoadingException
instanceKlass ch/qos/logback/core/util/IncompatibleClassException
instanceKlass ch/qos/logback/core/joran/spi/JoranException
instanceKlass java/util/concurrent/ExecutionException
instanceKlass java/net/URISyntaxException
instanceKlass java/util/concurrent/TimeoutException
instanceKlass org/osgi/service/application/ApplicationException
instanceKlass java/lang/CloneNotSupportedException
instanceKlass org/eclipse/core/runtime/CoreException
instanceKlass org/osgi/service/prefs/BackingStoreException
instanceKlass org/apache/felix/scr/impl/inject/methods/SuitableMethodNotAccessibleException
instanceKlass org/xml/sax/SAXException
instanceKlass javax/xml/parsers/ParserConfigurationException
instanceKlass org/osgi/service/resolver/ResolutionException
instanceKlass java/security/GeneralSecurityException
instanceKlass org/eclipse/osgi/container/ModuleContainer$ResolutionLockException
instanceKlass java/security/PrivilegedActionException
instanceKlass org/osgi/framework/InvalidSyntaxException
instanceKlass java/lang/InterruptedException
instanceKlass org/osgi/framework/BundleException
instanceKlass java/lang/instrument/UnmodifiableClassException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 27 1 100 1 7 1 1 5 0 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessor$1FoundRelevantDeltaException
instanceKlass org/eclipse/jdt/internal/codeassist/complete/InvalidCursorLocation
instanceKlass org/eclipse/jdt/internal/codeassist/complete/CompletionNodeFound
instanceKlass org/eclipse/jdt/internal/compiler/problem/AbortCompilation
instanceKlass org/eclipse/jdt/internal/compiler/lookup/SourceTypeCollisionException
instanceKlass org/eclipse/jdt/internal/core/builder/MissingSourceFileException
instanceKlass org/eclipse/jdt/internal/core/builder/ImageBuilderInternalException
instanceKlass java/lang/NegativeArraySizeException
instanceKlass org/eclipse/jdt/internal/core/ClasspathEntry$AssertionFailedException
instanceKlass com/google/common/cache/CacheLoader$InvalidCacheLoadException
instanceKlass com/google/common/util/concurrent/UncheckedExecutionException
instanceKlass ch/qos/logback/core/LogbackException
instanceKlass org/objectweb/asm/tree/UnsupportedClassVersionException
instanceKlass java/lang/reflect/UndeclaredThrowableException
instanceKlass org/eclipse/core/internal/events/BuildManager$JobManagerSuspendedException
instanceKlass org/eclipse/core/internal/localstore/IsSynchronizedVisitor$ResourceChangedException
instanceKlass java/lang/IllegalCallerException
instanceKlass org/eclipse/core/internal/dtree/ObjectNotFoundException
instanceKlass org/eclipse/core/internal/utils/WrappedRuntimeException
instanceKlass org/eclipse/core/runtime/OperationCanceledException
instanceKlass org/eclipse/equinox/internal/provisional/frameworkadmin/FrameworkAdminRuntimeException
instanceKlass org/osgi/util/promise/FailedPromisesException
instanceKlass org/eclipse/core/runtime/InvalidRegistryObjectException
instanceKlass java/util/concurrent/RejectedExecutionException
instanceKlass org/eclipse/core/runtime/AssertionFailedException
instanceKlass org/osgi/service/component/ComponentException
instanceKlass java/util/MissingResourceException
instanceKlass org/osgi/framework/hooks/weaving/WeavingException
instanceKlass java/util/ConcurrentModificationException
instanceKlass org/osgi/framework/ServiceException
instanceKlass java/lang/TypeNotPresentException
instanceKlass org/eclipse/osgi/framework/util/ThreadInfoReport
instanceKlass org/eclipse/osgi/storage/Storage$StorageException
instanceKlass java/util/NoSuchElementException
instanceKlass java/lang/SecurityException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/IllegalStateException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 27 1 100 1 7 1 1 5 0 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1
instanceKlass com/sun/jna/Native$6
instanceKlass org/eclipse/osgi/internal/permadmin/EquinoxSecurityManager
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$ClassContext
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder$Finder
instanceKlass org/eclipse/osgi/internal/url/MultiplexingFactory$InternalSecurityManager
ciInstanceKlass java/lang/SecurityManager 1 1 521 1 7 1 7 1 1 1 1 3 1 100 1 100 1 7 1 7 1 1 7 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 1 7 1 1 12 10 1 100 1 8 1 12 10 1 1 12 10 1 100 1 1 1 1 1 1 100 1 1 12 10 10 1 1 100 10 1 100 10 1 1 100 1 1 12 9 1 1 1 7 1 1 12 10 1 12 10 1 7 1 12 10 1 1 1 100 1 8 10 12 9 1 12 9 1 1 8 1 12 9 1 1 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 1 1 100 10 1 1 12 10 1 100 1 8 1 12 10 1 8 1 1 8 1 8 1 1 1 8 1 8 1 8 1 12 10 1 1 8 1 8 1 1 8 1 1 1 8 1 8 1 100 1 1 12 10 1 1 12 10 1 8 1 100 1 8 10 1 8 1 8 1 1 1 8 1 8 1 1 8 1 1 1 100 1 12 10 1 8 1 1 1 1 100 1 8 1 8 10 1 1 1 8 1 1 1 12 10 1 100 1 8 10 1 1 12 10 100 1 12 10 1 12 10 1 12 10 1 1 1 7 1 1 12 10 1 12 10 1 7 1 12 10 1 7 1 1 12 11 1 16 1 7 1 1 12 10 15 1 16 1 7 1 1 12 10 15 1 1 12 18 1 7 1 1 12 11 1 16 1 1 12 10 15 1 16 1 1 12 18 1 1 12 11 1 12 10 15 16 18 1 7 11 15 1 16 18 1 12 11 1 16 1 12 10 15 16 1 12 18 1 1 12 11 1 12 9 12 9 12 9 12 9 1 1 7 1 12 10 1 12 10 1 12 10 1 1 12 10 15 16 1 12 18 1 12 10 15 1 16 18 1 7 1 12 10 1 12 11 15 1 12 18 1 12 10 1 1 12 10 15 16 18 10 15 1 16 18 1 1 8 1 12 10 12 9 1 7 1 12 11 1 8 1 12 10 1 1 12 10 12 10 12 9 1 12 10 1 1 12 10 1 1 8 10 12 9 1 8 1 12 10 1 1 8 1 1 100 10 1 12 10 10 1 7 1 1 12 9 1 1 12 11 1 12 10 1 12 11 1 12 10 1 7 10 1 1 12 10 12 10 1 1 1 1 1 1 1 1
staticfield java/lang/SecurityManager packageAccessLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/SecurityManager packageDefinitionLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/SecurityManager nonExportedPkgs Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$GenerationProtectionDomain
ciInstanceKlass java/security/ProtectionDomain 1 1 283 1 7 1 7 1 7 1 100 1 7 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 10 12 9 12 9 12 9 1 7 1 12 10 1 7 1 12 9 1 100 12 9 1 7 12 9 12 9 1 1 100 100 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 1 12 10 12 10 1 12 9 1 100 1 12 9 1 1 12 10 10 1 100 1 100 1 1 12 10 1 1 1 8 1 100 1 8 1 12 10 1 7 10 1 100 1 12 10 1 1 12 10 1 8 11 1 8 12 10 1 8 1 8 1 12 10 1 12 10 1 12 10 12 10 1 8 1 12 10 1 8 1 8 1 100 1 100 1 1 12 10 1 100 1 1 12 9 12 10 1 100 1 1 12 9 1 1 12 10 1 12 10 1 100 1 1 12 10 10 1 100 1 12 10 1 1 12 10 1 100 1 100 1 12 11 1 12 11 1 1 12 11 1 100 1 12 10 10 1 1 12 11 1 1 12 11 1 12 10 1 12 10 1 100 1 12 10 1 12 11 12 10 1 1 8 1 8 1 7 1 1 12 10 10 1 7 1 1 12 10 1 1 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/AccessControlContext 1 1 318 1 7 1 7 1 1 1 1 3 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 12 9 1 7 1 1 12 10 1 8 1 100 1 1 12 10 1 1 1 12 10 12 9 12 9 12 9 7 1 1 12 10 1 100 1 12 10 1 100 1 1 12 11 1 12 11 1 12 11 1 1 12 11 1 7 1 1 12 11 1 1 12 10 1 7 1 1 12 10 1 100 1 1 12 9 1 100 1 1 12 10 1 100 1 100 1 1 12 11 10 1 7 1 100 1 8 1 12 10 1 1 12 10 1 7 1 7 1 1 12 10 12 9 12 9 12 9 12 9 12 9 12 9 1 12 9 1 1 1 1 1 1 7 1 1 12 10 1 1 12 9 1 12 10 1 100 12 10 1 8 1 1 12 10 1 1 12 10 1 100 1 1 12 10 1 100 10 1 1 12 10 1 100 1 1 12 10 10 1 8 1 100 1 12 10 1 8 1 100 1 12 10 1 8 1 8 1 12 10 1 8 1 12 10 1 8 1 12 10 1 12 10 1 1 12 10 1 8 1 8 1 12 10 1 1 12 10 1 12 10 1 8 1 12 10 1 12 10 10 1 1 8 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 10 10 1 12 10 1 1 1 1 1 1 1 1
ciInstanceKlass java/security/AccessController 1 1 258 1 7 1 100 1 1 1 1 3 1 100 1 1 1 1 1 12 10 1 1 1 1 1 7 1 1 12 10 1 1 12 10 1 1 1 12 10 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 1 1 12 10 1 100 1 8 1 12 10 1 7 1 12 10 1 100 1 1 12 10 1 1 1 100 1 100 1 100 1 12 10 1 7 1 1 12 10 12 10 1 12 10 1 1 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 100 1 1 12 9 1 100 1 1 12 10 1 12 10 1 12 10 1 1 12 9 1 1 1 1 1 1 1 12 10 1 1 12 10 12 9 10 1 100 10 1 7 1 1 12 11 1 7 1 12 10 1 11 1 1 12 10 1 1 1 1 12 10 1 12 10 1 1 1 100 1 8 1 1 12 10 1 8 1 100 1 1 12 10 1 8 1 100 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 100 1 8 1 100 1 12 10 1 8 1 8 1 12 10 1 8 1 12 10 12 10 1 1 12 10 1 1 1 1 1 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
instanceKlass java/net/URLClassLoader
instanceKlass jdk/internal/loader/BuiltinClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 81 1 7 1 7 1 7 1 100 1 7 1 1 1 1 1 1 1 12 10 1 7 1 12 10 12 9 1 12 10 1 12 10 1 1 1 1 1 12 10 1 12 10 1 1 1 12 10 1 1 1 7 10 1 12 10 1 12 10 1 7 1 1 12 11 1 7 1 1 12 11 1 1 1 12 10 1 1 1 1 1
instanceKlass java/lang/InstantiationException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 24 1 100 1 7 1 1 5 0 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 80 1 7 1 7 1 100 1 100 1 1 100 1 100 1 1 1 5 0 1 1 1 1 1 7 1 12 10 1 1 12 10 1 1 1 12 10 1 1 1 100 1 1 12 10 1 8 1 1 12 10 1 12 10 1 1 1 1 12 10 1 1 12 10 1 12 10 1 1 7 1 12 10 12 9 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
instanceKlass org/eclipse/m2e/core/embedder/ArtifactRepositoryRef
instanceKlass org/eclipse/m2e/core/project/configurator/ProjectConfigurationRequest
instanceKlass org/eclipse/m2e/core/embedder/ArtifactKey
instanceKlass org/eclipse/m2e/core/embedder/MavenConfigurationChangeEvent
instanceKlass org/eclipse/m2e/core/embedder/MavenSettingsLocations
instanceKlass jdk/internal/misc/ThreadTracker$ThreadRef
instanceKlass sun/security/pkcs/SignerInfo$AlgorithmInfo
ciInstanceKlass java/lang/Record 1 1 16 1 100 1 7 1 1 12 10 1 1 1 1 1 1 1
instanceKlass java/lang/ClassFormatError
instanceKlass java/lang/UnsatisfiedLinkError
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 21 1 100 1 7 1 1 5 0 1 1 12 10 1 12 10 1 12 10 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 0 0 18 1 100 1 100 1 1 5 0 1 1 12 10 1 12 10 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 18 1 100 1 100 1 1 5 0 1 1 12 10 1 12 10 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 18 1 100 1 100 1 1 5 0 1 1 12 10 1 12 10 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 24 1 100 1 7 1 1 5 0 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1
ciInstanceKlass java/lang/InternalError 1 1 24 1 100 1 7 1 1 5 0 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 18 1 100 1 100 1 1 5 0 1 1 12 10 1 12 10 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 18 1 100 1 100 1 1 5 0 1 1 12 10 1 12 10 1 1
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 18 1 100 1 100 1 1 5 0 1 1 12 10 1 12 10 1 1
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 179 1 7 1 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 7 1 12 9 1 1 12 10 1 12 10 12 9 12 10 12 9 1 100 12 9 1 7 1 12 10 1 12 10 12 10 1 1 100 12 10 1 12 10 1 1 1 1 12 9 1 1 1 1 12 10 1 12 10 1 1 12 10 1 12 9 1 100 1 100 1 12 10 12 9 1 1 1 1 1 1 12 9 1 1 100 10 1 1 1 12 10 1 10 1 1 1 1 7 1 12 10 1 7 1 1 12 10 1 1 12 10 1 7 1 12 10 1 8 1 12 10 1 1 12 10 1 1 12 10 1 12 10 10 1 7 1 1 12 10 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
instanceKlass java/io/ClassCache$CacheRef
instanceKlass org/eclipse/sisu/inject/MildKeys$Soft
instanceKlass org/eclipse/core/internal/registry/ReferenceMap$SoftRef
instanceKlass sun/util/locale/provider/LocaleResources$ResourceReference
instanceKlass sun/util/resources/Bundles$BundleReference
instanceKlass java/util/ResourceBundle$BundleReference
instanceKlass sun/util/locale/LocaleObjectCache$CacheEntry
instanceKlass sun/security/util/MemoryCache$SoftCacheEntry
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 32 1 7 1 1 7 1 1 1 1 1 1 12 10 12 9 12 9 1 1 12 10 1 1 1 12 10 1 100 1 1 1
instanceKlass org/eclipse/sisu/inject/MildKeys$Weak
instanceKlass com/sun/jna/CallbackReference
instanceKlass java/util/ResourceBundle$KeyElementReference
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass sun/nio/ch/FileLockTable$FileLockReference
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 17 1 100 1 1 7 1 1 1 12 10 1 1 12 10 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 33 1 100 1 1 7 1 1 1 12 10 1 1 1 1 12 10 1 1 1 12 10 1 1 1 100 1 8 1 12 10 1 1
instanceKlass com/sun/jna/internal/Cleaner$CleanerRef
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 24 1 100 1 1 7 1 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 137 1 7 1 1 7 1 7 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 12 10 12 9 12 9 12 9 12 9 1 7 1 100 1 1 1 1 12 10 1 1 1 1 12 10 12 9 1 100 1 12 10 1 100 1 100 1 12 11 1 12 10 1 1 12 10 1 100 1 1 12 10 1 1 100 1 1 12 10 10 12 10 1 1 7 1 1 12 10 1 7 10 10 1 7 1 1 12 10 1 1 12 10 1 7 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass com/sun/jna/internal/Cleaner$CleanerThread
instanceKlass org/eclipse/core/internal/jobs/InternalWorker
instanceKlass org/eclipse/core/internal/jobs/Worker
instanceKlass java/util/TimerThread
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager$EventThread
instanceKlass java/util/concurrent/ForkJoinWorkerThread
instanceKlass org/eclipse/equinox/launcher/Main$SplashHandler
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
ciInstanceKlass java/lang/Thread 1 1 559 1 7 1 7 1 7 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 1 12 9 1 1 1 1 12 10 12 9 12 9 1 100 1 100 1 1 1 1 1 7 1 1 100 1 8 1 1 12 10 3 1 8 5 0 12 10 1 1 12 10 12 9 12 9 12 9 12 9 1 100 1 8 10 1 7 1 100 1 100 12 9 1 7 1 1 12 10 1 100 1 1 12 10 10 1 12 10 1 1 12 10 1 1 12 10 1 100 1 1 12 9 1 1 12 10 1 12 10 12 9 1 1 12 10 1 12 10 12 9 1 1 12 10 12 9 1 7 1 1 12 10 12 9 12 9 1 1 12 10 1 1 12 10 12 9 12 10 12 9 1 1 1 100 10 1 7 10 1 8 1 1 12 10 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 1 1 1 1 12 9 1 100 10 1 1 12 10 1 12 10 1 12 10 1 12 11 1 1 100 1 1 12 9 1 12 10 1 12 10 12 10 12 9 1 1 1 1 10 1 12 9 1 12 10 1 100 10 1 1 12 10 1 12 9 1 12 10 12 11 1 12 10 1 1 12 9 1 1 1 12 10 1 12 10 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 100 10 1 12 10 1 12 10 1 12 10 1 100 1 1 12 9 1 1 12 10 12 10 1 1 7 1 8 10 1 12 10 1 1 1 12 10 1 8 12 10 1 8 10 1 8 1 8 1 1 100 1 12 10 1 100 1 1 12 10 1 1 1 100 8 10 1 1 1 1 1 12 9 12 9 1 1 12 10 1 100 100 10 12 10 1 1 1 1 12 9 1 1 12 10 1 100 12 10 1 100 1 100 1 1 12 11 1 1 1 12 9 1 1 12 9 1 1 12 10 1 12 10 1 100 1 1 12 11 1 100 1 12 10 1 1 12 10 1 12 11 1 12 10 1 12 10 1 1 12 10 1 1 1 1 7 1 1 12 10 1 1 8 12 9 1 1 1 1 1 1 12 10 1 1 12 11 1 1 100 1 1 12 10 1 12 11 1 100 1 12 10 1 7 1 1 1 1 1 1 1 1 1
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
ciInstanceKlass java/lang/ThreadGroup 1 1 263 1 7 1 7 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 1 8 12 9 12 9 12 9 1 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 12 9 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 3 1 12 9 1 1 1 1 1 100 1 1 12 10 12 9 12 9 1 7 1 1 12 10 100 1 100 12 10 1 1 1 1 7 1 1 12 10 1 100 12 10 1 12 9 12 10 1 1 1 12 10 1 12 9 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 1 1 1 12 10 12 10 1 12 10 10 1 12 10 1 12 10 10 1 1 100 10 12 10 1 12 10 1 12 10 1 1 7 1 12 10 1 1 1 1 12 9 1 12 10 1 100 1 8 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 11 1 100 1 12 9 1 100 10 1 8 1 1 12 10 12 10 1 8 1 12 10 1 1 12 10 1 1 1 12 10 1 100 10 1 8 10 1 8 1 12 10 1 8 1 1 1 1 1
instanceKlass org/apache/felix/scr/impl/helper/ReadOnlyDictionary
instanceKlass org/osgi/framework/FrameworkUtil$MapAsDictionary
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$UnmodifiableDictionary
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle$SystemBundle$SystemBundleHeaders
instanceKlass org/eclipse/osgi/storage/BundleInfo$CachedManifest
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 28 1 100 1 1 7 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 436 1 7 1 1 7 1 7 1 100 1 100 1 7 1 7 1 100 1 100 1 7 1 100 1 1 7 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 12 10 12 9 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 7 1 1 12 10 1 8 1 12 10 12 9 12 9 4 1 7 1 1 12 10 12 9 1 4 12 10 1 1 1 1 12 11 1 1 12 10 1 12 10 1 12 9 1 1 1 1 1 1 1 12 10 1 1 1 1 1 100 10 100 1 1 12 9 1 7 1 12 10 1 1 12 9 1 12 10 1 1 12 10 3 1 12 9 1 12 9 1 1 1 1 1 1 1 12 10 1 12 10 1 1 1 12 10 1 1 12 11 1 7 1 1 12 11 1 7 1 12 11 1 12 11 1 12 11 1 12 11 12 10 1 1 1 1 12 10 12 10 12 9 12 9 12 9 1 1 100 10 1 100 1 12 10 10 1 8 10 1 12 10 1 8 10 1 100 1 8 1 1 7 1 12 10 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 12 10 1 10 1 1 10 1 1 12 10 1 100 12 11 12 11 10 1 1 10 1 1 1 1 100 1 12 10 1 100 1 1 12 11 1 100 10 1 1 1 1 100 1 12 11 1 1 1 1 1 1 1 1 1 100 12 11 1 1 1 1 12 10 1 1 1 1 1 1 100 1 12 10 1 100 1 12 10 1 12 10 1 100 1 12 10 1 1 1 1 1 100 1 12 10 1 1 12 10 8 1 12 10 1 100 1 8 10 4 12 10 4 1 12 10 1 8 12 10 1 100 1 1 12 10 1 100 1 100 1 1 12 11 1 1 12 10 12 10 1 1 12 10 1 10 1 1 1 1 1 1 1
instanceKlass org/eclipse/core/internal/resources/SaveManager$MasterTable
instanceKlass org/eclipse/osgi/util/NLS$MessagesProperties
instanceKlass org/eclipse/core/internal/preferences/SortedProperties
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 545 1 7 1 1 7 1 7 1 7 1 1 7 1 7 1 1 100 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 100 1 12 10 12 10 12 9 12 9 12 9 1 7 1 12 10 1 1 1 1 12 10 1 1 1 100 1 8 1 7 1 1 12 10 12 10 1 1 12 10 1 1 8 12 10 1 7 12 10 1 1 12 10 1 1 12 9 1 1 12 10 1 7 1 12 10 1 12 10 1 1 12 10 1 100 1 8 1 12 10 100 1 12 10 1 1 12 10 1 1 1 12 10 3 10 1 100 1 1 12 10 1 12 10 1 1 12 10 1 8 1 12 10 1 1 12 10 1 1 1 8 1 100 1 12 10 1 1 12 10 1 12 10 1 1 1 1 12 10 1 1 12 10 1 100 1 1 12 10 1 100 1 100 1 1 12 9 1 12 10 12 10 1 100 10 10 1 1 12 10 1 7 1 1 12 11 1 100 1 7 1 1 12 11 1 1 12 11 1 12 11 1 12 11 12 10 1 8 1 100 1 12 10 1 1 100 1 12 10 1 100 10 1 12 10 1 100 1 12 10 1 1 100 1 12 9 1 12 10 1 1 100 1 100 1 100 1 1 12 10 1 100 10 1 8 1 8 1 12 10 1 1 1 12 10 12 10 1 1 1 1 10 1 1 12 10 1 12 10 1 1 1 7 10 1 12 10 1 12 11 1 7 1 1 12 10 1 1 1 8 1 100 1 12 10 11 1 8 1 1 100 10 1 11 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 12 10 1 12 10 10 1 12 10 1 1 12 10 1 12 10 10 1 1 1 12 10 1 1 1 12 10 1 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 1 1 12 10 1 12 10 1 12 10 1 1 12 10 12 10 1 1 1 12 10 1 1 1 12 10 1 12 10 1 1 1 12 10 1 1 1 1 12 10 12 10 1 1 1 100 10 1 100 1 12 11 4 11 1 1 12 10 1 100 1 12 10 1 12 11 1 1 12 10 1 1 1 100 1 100 1 12 10 1 12 10 1 100 1 8 1 12 10 10 1 100 1 1 12 10 1 100 6 0 1 1 12 10 1 100 1 1 12 11 1 12 10 1 1 1 12 10 1 1 1 1 1 1 1 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
ciInstanceKlass java/lang/Module 1 1 859 1 7 1 7 1 100 1 7 1 7 1 100 1 100 1 100 1 7 1 7 1 1 1 7 1 1 7 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 1 12 10 12 9 12 9 12 9 1 1 12 10 1 12 10 1 7 1 7 1 7 1 1 12 10 1 7 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 7 1 1 12 11 1 1 12 10 1 7 1 1 12 10 1 7 1 100 1 1 12 10 1 1 1 1 1 1 7 1 1 12 10 1 100 1 1 12 9 1 100 1 1 12 10 1 1 1 1 12 10 1 8 1 1 12 10 1 12 10 12 9 1 12 9 1 1 1 1 12 10 12 9 1 12 11 1 12 9 1 7 1 1 12 10 1 1 1 1 7 1 1 12 10 1 1 12 10 1 100 1 100 10 1 1 12 10 1 8 1 12 10 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 7 1 1 12 9 1 1 12 10 1 1 1 1 12 10 1 12 9 1 12 10 1 12 10 12 9 1 7 1 12 11 1 1 12 10 12 9 1 1 12 9 1 12 10 1 12 10 1 1 1 1 1 100 1 8 10 1 1 12 10 1 12 10 1 8 1 1 1 1 1 1 12 10 1 1 1 8 1 8 1 1 12 10 1 12 10 1 1 12 10 16 1 1 12 10 15 16 1 7 1 1 12 10 15 1 1 12 18 1 1 12 10 1 12 11 1 12 9 12 11 1 1 1 100 1 12 10 1 100 1 8 10 1 7 1 1 12 11 1 12 10 1 12 10 1 12 10 1 1 1 12 11 1 7 1 12 11 1 1 12 11 12 9 1 12 11 1 1 1 1 12 10 1 1 1 12 10 1 12 9 1 12 10 1 7 12 10 1 1 1 7 1 12 10 10 1 100 16 1 1 12 10 15 16 1 1 12 18 1 1 12 11 16 1 100 10 15 1 16 1 12 18 1 1 12 11 1 100 1 1 12 10 1 1 12 11 1 1 1 1 7 1 12 10 4 1 7 1 12 11 1 7 1 7 10 1 7 1 1 12 10 1 7 1 100 1 100 10 12 11 1 8 10 1 1 12 10 1 7 12 10 1 12 10 1 12 10 12 10 10 1 1 12 11 12 10 1 1 12 10 12 9 1 100 10 1 1 12 10 1 100 11 1 1 12 10 1 12 11 10 1 12 10 11 1 1 12 10 1 1 12 10 1 1 12 10 1 7 1 12 10 1 12 10 1 12 10 10 1 12 10 1 12 10 1 1 12 10 15 1 16 1 12 18 1 12 11 1 1 12 10 15 1 16 1 12 18 1 12 10 1 12 10 12 10 1 12 10 1 12 10 1 12 10 12 9 1 10 10 10 1 12 10 1 1 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 9 16 1 12 10 15 16 1 1 12 18 1 100 1 1 12 10 1 100 1 100 1 8 1 1 12 10 1 12 10 1 100 1 12 10 1 1 12 10 1 1 7 1 100 1 8 1 100 10 3 1 12 10 1 100 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 100 1 100 1 100 12 10 1 100 1 8 1 12 10 1 1 12 10 1 7 1 12 10 1 1 12 10 12 10 12 10 1 1 12 10 1 100 10 1 1 12 10 1 100 1 1 12 10 1 8 1 1 12 10 1 100 1 12 10 1 8 1 12 10 1 8 1 12 10 12 10 12 10 1 8 10 1 12 10 1 7 10 1 1 12 10 1 12 10 1 12 9 1 12 9 1 12 9 1 12 9 12 10 1 12 11 1 12 10 1 1 1 1 1 1 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 356 1 7 1 7 1 7 1 100 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 12 10 1 100 1 1 12 9 1 100 1 12 10 1 1 1 12 10 1 7 1 1 12 10 1 100 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 9 1 1 1 100 1 1 12 10 1 12 11 1 100 1 12 10 1 1 1 1 1 7 1 100 1 12 10 1 1 12 10 1 7 1 12 10 1 1 12 10 1 7 1 1 12 10 1 100 1 7 10 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 8 1 100 10 1 1 12 10 1 8 1 12 10 1 12 10 1 8 1 8 1 8 1 8 1 8 1 8 1 100 1 12 10 1 12 10 1 1 12 9 1 1 12 10 1 1 12 10 1 10 1 1 1 1 1 11 1 100 1 100 1 8 10 1 12 10 1 1 12 10 1 8 1 12 10 1 8 1 1 12 10 1 10 1 1 1 1 100 1 8 10 1 1 12 11 1 1 1 1 1 1 12 10 1 12 10 1 12 10 1 12 9 1 12 10 1 1 7 1 12 10 1 1 1 1 100 1 1 12 10 10 1 12 10 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 12 9 1 100 1 12 10 1 8 1 100 1 1 12 10 1 8 1 1 12 10 12 9 1 1 1 100 10 1 1 7 10 1 7 1 1 12 10 10 1 7 1 1 12 10 12 9 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
ciInstanceKlass java/lang/reflect/Field 1 1 398 1 7 1 7 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 1 1 1 12 10 1 100 1 1 12 10 1 100 1 12 10 1 1 12 9 12 10 12 10 1 100 1 12 10 1 1 1 1 12 10 12 9 12 9 12 9 12 9 12 9 12 9 12 9 1 1 12 9 1 100 1 8 1 12 10 12 10 12 9 12 9 1 1 1 1 12 10 1 7 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 1 1 12 10 1 1 7 1 12 10 1 1 1 12 10 12 10 12 10 1 1 12 10 1 1 100 10 1 100 12 10 1 1 100 10 1 8 1 12 10 1 1 12 10 1 8 12 10 1 12 10 1 8 1 1 8 1 10 1 100 11 1 1 1 100 1 1 12 9 1 1 12 10 1 1 12 10 1 7 12 11 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 1 12 10 1 7 1 12 10 1 12 10 1 1 12 10 12 10 1 1 12 9 1 7 1 1 12 10 1 1 12 10 1 1 1 1 1 1 100 1 12 10 1 12 10 1 100 11 1 12 10 1 100 1 1 1 1 100 1 1 12 10 1 1 1 100 1 1 12 10 1 12 9 1 100 1 1 12 10 1 100 1 1 12 11 1 1 12 10 1 100 1 1 1 1 12 10 1 1 12 9 1 100 1 1 12 10 1 12 10 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Parameter 0 0 207 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 1 1 12 10 1 1 12 10 1 1 1 100 1 12 10 1 1 1 100 10 1 1 12 10 1 100 1 12 11 1 12 10 1 100 1 12 10 1 1 12 10 1 12 10 1 100 1 12 10 1 8 1 8 1 1 12 10 1 12 10 12 10 1 1 1 12 10 1 8 1 12 10 1 12 9 1 1 12 10 1 1 1 12 9 1 1 12 10 1 100 1 1 1 1 12 10 1 1 1 12 10 1 12 10 10 1 12 10 1 1 1 1 100 1 1 12 10 1 12 10 1 100 1 12 11 1 12 10 1 100 1 1 1 1 100 1 1 12 10 1 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 9 1 100 10 1 100 1 12 11 1 1 12 11 1 1 1
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 484 1 7 1 7 1 100 1 100 1 100 1 100 1 100 1 100 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 12 10 1 1 12 10 1 7 1 1 12 11 1 7 1 12 10 1 1 1 1 12 10 1 7 1 1 12 10 1 100 1 1 12 10 1 12 10 1 8 1 1 1 1 100 10 12 10 1 1 12 10 1 100 1 1 12 10 1 16 1 100 1 1 12 11 15 1 16 1 100 1 1 12 10 15 1 1 12 18 1 100 1 1 12 11 1 8 1 8 1 8 1 100 1 1 12 10 1 1 12 11 1 100 1 8 1 8 12 10 1 100 1 8 1 12 10 1 8 1 1 1 1 100 1 1 12 11 1 100 1 1 12 10 1 12 11 1 100 1 8 1 16 18 1 8 1 12 10 1 1 1 1 12 10 12 10 15 16 18 1 8 1 100 1 12 10 1 100 1 12 10 1 12 10 1 12 10 1 8 1 8 1 1 12 10 1 1 12 10 10 1 12 10 1 1 1 1 1 1 1 1 1 100 10 12 10 12 10 1 100 12 10 12 10 1 12 10 1 1 12 10 100 1 100 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 8 1 12 10 1 12 10 1 1 3 1 100 1 8 1 12 10 1 12 10 10 1 12 10 1 1 12 10 1 8 1 8 1 8 1 100 12 9 1 12 10 1 8 12 9 12 10 12 10 1 1 12 10 1 12 10 12 10 1 1 12 10 1 1 1 1 1 1 100 12 10 1 1 12 10 1 100 1 100 1 1 12 10 1 7 1 1 1 1 1 7 1 12 10 1 12 10 1 7 1 12 11 1 7 1 12 10 1 1 1 1 100 1 1 12 10 1 1 1 1 12 10 1 12 9 1 1 12 10 12 10 1 1 12 10 1 100 1 1 1 1 1 1 12 9 1 100 1 1 12 10 1 1 12 10 1 1 12 10 1 12 9 1 10 10 10 1 100 1 1 12 10 1 1 12 10 1 12 9 1 1 12 10 1 1 12 9 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Method 1 1 418 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 1 1 100 1 1 12 10 1 100 1 12 10 1 1 12 9 12 10 12 10 1 100 1 12 10 1 1 1 1 12 10 12 9 12 9 12 9 12 9 12 9 12 9 12 9 12 9 12 9 12 9 1 1 12 9 1 7 1 8 1 12 10 12 10 12 9 1 1 8 1 1 1 1 7 1 12 10 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 10 1 100 1 1 12 9 1 1 1 12 10 12 10 1 1 1 1 1 7 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 12 10 1 7 12 10 1 1 12 10 1 1 7 10 1 100 12 10 1 1 7 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 7 1 1 12 10 1 12 10 1 10 1 8 1 12 10 12 10 1 7 1 8 1 8 1 8 1 12 10 12 10 1 1 12 10 10 1 1 1 12 10 1 12 10 1 100 11 1 1 1 7 1 7 1 1 1 1 12 9 1 1 12 10 1 100 1 12 10 1 1 12 10 1 1 12 10 1 7 12 11 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 9 1 7 1 1 12 10 1 1 12 10 1 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 11 1 100 1 1 12 10 1 100 1 100 1 100 1 12 10 1 1 12 10 1 12 10 1 100 1 8 1 12 10 10 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 8 1 1 12 10 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 399 1 7 1 1 7 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 1 100 1 12 10 1 1 12 9 1 1 12 10 12 10 1 100 1 12 10 1 1 1 12 9 1 1 1 1 12 10 12 9 12 9 12 9 12 9 12 9 12 9 12 9 12 9 1 1 100 1 8 1 12 10 12 10 12 9 1 1 1 1 7 1 12 10 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 7 1 100 1 8 10 1 1 1 1 1 1 1 12 10 12 10 1 1 1 1 1 12 10 12 10 1 100 1 1 12 9 1 1 1 1 1 7 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 100 12 10 1 1 100 1 12 10 1 1 12 10 1 1 1 12 10 1 100 1 1 12 10 1 1 8 10 1 12 10 1 100 1 8 1 12 10 12 10 1 1 12 10 1 12 10 12 10 1 1 1 12 10 1 12 10 1 1 1 1 100 1 100 1 100 1 1 1 12 9 1 100 1 1 12 10 1 1 1 12 10 12 10 1 8 1 1 12 10 1 7 12 11 1 12 10 1 12 10 1 12 10 1 1 12 9 1 7 1 1 12 10 1 1 12 10 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 100 1 12 9 1 12 10 1 12 10 1 12 10 1 100 1 8 10 1 1 1 1 12 10 1 1 12 10 10 1 1 12 10 1 12 10 1 100 1 1 12 10 1 100 1 1 12 11 1 1 12 10 1 1 12 9 1 100 1 1 12 10 1 12 10 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 10 1 100 1 7 1 1 12 10 1
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor6
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor5
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor4
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor3
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor2
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor1
instanceKlass jdk/internal/reflect/DelegatingMethodAccessorImpl
instanceKlass jdk/internal/reflect/NativeMethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 19 1 100 1 7 1 100 1 1 12 10 1 1 1 100 1 100 1 1
instanceKlass jdk/internal/reflect/SerializationConstructorAccessorImpl
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor5
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor4
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor3
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor2
instanceKlass jdk/internal/reflect/BootstrapConstructorAccessorImpl
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor1
instanceKlass jdk/internal/reflect/DelegatingConstructorAccessorImpl
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 21 1 100 1 7 1 100 1 1 12 10 1 1 1 100 1 100 1 100 1 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 1 1 10 1 100 1 7 1 1 12 10 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 134 1 7 1 100 1 100 1 1 1 1 1 12 10 1 1 12 9 1 1 12 10 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 8 1 7 1 1 12 11 1 7 1 1 12 10 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 53 1 100 1 7 1 100 1 1 12 10 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeShortFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeObjectFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeIntegerFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeQualifiedFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 1 1 227 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 1 7 1 1 12 10 1 7 1 1 12 10 12 9 1 7 1 1 12 10 12 9 1 12 10 12 10 12 9 1 1 1 1 12 10 1 7 1 12 10 1 7 1 1 12 10 1 12 10 1 1 1 100 10 1 12 10 1 1 12 10 1 8 10 1 12 10 1 1 1 100 1 8 1 12 10 1 8 12 10 1 8 1 12 10 1 1 1 100 1 1 12 10 10 1 8 1 100 12 10 1 1 8 1 100 1 12 10 1 1 8 1 100 1 12 10 1 1 8 1 100 1 12 10 1 1 8 1 100 1 12 10 1 1 8 1 100 1 12 10 1 1 8 1 100 1 12 10 1 1 8 1 100 1 12 10 1 1 8 1 100 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 8 1 8 1 8 1 8 1 8 1 8 1 1 12 10 1 8 1 8 1 8 12 10 1 1 1 12 10 1 1 1
staticfield jdk/internal/reflect/UnsafeFieldAccessorImpl unsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass jdk/internal/reflect/UnsafeStaticObjectFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeQualifiedStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 1 1 39 1 7 1 7 1 1 1 1 12 10 1 1 12 9 1 7 1 1 12 10 12 9 1 1 8 1 7 1 1 12 11 1 7 1 1 12 10 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 1 1 15 1 100 1 100 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 1 1 113 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 1 1 1 100 1 100 1 100 1 100 12 9 1 7 1 1 12 10 1 7 1 1 12 10 1 7 1 1 12 10 12 9 12 9 12 9 1 7 1 1 12 10 1 7 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 12 9 1 7 1 1 12 10 1 1 12 10 1 1 1 1 1 1 12 10 8 1 1 12 10 1 1 1 1
staticfield jdk/internal/reflect/NativeConstructorAccessorImpl U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/reflect/NativeConstructorAccessorImpl GENERATED_OFFSET J 16
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 582 1 7 1 7 1 100 1 100 1 7 1 100 1 100 1 1 100 1 100 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 1 1 12 10 1 7 1 1 12 10 1 7 1 7 1 1 12 10 12 9 1 12 10 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 1 1 100 1 1 12 11 12 10 1 12 10 1 12 10 12 9 1 1 12 10 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 8 1 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 7 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 1 1 12 10 1 8 1 7 1 1 12 10 1 8 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 8 1 1 12 10 1 1 8 1 12 10 1 8 1 100 1 1 12 10 12 9 1 100 10 1 100 1 1 12 9 1 100 9 1 8 1 1 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 8 1 1 12 10 1 8 1 12 10 1 1 1 12 10 1 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 1 1 100 1 1 12 9 1 1 12 10 1 100 1 12 11 1 12 10 12 10 1 100 1 1 12 10 1 100 1 12 11 10 1 100 1 12 11 1 12 10 1 100 1 12 11 1 1 12 9 1 1 12 11 1 100 1 1 12 11 1 1 12 10 1 12 9 1 12 11 1 12 9 1 12 9 1 12 9 1 1 12 11 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 8 1 12 10 1 12 10 1 8 1 12 10 1 12 10 10 1 1 1 7 1 100 1 7 1 12 10 12 10 1 8 1 1 12 10 1 1 1 1 12 10 1 12 10 1 12 10 1 100 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 12 9 1 1 1 1 1 12 10 12 10 1 1 1 1 8 1 1 12 9 12 9 1 1 12 9 1 12 10 1 12 10 1 1 12 10 1 1 1 12 9 12 9 1 7 1 1 12 10 1 7 1 12 11 12 9 1 1 12 10 1 12 10 12 9 1 1 12 10 8 1 1 12 10 8 1 1 1 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 858 1 7 1 7 1 7 1 7 1 100 1 7 1 7 1 7 1 7 1 100 1 100 1 100 1 7 1 1 7 1 7 1 1 1 1 1 1 1 7 1 7 1 1 7 1 1 7 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 3 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 100 1 12 10 1 7 1 1 12 10 1 7 1 12 10 1 1 12 10 1 12 10 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 12 9 1 100 10 12 9 12 9 1 1 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 8 1 1 12 10 1 12 10 10 1 12 10 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 12 10 8 1 100 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 7 1 1 12 9 1 12 10 1 1 1 7 1 1 12 10 1 1 1 12 10 1 1 1 1 12 10 1 1 12 9 1 1 1 100 10 1 8 1 1 12 10 1 12 10 1 12 10 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 10 1 7 1 1 12 10 1 12 10 1 7 1 1 12 10 1 12 10 1 1 12 10 1 100 1 8 1 1 12 9 1 8 1 12 9 1 12 9 1 8 1 12 9 1 12 9 1 8 1 12 9 1 12 9 1 8 1 12 10 1 1 12 10 12 9 1 7 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 100 1 1 12 10 1 12 10 1 1 12 10 1 7 1 1 12 10 1 7 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 9 10 12 9 1 12 9 1 7 1 1 12 10 1 1 1 12 10 1 7 1 7 1 7 1 1 12 9 1 7 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 9 1 1 12 10 1 100 1 100 1 1 12 10 1 12 10 1 12 10 1 1 8 1 1 12 9 1 1 100 1 12 9 12 10 1 1 12 9 1 1 100 1 12 10 1 1 1 12 9 1 1 12 9 1 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 12 10 1 7 1 1 12 10 1 12 10 1 7 1 12 10 12 9 1 12 10 1 12 10 1 12 10 1 12 10 12 10 12 9 1 1 12 10 1 1 1 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 8 1 1 12 9 12 9 1 12 10 12 10 1 7 9 1 12 10 1 1 12 9 12 10 1 12 10 1 12 10 1 12 10 10 1 8 1 8 1 8 1 8 1 1 12 10 12 9 1 12 10 1 100 1 1 12 10 8 12 9 1 1 12 10 8 8 8 12 9 8 8 8 8 8 8 8 1 12 10 1 12 10 8 1 8 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
instanceKlass java/lang/invoke/VarHandleReferences$Array
instanceKlass java/lang/invoke/VarHandleInts$FieldStaticReadOnly
instanceKlass java/lang/invoke/VarHandleLongs$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleByteArrayAsLongs$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsInts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleReferences$FieldStaticReadOnly
ciInstanceKlass java/lang/invoke/VarHandle 1 1 368 1 7 1 7 1 100 1 100 1 100 1 100 1 7 1 7 1 100 1 7 1 7 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 12 9 12 9 1 1 1 100 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 1 12 10 1 7 1 12 10 1 1 12 10 1 100 1 1 12 10 1 1 1 12 9 1 1 12 10 1 100 1 1 12 10 1 1 12 10 1 1 12 9 1 12 10 1 1 12 9 1 12 10 1 12 10 1 1 1 1 1 12 9 1 1 12 9 1 12 10 1 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 8 12 10 1 12 10 1 1 12 10 1 1 12 9 1 12 10 1 1 12 10 1 12 10 1 1 10 1 100 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 100 1 1 12 10 1 1 1 1 100 1 12 10 12 9 10 1 1 12 9 1 12 10 1 12 10 1 1 12 10 1 12 10 1 100 1 1 12 10 1 1 12 9 1 1 12 10 12 9 1 12 10 1 12 10 1 100 1 12 10 1 100 10 1 1 7 1 1 12 9 12 9 1 7 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 10 1 7 1 1 12 10 12 9 8 1 1 12 10 1 7 1 1 12 10 1 1 1 1 1 1
staticfield java/lang/invoke/VarHandle AIOOBE_SUPPLIER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 693 1 7 1 7 1 100 1 100 1 7 1 100 1 7 1 1 100 1 100 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 8 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 12 9 1 1 1 7 12 10 1 1 12 9 1 1 12 10 1 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 7 1 1 12 10 1 12 10 1 100 1 12 9 1 12 10 1 100 1 100 12 10 1 8 1 1 12 10 1 12 10 1 12 10 1 12 10 12 9 1 8 1 100 1 1 12 10 1 7 10 1 1 12 10 1 100 1 100 1 1 12 10 12 9 1 100 1 8 1 12 10 1 100 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 8 1 8 1 8 1 1 1 1 100 1 1 12 10 1 12 10 1 1 12 9 1 1 12 10 1 12 10 1 12 10 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 8 1 1 12 10 1 12 10 1 8 1 100 9 8 1 100 9 1 1 12 10 1 1 1 12 10 1 12 10 1 8 1 1 12 10 1 12 10 12 10 1 1 12 10 1 1 12 10 1 100 1 1 12 10 1 12 10 1 8 1 12 10 1 12 10 1 8 1 1 1 12 10 1 1 1 12 10 1 1 1 7 1 1 12 10 12 10 1 8 1 8 1 1 12 10 1 100 1 1 12 10 1 7 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 10 1 12 10 1 12 10 1 12 10 1 3 12 10 1 1 12 10 1 1 1 1 3 1 1 1 12 10 1 7 1 1 12 10 1 1 1 12 10 1 1 1 12 9 1 1 12 10 1 1 3 1 1 12 10 10 1 7 1 1 12 10 1 12 10 1 7 10 10 12 10 12 10 1 12 10 10 12 10 12 10 12 10 12 10 1 100 10 10 12 10 1 100 10 10 12 10 1 1 1 12 10 10 1 1 12 10 1 1 1 100 10 1 1 12 10 1 100 10 12 10 1 12 10 1 12 10 1 1 1 10 1 12 10 1 1 12 10 1 1 1 12 10 1 12 10 1 12 10 1 100 12 10 1 1 12 10 1 1 100 1 8 10 1 7 1 12 10 12 10 1 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 8 1 8 1 8 1 8 1 12 10 1 1 1 12 10 1 1 12 10 1 8 1 8 1 12 10 1 8 10 1 12 10 1 12 10 1 8 1 8 10 1 12 10 1 1 1 8 1 1 12 10 1 8 1 12 10 1 1 12 10 1 12 10 1 100 1 8 1 8 1 8 1 8 1 100 10 1 1 8 1 8 1 8 1 8 1 12 10 1 100 1 100 1 100 10 1 100 10 1 100 1 1 12 10 1 1 1 1 12 9 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 10 1 100 1 100 1 1 12 10 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 606 1 7 1 7 1 100 1 100 1 1 7 1 7 1 1 7 1 1 7 1 1 7 1 7 1 1 7 1 100 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 12 10 1 100 10 1 1 12 10 12 10 1 1 1 1 12 10 1 1 1 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 7 1 1 1 1 1 100 1 100 1 100 12 10 1 7 1 1 12 10 1 100 1 1 12 10 1 7 10 1 1 12 10 1 8 1 12 10 1 8 1 1 12 10 1 8 1 1 12 10 1 100 1 1 12 9 1 8 1 100 1 1 12 10 1 100 12 10 1 100 1 8 1 1 1 7 10 1 12 10 1 7 1 7 1 12 9 1 1 12 10 1 12 10 1 1 7 1 1 12 10 1 7 1 1 12 10 1 7 1 1 12 10 1 12 10 1 100 1 1 12 10 1 1 12 10 1 12 9 1 8 1 12 10 1 8 1 12 10 1 8 1 8 1 8 1 12 10 1 8 1 1 1 1 12 10 1 12 10 1 1 100 1 1 12 10 1 8 1 100 1 1 12 10 1 7 1 8 1 12 10 1 8 1 8 1 8 1 8 1 8 1 1 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 12 10 1 100 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 8 1 8 10 1 1 1 12 10 1 1 12 10 1 8 1 100 1 1 12 10 1 8 1 8 1 100 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 9 1 1 12 10 10 1 12 10 1 12 9 1 1 12 10 1 12 9 1 12 9 1 1 12 10 1 12 10 1 100 1 1 12 10 1 7 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 8 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 7 1 1 12 10 1 1 12 10 1 100 1 8 10 1 1 1 1 1 12 9 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 100 1 100 10 10 1 100 1 100 10 1 100 10 1 1 12 10 1 1 100 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 1 12 10 12 10 1 12 10 10 1 8 1 100 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 12 9 1 7 1 1 12 10 12 9 1 7 12 11 1 1 12 10 12 10 10 12 10 1 7 1 1 12 10 1 1 1 1 1 1
staticfield java/lang/invoke/MethodHandleNatives JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 940 1 7 1 7 1 100 1 7 1 100 1 7 1 7 1 7 1 7 1 100 1 100 1 1 1 1 1 1 100 1 7 1 1 7 1 7 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 12 10 1 1 1 12 10 12 9 12 9 1 1 12 10 1 100 10 7 1 100 12 9 1 1 12 10 12 9 1 1 12 10 12 9 12 9 12 9 12 9 1 1 12 10 1 12 10 1 1 1 1 1 1 100 1 1 12 10 1 100 1 1 12 10 1 1 7 1 12 10 1 1 12 10 1 100 1 1 12 9 1 1 12 10 1 12 9 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 100 1 1 12 10 1 7 10 1 1 12 10 1 8 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 9 1 12 9 1 12 9 1 1 12 10 12 9 1 100 1 1 12 10 1 100 1 1 1 12 10 1 12 10 1 1 12 9 1 100 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 12 10 1 8 1 12 10 1 12 10 1 8 1 8 1 1 12 9 12 9 12 9 1 1 1 1 12 10 1 12 10 1 12 9 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 100 1 12 9 1 12 10 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 1 1 1 12 10 1 1 1 12 9 1 1 1 1 7 1 100 12 10 1 12 9 12 10 1 1 12 10 12 10 1 12 10 1 1 12 10 1 1 1 1 12 10 10 1 1 12 10 1 1 12 10 1 1 8 1 1 12 10 1 12 10 1 1 12 10 10 1 1 1 1 8 12 10 1 1 8 1 1 8 1 1 8 1 1 12 10 12 9 12 10 1 1 12 10 1 100 1 1 12 10 12 10 1 1 12 10 1 1 12 10 1 1 12 9 1 8 1 100 1 1 12 10 1 100 1 100 1 12 10 12 10 10 1 1 12 10 12 9 8 1 1 12 10 12 10 1 12 10 1 7 1 12 9 1 1 12 9 1 8 1 100 1 12 10 1 1 12 10 10 1 1 12 10 1 1 12 10 1 8 1 8 1 8 12 10 1 12 10 1 12 10 1 1 12 10 1 1 8 1 8 1 1 12 9 1 12 10 1 1 12 10 1 8 1 8 1 8 1 100 1 8 1 100 1 8 1 100 1 8 1 12 10 1 8 1 9 1 7 1 1 12 10 1 12 10 1 1 12 9 1 12 10 10 1 12 10 1 12 10 1 12 10 1 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 8 1 12 10 1 12 10 1 1 12 9 1 12 10 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 12 10 12 10 1 12 10 12 10 1 8 1 8 1 8 1 12 10 1 8 1 12 10 1 8 1 8 1 8 1 8 1 8 1 12 10 1 12 10 1 1 12 10 1 1 1 7 1 1 12 10 1 1 12 10 1 1 100 1 12 10 1 12 10 1 1 1 12 10 1 1 12 10 12 10 1 1 1 12 10 12 9 1 8 12 10 1 1 12 9 1 1 12 10 1 12 9 1 12 9 12 9 1 100 1 7 1 1 12 9 1 7 1 7 1 1 12 10 12 9 1 12 10 1 12 10 1 8 1 12 10 12 9 1 1 12 10 1 8 1 100 1 12 10 1 12 9 1 12 9 12 10 1 12 10 1 7 1 1 12 10 1 12 10 1 12 9 1 1 12 10 1 12 10 1 12 10 12 9 1 12 10 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 9 1 12 9 7 1 1 12 9 100 1 1 12 10 1 12 9 1 12 10 10 9 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 688 1 7 1 1 7 1 100 1 100 1 100 1 7 1 7 1 100 1 1 1 1 100 1 1 100 1 1 100 1 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 1 12 9 1 1 1 1 1 1 1 1 1 7 1 7 1 1 12 10 1 7 1 12 9 1 8 1 100 1 1 12 10 1 7 1 7 9 1 7 9 1 1 12 10 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 1 1 1 100 1 8 1 12 10 1 100 10 1 12 10 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 7 1 1 12 11 12 9 1 1 12 11 1 1 1 7 1 1 12 10 1 1 1 1 1 1 1 12 10 12 9 1 12 10 12 10 1 7 1 1 12 10 1 7 1 1 12 10 1 12 10 1 1 12 9 1 1 12 10 1 100 1 12 10 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1 12 10 12 10 1 12 10 1 1 1 1 12 10 12 10 1 1 1 1 12 10 1 1 1 1 8 1 8 1 1 12 10 1 1 1 12 9 1 100 10 1 1 12 10 12 10 1 12 10 1 12 10 1 12 10 12 10 1 1 12 10 1 8 1 1 1 12 10 1 7 1 1 12 10 12 10 1 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 1 12 10 1 12 10 1 12 9 1 12 10 10 1 1 1 12 10 1 12 10 12 9 1 1 12 10 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1 12 10 12 10 1 12 10 1 12 10 1 100 1 8 1 8 1 8 1 12 10 1 12 10 1 12 10 10 1 1 1 1 1 12 11 12 11 1 1 1 100 1 1 12 10 1 12 9 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 100 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 10 1 12 9 1 7 1 12 10 1 1 1 7 1 7 1 1 12 10 1 100 1 1 12 9 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 12 10 1 1 12 10 1 1 12 10 1 8 1 7 1 1 12 10 1 1 12 11 1 12 9 1 1 12 10 1 12 10 1 1 1 1 12 10 1 1 1 1 100 12 10 1 100 1 12 10 1 100 12 10 1 100 1 1 12 11 16 1 1 12 10 15 16 1 100 1 1 12 10 15 1 1 12 18 1 1 12 11 16 1 1 12 10 15 16 1 12 18 1 12 11 1 100 1 100 1 12 11 1 12 10 1 12 10 1 1 1 7 1 100 1 12 10 1 12 10 1 1 1 7 1 1 12 9 1 12 9 1 100 1 1 12 10 1 12 9 1 100 1 12 10 12 10 1 100 1 1 1 12 10 1 1 1 1 1 1 12 10 10 1 7 1 7 12 9 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MethodType internTable Ljava/lang/invoke/MethodType$ConcurrentWeakInternSet; java/lang/invoke/MethodType$ConcurrentWeakInternSet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 35 1 100 1 100 1 1 5 0 1 1 12 10 1 12 10 1 12 10 1 1 100 1 1 12 10 1 100 1 1 12 10 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 267 1 7 1 7 1 7 1 100 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 12 9 1 1 12 10 12 9 1 1 7 1 1 12 10 1 1 100 12 10 1 100 1 1 12 10 1 12 10 1 100 1 1 12 9 1 100 1 12 10 1 1 1 1 1 1 12 10 1 7 1 100 1 100 10 1 100 1 1 12 10 1 1 12 10 1 8 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 1 12 10 10 1 12 10 1 1 12 10 1 100 12 9 1 1 12 9 8 1 1 12 10 1 1 12 10 1 1 12 10 1 12 9 1 8 1 100 1 12 10 1 12 10 1 100 1 8 10 1 12 10 1 1 12 10 1 100 1 1 12 10 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 9 8 1 1 12 10 12 9 1 100 10 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 100 1 7 1 1 12 10 1 100 1 8 10 1 7 12 10 1 1 12 10 1 100 1 8 1 12 10 1 1 1 12 10 1 1 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/invoke/NativeEntryPoint 0 0 80 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 1 100 1 1 12 10 100 12 9 12 9 12 9 12 9 12 9 1 1 1 100 1 8 1 12 10 1 100 1 1 12 11 1 1 12 10 12 10 1 100 1 12 11 1 12 11 1 1 12 10 1 1 1 12 10 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 40 1 7 1 7 1 100 1 7 1 1 100 1 7 1 1 1 12 10 1 1 10 1 7 1 1 12 10 1 1 12 10 1 1 1 12 10 1 1 1
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 55 1 7 1 7 1 1 1 1 1 1 1 12 10 12 9 12 9 1 7 1 1 12 10 1 1 100 12 10 1 1 1 100 12 10 1 1 12 9 1 1 100 10 1 12 10 1 1 1 12 10 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 52 1 100 1 100 1 1 1 1 12 10 1 12 10 1 1 1 1 12 9 1 1 12 10 1 1 12 10 1 1 12 9 1 100 1 1 12 10 1 100 1 100 1 1 12 10 1 1 12 10 1 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 26 1 100 1 100 1 1 12 10 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 18 1 100 1 100 1 1 1 1 1 1 1 1 1 1 12 10 1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 485 1 7 1 7 1 100 1 7 1 100 1 7 1 1 100 1 7 1 1 100 1 100 1 100 1 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 1 12 10 12 9 12 9 1 1 7 1 1 12 9 12 9 1 1 12 10 1 1 1 12 10 3 3 1 12 10 100 1 1 12 10 1 11 1 100 1 100 10 1 8 1 12 10 1 12 10 1 1 12 10 12 10 1 12 10 1 12 10 1 1 12 9 1 1 12 10 1 12 10 10 1 12 10 1 12 10 1 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 7 1 1 12 10 1 100 1 8 10 1 1 12 10 1 1 1 100 12 10 1 1 12 10 10 1 1 1 1 12 10 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 1 100 10 1 12 10 1 1 100 1 12 10 1 1 1 1 12 10 1 12 10 1 12 10 10 1 1 1 1 12 10 12 10 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 10 1 1 12 10 1 12 10 1 1 12 10 1 8 1 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 1 1 7 1 12 10 1 12 10 1 12 10 1 1 7 1 12 10 1 12 10 1 12 10 1 1 100 1 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 12 10 1 1 12 10 12 10 1 1 1 1 1 1 12 10 1 1 1 1 12 10 10 1 7 1 1 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 12 10 1 12 10 1 12 10 12 10 1 1 12 10 1 1 1 16 1 1 12 10 15 16 1 100 1 1 12 10 15 1 1 12 18 1 100 1 1 12 10 1 1 12 10 15 18 1 1 1 1 1 12 10 1 12 10 1 1 12 10 12 11 1 12 10 1 12 10 1 100 10 12 10 10 1 8 1 8 1 8 10 10 1 1 100 1 1 1 12 10 10 10 1 1 1 1 1 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciInstanceKlass java/lang/StringBuffer 1 1 417 1 7 1 1 7 1 100 1 100 1 100 1 100 1 100 1 1 100 1 100 1 1 1 1 1 5 0 1 1 1 1 1 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 1 1 1 12 9 1 12 10 1 12 10 1 12 10 1 12 9 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 1 100 1 1 12 10 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 12 10 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 10 1 1 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 10 1 1 1 12 10 1 1 1 1 12 10 1 1 12 9 1 7 1 1 12 10 1 100 10 10 1 1 1 100 1 1 12 10 10 1 12 10 1 7 10 8 1 1 12 10 8 1 12 10 1 8 1 12 10 1 12 10 1 1 1 100 1 1 12 10 1 1 12 10 12 10 1 100 1 8 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 1 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 1 12 10 1 1 1 1 12 10 1 1 7 1 12 10 1 7 1 1 12 9 1 7 9 12 9 1 1 1 1 1 1 1
staticfield java/lang/StringBuffer serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/StringBuilder 1 1 371 1 7 1 1 7 1 100 1 100 1 100 1 1 5 0 1 1 1 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 1 1 7 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 12 10 1 1 1 12 10 1 1 1 1 12 10 1 1 12 9 1 1 12 9 1 7 1 1 12 10 1 100 10 1 1 1 100 1 100 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 100 10 1 12 10 1 1 1 100 1 100 1 12 10 1 12 10 1 12 10 1 100 1 8 10 1 1 12 10 1 1 12 10 1 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 1 1 12 10 1 1 12 10 1 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 10 1 12 10 1 1 1 1 12 10 1 1 1 1 1 1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 28 1 7 1 100 1 1 1 1 1 1 1 1 1 12 10 1 12 9 12 9 12 9 12 9 12 9 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1203 1 7 1 7 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 12 10 1 7 1 1 12 10 12 10 1 12 10 12 10 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 1 1 12 10 1 12 10 1 12 10 1 12 10 12 10 12 10 1 1 1 100 10 1 1 1 1 12 10 12 10 1 5 0 1 1 1 12 10 12 10 1 1 1 1 7 1 1 12 10 1 1 12 10 1 12 10 1 12 10 12 10 1 1 12 10 1 12 10 1 12 10 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 8 1 1 12 10 1 12 10 12 10 1 1 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 12 10 12 10 1 1 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 12 10 5 0 5 0 5 0 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 100 1 8 10 1 1 1 100 10 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 7 1 12 9 1 12 9 1 1 1 1 100 10 1 12 10 1 1 1 1 100 1 1 1 1 8 10 1 8 1 8 1 12 10 1 7 1 1 12 9 1 100 9 1 7 9 1 7 9 9 1 100 9 1 7 9 1 100 9 1 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1 5 0 5 0 1 1 12 9 1 12 10 12 10 1 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 1 8 3 1 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 12 10 1 12 10 12 10 1 1 1 1 1 12 10 1 12 10 1 12 10 1 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 1 100 10 1 1 100 10 1 1 1 12 9 1 5 0 1 1 12 10 5 0 1 12 10 5 0 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 12 10 1 12 10 10 1 1 12 10 1 12 10 1 1 1 12 10 12 10 12 10 5 0 5 0 5 0 1 1 12 10 1 12 10 1 12 10 12 10 1 1 1 100 1 12 10 1 8 1 100 1 1 12 11 1 8 1 1 12 11 1 100 1 12 10 1 1 1 1 1 3 1 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 1 12 10 1 1 12 10 10 1 7 12 10 12 9 1 7 12 9 1 7 12 9 1 7 12 9 1 7 12 9 1 7 12 9 1 7 12 9 1 7 12 9 1 7 12 9 12 10 12 9 12 9 12 9 12 9 12 9 12 9 12 9 12 9 12 9 1 12 9 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
ciInstanceKlass jdk/internal/module/Modules 1 1 441 1 7 1 100 1 100 1 100 1 1 100 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 9 1 7 12 11 1 1 12 11 1 1 12 11 1 1 12 11 1 12 11 1 12 11 1 12 11 1 12 11 1 1 1 12 11 1 1 1 1 100 1 1 12 10 1 100 1 1 12 10 1 16 1 1 12 10 15 16 1 100 1 1 12 10 15 1 1 12 18 1 100 1 1 12 10 1 100 1 100 1 12 10 1 7 1 100 1 100 1 100 1 1 12 10 1 100 1 12 10 1 12 10 1 12 11 1 1 1 12 9 1 7 1 12 11 1 1 1 12 10 10 1 12 10 10 1 1 12 9 1 12 10 1 1 12 10 1 100 1 12 10 1 100 1 100 1 1 12 11 1 100 1 1 12 10 1 100 1 12 11 1 1 12 10 1 100 1 12 10 1 100 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 11 16 1 1 12 10 15 1 16 1 1 12 18 1 100 1 12 11 1 100 1 1 12 10 1 100 1 1 12 11 1 100 1 100 1 1 12 11 1 100 1 1 12 11 1 12 11 1 1 12 10 1 12 10 1 16 1 12 10 15 1 16 1 1 12 18 1 1 12 11 1 16 1 1 12 10 15 1 16 1 1 12 18 1 1 12 11 1 12 10 10 15 1 16 18 1 1 12 10 15 1 16 18 1 1 12 10 12 9 1 100 1 1 12 11 1 100 10 1 12 11 1 1 12 11 1 1 12 11 10 1 100 1 1 12 10 1 100 1 12 10 1 12 10 12 11 1 12 10 1 100 10 1 1 12 10 15 16 1 12 18 1 1 12 10 1 1 1 100 1 8 1 12 10 1 12 10 1 1 12 10 15 16 1 12 18 11 12 11 1 12 10 10 10 1 1 12 10 15 1 12 18 10 10 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 1 1 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
instanceKlass java/io/ObjectInputStream$PeekInputStream
instanceKlass java/io/ObjectInputStream$BlockDataInputStream
instanceKlass org/eclipse/core/internal/resources/ContentDescriptionManager$LazyFileInputStream
instanceKlass java/io/ObjectInputStream
instanceKlass org/eclipse/core/internal/localstore/SafeChunkyInputStream
instanceKlass org/eclipse/core/internal/registry/BufferedRandomInputStream
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager$RewindableInputStream
instanceKlass org/eclipse/osgi/storage/url/reference/ReferenceInputStream
instanceKlass java/util/jar/JarVerifier$VerifierStream
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 150 1 7 1 7 1 100 1 100 1 1 3 1 3 1 3 1 1 12 10 1 1 10 1 1 1 100 1 1 12 10 1 7 1 1 12 10 12 10 1 1 3 1 1 12 10 1 100 1 8 1 12 10 1 7 1 7 1 7 1 1 12 10 1 100 1 8 10 1 7 1 1 12 10 1 7 10 1 1 12 11 1 1 12 10 1 1 12 11 1 7 1 1 12 11 1 1 12 11 1 7 1 1 12 10 1 1 5 0 1 12 10 1 1 12 10 1 100 10 1 8 10 1 1 1 1 1 1 8 1 1 1 1 8 1 1 12 10 1 100 1 1 12 10 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 77 1 7 1 7 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 1 1 100 1 1 12 10 1 1 1 1 7 1 1 12 10 1 7 1 1 12 10 1 1 1 100 1 1 12 10 1 12 10 1 1 1 100 1 100 1 12 10 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/net/URL 1 1 672 1 7 1 7 1 100 1 7 1 7 1 7 1 7 1 100 1 100 1 1 100 1 100 1 1 1 1 1 8 1 1 5 0 1 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 12 10 1 12 10 1 12 10 12 9 12 9 1 7 1 1 12 10 1 1 12 10 1 7 1 7 1 1 12 10 12 9 1 1 12 10 1 8 1 1 12 10 1 7 10 1 1 12 10 1 8 1 1 12 10 12 9 1 8 1 12 10 1 12 10 1 8 12 9 1 1 12 10 12 9 1 12 10 1 12 10 12 9 12 9 1 8 12 9 1 1 12 10 1 8 12 9 1 1 12 10 1 7 1 1 12 10 1 8 1 12 10 1 7 1 12 10 1 12 10 1 12 10 1 100 1 1 12 10 1 1 12 10 1 8 1 1 12 10 1 12 10 1 8 12 9 1 8 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 1 7 1 1 12 10 1 100 1 8 10 1 12 10 1 8 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 8 1 12 10 10 1 7 1 1 12 10 1 12 10 1 100 1 1 12 9 1 100 1 1 12 10 1 1 12 9 1 100 1 1 1 100 1 100 1 12 10 1 100 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 12 10 1 12 10 1 1 12 10 1 12 10 12 10 1 1 1 100 10 10 1 12 10 1 12 10 1 1 1 100 1 12 10 1 1 8 1 1 12 9 1 100 1 1 12 10 1 1 12 10 1 1 12 9 1 1 12 10 1 100 1 12 10 1 12 10 10 1 1 12 10 1 12 10 12 10 1 12 10 1 1 12 10 1 7 1 12 10 1 1 12 10 1 1 12 10 1 1 12 9 12 9 1 100 1 8 10 1 12 10 12 9 1 7 1 12 10 1 7 1 1 100 1 7 1 12 10 1 8 1 1 12 10 1 100 1 12 10 1 8 1 8 1 7 1 1 12 10 1 100 1 1 12 10 1 12 10 1 12 10 1 1 1 10 1 12 9 1 7 1 12 10 1 8 1 12 10 10 1 7 1 1 12 10 8 1 7 1 1 12 9 1 12 10 1 1 12 10 1 12 10 12 10 1 7 1 12 10 1 12 11 12 10 12 10 12 9 1 1 12 10 1 1 1 100 1 12 10 1 1 1 1 12 10 8 1 12 10 10 8 8 1 12 10 8 8 8 1 100 1 12 10 12 9 1 1 100 12 10 1 1 12 10 1 12 10 12 10 1 1 12 10 1 1 12 10 10 10 12 10 12 10 12 10 1 12 10 1 100 1 12 10 10 1 12 10 1 8 10 10 1 1 12 10 1 12 10 1 1 12 10 1 10 10 10 1 7 1 12 10 1 7 1 1 12 9 12 9 10 1 7 1 1 12 10 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/util/jar/Manifest 1 1 290 1 7 1 7 1 100 1 7 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 12 10 1 7 10 12 9 1 7 10 12 9 12 9 1 1 100 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 11 1 1 1 1 1 12 11 1 12 10 1 100 1 1 12 10 1 100 1 100 10 1 8 1 1 12 10 1 1 12 10 1 12 10 1 12 10 11 1 1 1 100 12 10 1 1 12 10 1 1 12 11 1 12 10 1 1 12 11 1 100 1 1 12 11 1 100 1 12 11 1 1 12 11 1 12 10 1 8 1 12 11 1 7 1 1 12 10 1 12 11 12 10 1 12 10 1 1 1 1 1 1 100 1 1 12 10 1 8 1 1 12 10 10 1 7 1 1 12 9 1 1 12 10 1 100 12 10 1 100 1 12 10 1 12 10 1 1 1 100 1 1 12 9 1 8 1 12 10 1 8 1 8 12 10 1 12 10 1 100 1 1 12 10 1 8 12 10 1 8 10 1 1 12 10 1 8 1 1 12 10 1 7 1 1 12 10 1 12 10 10 1 1 12 11 1 12 10 1 7 1 1 12 10 1 1 12 10 1 1 12 10 11 1 12 10 11 1 12 10 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 654 1 7 1 7 1 100 1 7 1 7 1 100 1 100 1 7 1 7 1 1 7 1 100 1 1 7 1 7 1 1 100 1 7 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 1 1 7 1 1 12 10 1 7 1 7 1 7 1 12 10 12 9 12 9 1 7 1 12 10 12 9 1 12 10 12 9 1 1 1 12 10 1 1 1 1 1 1 1 7 1 1 12 10 1 1 12 10 1 7 1 1 12 11 1 100 1 100 10 1 1 12 10 1 8 1 12 10 12 10 1 12 10 1 1 12 10 1 7 1 1 12 11 1 7 1 12 11 1 1 12 11 1 8 1 8 10 12 9 1 7 1 12 10 1 1 1 1 12 11 1 1 1 100 1 12 10 1 100 1 1 12 10 1 1 12 10 1 1 1 7 1 1 12 10 12 10 1 1 12 10 1 7 1 1 12 10 1 7 1 1 12 11 1 7 1 12 10 1 7 1 1 12 10 1 12 10 1 8 1 1 12 10 10 1 8 1 1 12 10 1 1 12 10 1 1 12 10 1 7 1 12 11 1 12 11 1 1 1 1 7 10 1 1 12 11 11 1 12 10 1 12 10 1 1 100 1 7 12 10 1 12 10 1 12 10 1 7 1 1 12 10 1 1 12 10 1 100 1 100 1 12 11 1 7 1 12 10 1 1 12 10 1 100 1 12 10 1 12 10 16 1 12 10 15 16 1 100 1 1 12 10 15 1 1 12 18 1 12 10 1 12 10 1 12 10 15 1 16 18 1 100 1 7 1 12 10 1 1 1 1 7 10 1 1 12 10 1 1 12 10 1 7 1 12 10 1 1 1 12 10 12 9 1 100 10 1 1 1 1 12 10 1 1 12 10 1 12 10 1 100 12 10 1 1 12 10 1 100 1 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 15 1 16 1 12 18 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 7 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 7 1 12 11 1 12 10 1 7 1 100 1 12 10 1 12 10 1 1 12 11 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 7 1 12 10 1 8 1 1 12 10 1 12 10 1 12 10 1 100 1 8 1 8 10 1 12 10 1 8 1 8 1 7 1 1 12 10 1 7 1 1 12 11 1 1 12 9 1 1 12 10 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 9 1 7 1 1 12 10 1 8 1 12 10 1 1 1 7 12 10 1 12 10 10 1 1 12 11 12 10 1 12 10 1 12 10 1 12 10 1 12 10 10 1 1 1 12 10 1 12 10 1 8 1 7 1 12 10 12 10 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 162 1 7 1 100 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 7 1 1 12 10 1 1 12 10 1 100 1 1 12 10 1 1 12 9 1 1 12 9 1 12 9 1 1 1 1 100 1 100 1 7 1 100 1 1 12 11 1 100 1 1 12 11 1 1 12 11 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 7 1 1 12 10 12 9 12 10 12 10 1 12 10 1 8 1 7 1 1 12 10 1 1 12 10 1 7 1 12 10 1 12 10 1 12 10 1 8 1 7 1 12 10 1 8 1 8 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 91 1 7 1 7 1 100 1 1 1 1 8 1 12 10 1 1 1 1 100 1 7 1 1 12 10 1 100 1 1 12 10 1 1 12 10 1 100 1 1 12 10 12 10 1 1 12 10 1 7 1 8 12 10 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 7 1 1 12 10 1 100 12 10 1 1 1 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 34 1 100 1 7 1 100 1 1 100 1 1 1 1 8 1 12 10 1 1 1 7 1 1 12 10 1 100 12 10 1 1 1 1
ciInstanceKlass java/security/CodeSource 1 1 337 1 7 1 7 1 100 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 1 7 1 1 12 10 12 9 1 100 7 1 1 12 10 1 7 10 1 1 12 10 1 1 1 100 1 12 10 1 1 12 10 1 1 1 1 1 1 1 7 10 1 7 1 1 12 10 1 7 1 12 10 1 1 12 10 1 12 10 1 7 1 1 12 10 1 1 1 1 12 10 1 1 1 12 10 12 10 10 10 1 12 10 1 100 1 1 12 10 1 12 10 1 12 10 1 12 10 1 8 1 12 10 1 12 10 1 1 12 10 1 12 10 1 8 1 1 12 10 10 1 100 10 1 1 12 10 1 8 1 12 10 1 12 10 1 12 10 1 8 1 8 12 9 1 100 1 8 1 12 10 1 12 10 1 8 1 12 10 1 8 1 8 1 8 1 1 1 100 1 100 1 100 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 12 10 1 12 10 1 1 1 100 1 100 1 100 1 12 10 1 12 10 1 100 12 10 1 100 10 1 8 1 12 10 1 12 10 1 1 12 10 1 100 1 1 12 10 1 8 1 8 10 1 1 12 10 1 100 1 1 12 10 1 100 12 10 1 1 12 10 1 12 11 1 100 10 1 12 10 11 12 10 1 8 1 100 1 12 10 1 1 12 10 1 12 10 1 1 12 11 11 1 1 1 1
instanceKlass com/google/common/cache/LocalCache
instanceKlass org/apache/felix/resolver/util/ArrayMap
instanceKlass java/util/concurrent/ConcurrentSkipListMap
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl$DictionaryMap
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl$ServiceReferenceMap
instanceKlass org/eclipse/osgi/internal/serviceregistry/ShrinkableValueCollectionMap
instanceKlass java/util/Collections$SingletonMap
instanceKlass java/util/TreeMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/EnumMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/HashMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 151 1 7 1 1 7 1 7 1 100 1 100 1 7 1 100 1 7 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 7 12 11 1 1 10 1 1 1 1 12 11 1 7 1 12 11 1 1 12 11 1 12 11 1 12 10 1 1 12 11 1 1 1 1 1 1 1 100 10 1 12 11 1 1 1 11 12 10 1 12 11 1 12 9 1 12 10 1 1 12 9 10 1 100 1 1 100 1 100 11 12 11 12 11 1 12 11 1 1 1 8 1 7 10 1 1 12 10 1 8 1 12 10 12 10 1 1 7 12 10 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1022 1 7 1 1 7 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 7 1 7 1 7 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 7 1 7 1 7 1 100 1 7 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 1 1 3 1 3 1 3 1 1 1 4 1 3 1 3 1 3 1 1 1 3 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 12 10 1 1 1 1 100 1 7 1 1 12 10 1 100 1 100 1 1 12 10 1 100 1 100 1 1 12 11 1 12 11 1 1 1 1 1 12 11 1 1 1 12 9 12 9 12 9 1 7 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 9 1 12 10 1 1 100 10 5 0 12 10 1 1 1 1 12 10 5 0 1 1 1 1 1 1 12 10 12 10 12 9 12 10 1 12 9 1 1 12 9 1 1 12 10 7 1 12 9 1 1 12 10 1 1 12 9 1 12 10 1 1 100 10 1 12 10 1 1 12 10 1 1 1 1 1 12 10 1 1 1 12 10 1 12 10 12 10 1 1 12 10 1 1 12 10 1 100 1 8 1 12 10 1 100 1 1 12 10 1 1 12 10 12 11 1 12 10 1 12 11 1 7 1 1 12 11 1 7 1 12 11 1 12 11 1 12 11 1 12 11 1 1 1 12 10 1 12 10 1 1 12 9 1 1 12 10 9 1 1 12 10 1 12 9 1 1 12 10 5 0 1 1 1 12 9 1 12 10 1 1 12 9 1 12 10 1 12 9 10 1 1 1 100 10 1 1 12 10 1 8 1 12 10 12 10 11 1 1 1 100 1 7 1 12 10 1 1 12 10 1 8 1 12 10 1 8 1 12 10 1 8 1 12 10 1 12 10 1 1 1 100 1 100 1 12 10 12 10 1 12 10 1 12 10 1 12 9 9 1 12 10 12 9 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 11 1 1 1 1 100 1 12 11 1 1 1 12 10 1 100 1 12 11 1 1 1 1 1 1 7 10 12 11 1 1 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 10 1 1 1 10 1 7 1 1 12 9 1 1 12 10 1 1 1 3 1 1 100 1 12 10 12 9 1 1 12 10 12 9 12 9 1 1 12 10 100 1 100 1 12 10 12 9 1 12 9 1 1 12 10 12 10 12 9 12 9 1 1 12 10 1 9 3 1 12 9 1 12 10 12 9 1 12 10 12 9 1 12 10 12 9 1 100 1 1 12 10 1 12 10 1 1 1 1 5 0 1 100 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 1 12 10 10 1 1 1 1 100 10 1 12 10 10 1 1 1 1 12 10 10 1 1 1 1 12 10 10 1 100 1 1 12 10 1 1 1 1 12 10 10 1 100 1 12 10 1 1 1 1 12 10 10 1 12 10 1 1 1 1 12 10 10 1 1 1 12 10 10 1 1 1 1 12 10 10 1 1 1 12 10 10 1 1 1 12 10 10 1 1 1 1 12 10 10 1 1 1 1 12 10 10 1 1 1 1 12 10 10 1 1 10 10 1 10 10 1 1 10 10 1 1 1 12 10 10 1 1 12 10 10 1 1 1 12 10 10 1 1 1 12 10 10 1 1 1 12 10 10 1 1 10 10 1 10 10 1 1 10 10 1 1 1 1 12 10 10 1 1 12 10 10 1 1 1 12 10 10 1 1 1 12 10 10 1 1 1 12 10 10 12 10 1 1 7 1 1 12 10 1 12 10 1 7 1 12 10 1 1 12 9 12 9 1 1 12 10 8 1 1 12 10 8 8 8 8 1 1 12 10 1 12 10 1 100 1 8 10 1 7 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 12
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
ciInstanceKlass java/util/Collection 1 1 96 1 7 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 12 11 1 7 12 11 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 12 11 1 100 1 12 11 1 1 12 11 1 100 1 12 11 1 12 11 1 1 1 1 1 1 1 1 100 1 12 10 1 1 1 12 11 1 7 1 12 10 1 1 1 1
ciInstanceKlass java/util/List 1 1 177 1 100 1 1 7 1 100 1 100 1 100 1 1 100 1 7 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 1 1 12 11 1 100 1 12 11 1 1 12 11 1 100 1 12 11 1 1 12 11 1 1 1 12 11 1 100 1 12 10 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 1 100 1 12 10 1 1 1 1 1 12 9 1 1 12 10 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 1
ciInstanceKlass java/util/RandomAccess 1 0 5 1 100 1 100
instanceKlass org/apache/felix/resolver/util/ArrayMap$1
instanceKlass org/apache/felix/resolver/util/OpenHashMap$AbstractObjectCollection
instanceKlass com/sun/jna/Structure$StructureSet
instanceKlass java/util/AbstractMap$2
instanceKlass java/util/TreeMap$Values
instanceKlass java/util/IdentityHashMap$Values
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder
instanceKlass java/util/AbstractQueue
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass java/util/HashMap$Values
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 130 1 7 1 1 7 1 7 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 12 10 1 7 1 12 11 1 1 12 11 1 12 10 1 1 1 7 1 100 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 7 1 12 10 1 7 1 1 12 10 1 100 1 1 12 10 1 1 100 1 1 12 10 1 1 1 100 10 1 12 11 1 1 1 11 12 10 1 1 12 10 1 1 100 1 1 12 10 11 1 1 1 1 1 8 1 7 10 1 1 12 10 1 8 1 12 10 12 10 1 1 1
instanceKlass org/eclipse/osgi/internal/container/InternalUtils$CopyOnFirstWriteList
instanceKlass org/eclipse/osgi/internal/weaving/DynamicImportList
instanceKlass java/util/AbstractSequentialList
instanceKlass java/util/Collections$SingletonList
instanceKlass java/util/Vector
instanceKlass sun/security/jca/ProviderList$ServiceList
instanceKlass sun/security/jca/ProviderList$3
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/ArrayList$SubList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 187 1 7 1 1 7 1 100 1 100 1 100 1 100 1 100 1 100 1 7 1 1 1 1 1 1 1 1 1 12 10 12 9 1 1 1 1 1 12 10 1 12 10 1 1 1 1 1 1 1 100 10 1 1 1 1 1 1 12 10 1 100 1 1 12 11 1 1 12 11 1 12 11 1 100 1 12 10 1 1 12 10 1 12 11 1 12 11 1 12 11 1 1 1 12 10 1 1 1 1 1 12 10 1 100 1 1 12 11 1 100 11 11 1 1 12 10 1 1 1 12 10 1 1 1 1 1 12 10 1 100 1 12 10 10 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 8 1 100 1 8 1 8 1 8 10 11 1 10 12 10 12 11 1 1 12 10 1 8 1 8 1 1 1 1 1
ciInstanceKlass java/util/ArrayList 1 1 415 1 7 1 1 7 1 100 1 100 1 100 1 100 1 7 1 7 1 100 1 100 1 7 1 7 1 1 1 1 1 1 5 0 1 1 3 1 1 1 1 1 1 1 1 12 10 1 7 12 9 12 9 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 1 12 10 12 9 1 1 1 7 1 1 12 11 12 9 1 1 12 10 7 1 7 1 1 12 10 1 1 12 9 1 12 10 1 1 1 12 10 1 7 1 1 12 10 1 7 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 1 100 12 10 1 100 1 12 10 1 1 1 7 1 1 12 10 1 1 1 1 1 1 1 7 1 12 10 12 10 1 1 1 1 1 1 12 10 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 100 10 1 1 12 11 1 100 1 12 11 1 12 11 1 12 10 1 1 1 12 10 12 10 1 1 1 1 1 1 1 1 1 100 1 1 12 10 10 1 1 12 10 1 12 10 1 8 1 8 1 8 1 8 1 1 1 1 12 10 1 1 1 100 1 1 12 10 12 11 1 1 1 100 1 100 1 12 10 1 12 10 1 12 10 1 1 1 100 1 100 1 12 10 1 12 10 1 100 1 1 12 10 1 100 1 1 12 11 12 10 1 100 1 8 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 1 12 10 1 12 10 1 1 1 1 7 12 10 1 12 11 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 12 10 1 1 7 1 12 11 12 10 1 100 12 10 12 10 1 1 1 1 1 12 10 1 1 100 1 12 11 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/lang/StackTraceElement 1 1 198 1 7 1 7 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 12 10 1 12 10 12 9 12 9 12 9 12 9 1 8 1 100 1 1 12 10 1 100 12 9 1 8 12 9 12 9 12 9 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 12 10 1 12 10 1 100 10 1 1 12 10 1 8 12 10 1 12 10 1 8 1 8 1 8 12 10 1 8 1 8 1 12 10 1 8 1 8 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 9 1 7 1 1 12 10 1 1 12 10 1 7 1 100 1 7 1 1 12 10 1 100 1 7 1 12 10 1 7 1 1 12 10 1 12 10 1 12 10 1 1 1 100 10 1 1 12 10 12 10 1 1 1 12 10 1 1 1 1 1 1
instanceKlass java/nio/DoubleBuffer
instanceKlass java/nio/FloatBuffer
instanceKlass java/nio/ShortBuffer
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 202 1 7 1 7 1 7 1 100 1 7 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 12 9 1 1 1 12 10 1 100 1 12 10 12 10 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 8 1 8 1 1 12 10 1 12 10 1 1 1 8 12 9 1 100 1 8 1 12 10 1 8 1 8 1 12 9 1 12 10 1 8 1 100 1 8 1 8 1 12 10 1 8 1 8 1 8 1 1 1 100 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 10 1 1 1 100 10 1 1 1 100 10 1 1 1 1 1 1 12 10 1 10 1 12 11 1 1 7 1 12 10 1 7 1 1 12 10 12 9 1 1 12 10 12 9 10 1 7 1 1 12 10 1 1 1 1 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer $assertionsDisabled Z 1
ciInstanceKlass java/lang/StackWalker 1 1 217 1 7 1 7 1 7 1 7 1 100 1 1 1 7 1 7 1 1 100 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 1 7 1 1 12 10 1 7 1 1 12 10 1 12 10 1 1 7 1 1 12 11 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 100 1 8 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 12 9 12 9 12 9 1 1 12 9 1 1 12 10 12 9 1 1 7 1 1 12 10 1 1 12 11 1 100 1 8 10 1 100 1 12 10 1 12 9 1 1 12 10 1 1 1 1 1 1 12 10 1 12 10 1 1 1 16 1 1 12 10 15 1 16 1 100 1 1 12 10 15 1 1 12 18 1 1 1 1 100 1 8 10 1 1 12 10 1 12 10 1 1 1 1 1 1 12 9 1 100 12 11 1 1 1 12 10 1 1 1 1 1 1 1
staticfield java/lang/StackWalker DEFAULT_EMPTY_OPTION Ljava/util/EnumSet; java/util/RegularEnumSet
staticfield java/lang/StackWalker DEFAULT_WALKER Ljava/lang/StackWalker; java/lang/StackWalker
instanceKlass java/lang/StackStreamFactory$StackFrameTraverser
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 1 286 1 7 1 1 7 1 7 1 1 7 1 7 1 1 7 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 12 10 1 12 10 1 7 1 1 12 10 12 9 1 1 12 10 12 9 12 9 12 9 12 9 1 1 12 9 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 12 9 1 12 10 12 10 1 1 12 9 1 100 1 1 12 9 1 100 10 1 8 1 1 12 10 1 12 10 1 8 1 1 12 10 1 100 1 1 12 10 1 1 1 100 1 8 1 12 10 1 8 12 10 1 1 12 9 1 12 10 12 9 1 8 5 0 1 8 1 8 1 1 1 1 12 9 12 10 1 12 10 12 10 1 100 1 1 12 9 1 1 1 1 12 10 1 12 10 1 12 10 1 12 10 12 10 1 1 12 10 1 8 1 12 10 1 12 10 1 100 1 12 10 1 1 1 1 12 10 1 8 1 100 1 1 12 10 1 1 12 10 1 1 12 10 12 10 12 10 1 7 1 1 12 10 1 12 9 1 8 1 12 10 1 12 10 1 1 12 10 12 10 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 8 1 1 1 1 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 1 1 123 1 7 1 7 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 9 12 9 1 7 1 1 12 11 12 9 1 1 1 1 1 12 11 1 1 12 10 1 7 1 12 10 1 12 10 1 1 12 11 1 1 1 12 11 1 1 12 11 1 1 1 1 12 10 12 9 1 1 1 12 10 1 100 12 10 1 12 10 1 1 12 11 1 12 10 12 9 1 1 12 10 1 100 1 100 1 8 1 12 10 1 1 7 1 1 12 10 1 1 1 1
staticfield java/lang/StackFrameInfo JLIA Ljdk/internal/access/JavaLangInvokeAccess; java/lang/invoke/MethodHandleImpl$1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 85 1 100 1 100 1 100 1 100 1 100 1 1 1 100 1 1 1 1 1 3 1 3 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 12 9 1 1 1 1 1 1 1 100 12 10 1 12 10 1 8 1 1 12 10 1 8 1 8 1 8 10 1 1 1 12 10 1 1 12 10 1 1 1 100 1 1 1 1 1
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 25 1 7 1 7 1 100 1 1 5 0 1 1 1 1 12 10 1 1 12 9 1 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 136 1 7 1 1 7 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 3 1 12 10 12 9 1 1 1 12 10 12 10 1 8 1 7 1 12 10 1 1 1 1 1 12 9 12 9 1 1 1 1 8 1 1 1 1 12 10 1 1 12 10 1 1 100 1 100 1 7 1 1 12 10 1 100 1 1 1 1 12 10 1 1 1 1 1 1 1 1 100 1 12 9 12 9 1 100 1 100 1 1 12 10 1 12 10 1 1 8 1 7 1 1 12 10 12 9 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Character 1 1 531 1 7 1 1 7 1 100 1 100 1 100 1 1 7 1 100 1 100 1 100 1 1 1 1 1 1 3 1 3 1 1 3 1 3 1 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 3 1 3 1 3 1 3 1 1 1 3 1 1 3 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 1 12 9 1 8 1 1 12 9 1 100 12 9 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 1 12 9 12 10 1 1 1 1 1 12 10 1 1 12 10 1 1 1 100 1 12 10 1 1 12 10 1 1 1 1 3 1 1 1 3 1 1 1 12 10 12 10 1 1 1 1 3 1 1 1 7 1 1 12 11 1 12 11 12 10 1 1 1 12 10 1 100 10 1 1 12 10 1 3 1 1 1 12 10 12 10 1 1 12 10 1 100 1 8 1 1 12 10 1 12 10 1 12 10 12 10 1 1 1 12 10 1 1 1 12 10 1 12 10 1 7 1 12 10 10 1 12 10 10 1 12 10 1 12 10 1 12 10 10 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 12 10 10 12 10 10 1 12 10 10 1 12 10 10 1 12 10 10 1 1 12 10 10 1 12 10 10 1 12 10 10 1 1 1 12 10 10 1 12 10 10 1 5 0 1 12 10 1 12 10 10 1 12 10 10 1 1 1 1 1 12 10 10 1 12 10 10 1 1 1 12 10 1 12 9 1 100 10 12 10 1 12 10 1 3 1 1 100 1 1 12 10 12 10 1 12 10 1 100 10 12 10 1 1 12 10 1 1 12 10 1 8 1 12 10 1 100 1 1 12 9 1 12 10 10 1 1 1 100 1 12 10 1 12 10 1 12 10 10 1 1 12 10 10 12 10 1 8 1 12 10 1 1 7 1 1 12 10 1 8 1 1 12 10 12 9 1 1 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
instanceKlass com/sun/jna/IntegerType
instanceKlass java/util/concurrent/atomic/Striped64
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 31 1 100 1 7 1 100 1 1 5 0 1 1 12 10 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1
ciInstanceKlass java/lang/Float 1 1 199 1 7 1 1 7 1 100 1 100 1 100 1 1 100 1 100 1 1 1 4 1 4 1 4 1 4 1 4 1 4 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 5 0 1 1 1 100 1 12 10 1 1 100 1 1 12 10 1 1 12 10 1 100 1 12 10 1 8 1 8 1 100 1 1 12 10 1 1 1 100 1 1 12 10 1 1 12 10 1 1 10 1 1 1 1 1 1 1 1 3 1 12 10 12 9 1 1 1 12 10 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 1 1 12 10 3 1 1 1 1 1 1 12 10 1 1 1 12 10 1 12 10 1 1 1 1 100 1 1 12 10 1 1 1 12 10 1 1 100 12 10 1 1 8 1 7 1 1 12 10 12 9 1 1 1 1 1 1 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Double 1 1 253 1 7 1 1 7 1 100 1 100 1 100 1 1 100 1 100 1 1 1 6 0 1 6 0 1 6 0 1 6 0 1 6 0 1 6 0 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 5 0 1 1 1 7 1 12 10 1 1 1 12 10 12 10 1 100 1 1 12 10 1 100 1 1 12 10 6 0 1 8 1 1 12 10 1 8 1 1 12 10 1 8 1 1 12 10 5 0 5 0 1 8 1 8 1 100 1 100 1 12 10 1 1 12 10 1 8 1 1 12 10 1 8 1 8 1 8 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 1 100 1 1 12 10 1 12 10 1 1 10 1 1 1 1 1 1 3 1 12 10 12 9 1 1 12 10 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 5 0 1 1 1 1 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1 100 1 1 12 10 1 1 1 12 10 1 1 100 12 10 1 1 8 1 7 1 1 12 10 12 9 1 1 1 1 1 1 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 197 1 7 1 1 7 1 100 1 100 1 1 7 1 1 1 3 1 3 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 7 1 12 10 1 1 1 1 100 1 1 12 9 1 8 1 1 12 9 1 100 1 1 12 10 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 1 1 1 12 9 1 1 1 100 1 1 12 10 1 100 1 1 12 10 1 8 1 1 12 10 1 8 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 10 1 8 1 8 1 1 1 1 1 10 12 9 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 5 0 1 12 10 1 1 8 1 7 1 1 12 10 12 9 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 204 1 7 1 1 7 1 100 1 100 1 1 7 1 1 1 3 1 3 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 100 1 12 10 1 1 1 100 1 1 12 10 1 100 1 1 12 10 1 8 1 1 12 10 1 8 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 1 1 100 1 1 12 9 1 8 1 1 12 9 1 100 1 1 12 10 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 1 1 12 9 1 12 10 1 1 12 10 10 1 8 1 8 1 1 1 1 3 10 12 9 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 3 3 1 1 5 0 1 12 10 1 1 8 1 7 1 1 12 10 12 9 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Integer 1 1 390 1 7 1 1 7 1 100 1 100 1 100 1 1 7 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 12 10 1 7 1 1 12 9 100 12 9 1 100 1 1 12 10 1 12 10 1 100 1 1 12 10 10 1 1 1 12 10 1 100 1 12 10 1 1 12 10 1 1 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 10 1 12 10 12 9 12 9 1 1 1 100 1 8 1 12 10 1 100 1 12 10 1 8 1 1 12 10 1 12 10 1 8 1 12 10 1 8 1 1 12 10 3 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 1 7 1 1 12 10 1 7 11 1 100 10 11 1 1 12 10 1 8 1 12 10 1 1 8 1 100 1 1 12 10 1 1 12 10 5 0 1 8 12 10 1 12 10 12 10 1 1 1 12 10 1 1 12 9 1 1 12 9 1 12 10 1 1 1 1 3 10 12 9 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 1 12 10 1 100 1 100 1 7 1 1 12 10 1 100 1 12 10 1 1 12 10 1 8 1 8 1 1 12 10 1 8 1 8 1 8 1 8 1 8 1 8 1 12 10 12 10 1 1 1 12 10 1 5 0 1 1 1 1 3 1 1 3 3 3 1 1 1 1 12 10 1 3 1 1 12 10 1 1 1 1 100 1 1 12 10 1 1 1 12 10 1 1 100 12 10 1 1 8 1 7 1 1 12 10 12 9 3 3 3 3 3 12 9 1 1 1 1 1 1 1 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
staticfield java/lang/Integer sizeTable [I 10
ciInstanceKlass java/lang/Long 1 1 440 1 7 1 1 7 1 100 1 100 1 100 1 1 7 1 1 100 1 100 1 1 1 5 0 1 5 0 1 1 1 1 1 1 3 1 3 1 5 0 1 1 1 12 10 1 7 1 1 12 9 1 100 1 7 1 1 12 9 1 100 1 1 12 10 1 12 10 1 100 1 1 12 10 10 1 12 10 1 12 10 1 12 10 1 12 10 5 0 5 0 1 100 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 100 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 1 12 10 10 12 10 5 0 5 0 1 12 9 1 12 9 5 0 1 1 1 100 1 8 1 12 10 1 8 1 12 10 1 8 1 8 1 1 12 10 5 0 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 1 100 1 1 12 10 1 100 11 1 100 10 11 1 1 12 10 1 8 1 12 10 1 1 8 1 100 1 1 12 10 12 10 1 8 1 8 1 1 12 11 1 12 10 12 10 1 1 12 10 1 1 5 0 5 0 1 1 12 9 1 12 10 1 1 1 12 10 1 8 1 8 1 1 12 10 1 8 1 8 1 8 1 8 1 8 1 8 1 12 10 12 10 1 1 12 10 1 1 1 1 3 10 12 9 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 100 1 100 1 100 1 1 12 10 1 100 12 10 1 1 1 1 12 10 1 1 1 5 0 1 1 1 1 1 12 10 1 12 10 1 5 0 5 0 5 0 1 1 1 1 1 12 10 1 5 0 5 0 1 12 10 1 12 10 1 1 1 1 100 1 1 12 10 1 1 1 12 10 1 1 100 12 10 1 1 8 1 7 1 1 12 10 12 9 1 1 1 1 1 1 1 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/util/Iterator 1 1 42 1 7 1 1 100 1 1 1 1 1 1 1 1 100 8 1 1 12 10 1 1 1 1 7 1 1 12 10 12 11 12 11 1 7 1 1 12 11 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 0 0 186 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 9 1 1 1 12 9 1 12 9 1 1 12 10 1 1 12 10 1 100 12 10 12 10 12 9 1 1 12 10 1 1 12 10 1 12 10 1 100 1 12 10 1 100 1 12 10 1 1 12 9 1 100 1 1 12 10 1 100 1 1 12 11 10 1 1 12 9 1 100 1 1 12 10 1 1 12 9 1 1 1 1 100 1 1 12 10 1 12 10 1 100 1 12 11 1 100 1 12 10 1 100 1 12 9 12 9 12 9 1 100 1 1 12 10 1 100 1 1 1 12 10 1 1 12 10 1 1 100 10 1 12 10 1 1 12 10 1 8 12 10 12 10 12 9 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 367 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 12 9 1 1 12 10 1 100 1 12 10 1 1 12 11 1 1 1 1 1 12 11 1 1 1 1 12 11 1 1 1 1 1 12 11 1 1 1 1 100 11 1 100 1 1 12 10 1 1 1 1 12 11 1 1 1 1 12 11 1 1 1 1 1 1 1 100 1 12 11 1 1 1 1 12 11 1 1 1 1 12 11 1 1 1 1 12 11 1 1 1 1 12 11 1 1 1 1 12 11 1 1 1 1 100 1 1 12 10 1 1 1 1 12 11 1 1 1 1 12 11 1 1 1 1 12 11 1 1 1 1 12 11 1 1 1 11 1 1 12 9 1 100 1 12 10 1 1 1 1 12 11 1 1 1 1 1 12 10 1 100 1 1 12 10 1 1 1 1 12 10 12 10 1 1 12 10 1 1 1 1 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 26 1 100 1 100 1 100 1 1 1 1 1 1 12 10 12 9 1 1 1 1 12 10 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 18 1 100 1 1 100 1 100 1 1 1 1 12 10 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 18 1 100 1 1 100 1 100 1 1 1 1 12 10 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 18 1 100 1 1 100 1 100 1 1 1 1 12 10 1 1 1 1
ciInstanceKlass java/lang/NullPointerException 1 1 44 1 7 1 7 1 1 5 0 1 1 1 1 1 1 12 10 1 12 10 1 1 12 9 1 1 12 10 12 9 12 10 1 12 10 1 100 1 100 1 100 1 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 18 1 100 1 100 1 1 5 0 1 1 12 10 1 12 10 1 1
ciInstanceKlass java/lang/StringLatin1 1 1 288 1 7 1 100 1 100 1 100 1 1 1 1 1 1 12 10 1 1 1 100 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 7 1 1 12 10 1 12 10 1 1 1 1 1 7 1 1 12 10 1 1 1 1 1 1 12 10 1 7 1 1 12 10 1 12 10 1 12 10 10 1 12 10 1 7 1 1 12 10 10 1 1 100 1 7 1 1 12 9 1 1 12 10 1 7 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1 7 1 1 12 10 1 12 10 1 1 12 10 1 1 100 12 9 1 100 10 1 100 1 100 1 1 12 10 1 7 1 1 12 10 1 12 10 1 12 10 1 100 1 8 1 12 10 1 8 1 1 12 10 1 1 1 100 10 10 1 7 1 1 12 10 1 8 1 8 1 8 1 1 12 10 1 100 1 12 10 1 12 10 1 1 12 10 1 12 10 1 100 1 1 12 10 1 12 10 12 10 12 10 1 12 10 12 10 1 1 10 1 1 12 10 1 1 12 10 12 10 1 1 1 1 1 1 1 12 10 1 100 1 1 12 10 1 1 1 1 12 10 1 1 1 12 10 10 1 1 7 1 1 12 10 1 1 1 1 1 1
staticfield java/lang/StringLatin1 $assertionsDisabled Z 1
ciInstanceKlass java/lang/Math 1 1 330 1 7 1 100 1 100 1 1 1 6 0 1 6 0 1 6 0 1 6 0 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 7 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 7 1 12 10 3 3 3 1 1 7 1 12 10 5 0 5 0 5 0 5 0 5 0 1 1 1 1 12 9 1 100 1 12 10 1 1 1 100 1 8 1 12 10 1 1 8 1 1 1 12 10 1 1 12 10 5 0 5 0 1 1 3 5 0 1 3 1 1 1 1 1 1 5 0 1 12 10 1 1 12 10 1 1 8 12 10 1 8 1 1 1 12 9 12 9 1 1 1 1 1 12 10 6 0 1 12 10 12 9 1 100 10 1 12 10 1 100 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 12 10 1 1 1 12 10 12 10 6 0 1 1 12 10 1 1 12 10 12 10 12 10 4 1 1 12 10 1 12 10 1 1 12 10 12 10 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 5 0 1 6 0 1 4 1 6 0 4 1 6 0 4 1 1 12 10 12 9 12 10 12 9 1 1 1 7 1 1 12 10 4 6 0 1 1 1 1 1 1
staticfield java/lang/Math negativeZeroFloatBits J -2147483648
staticfield java/lang/Math negativeZeroDoubleBits J -9223372036854775808
staticfield java/lang/Math $assertionsDisabled Z 1
ciInstanceKlass java/lang/Integer$IntegerCache 1 1 80 1 7 1 100 1 7 1 1 1 3 1 1 1 1 1 1 1 1 12 10 1 1 100 1 7 1 1 12 10 12 9 1 8 1 7 1 1 12 10 1 1 12 10 1 100 1 1 12 10 3 1 12 10 1 100 12 9 1 7 1 1 12 10 12 9 100 1 12 10 12 9 1 100 10 1 1 1 1 1
staticfield java/lang/Integer$IntegerCache high I 127
staticfield java/lang/Integer$IntegerCache cache [Ljava/lang/Integer; 256 [Ljava/lang/Integer;
staticfield java/lang/Integer$IntegerCache $assertionsDisabled Z 1
ciInstanceKlass java/util/Collections 1 1 721 1 7 1 7 1 100 1 7 1 7 1 7 1 100 1 7 1 7 1 7 1 7 1 7 1 7 1 7 1 100 1 7 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 7 1 7 1 100 1 100 1 100 1 100 1 7 1 7 1 100 1 100 1 100 1 7 1 100 1 100 1 100 1 100 1 7 1 7 1 100 1 100 1 100 1 7 1 7 1 7 1 100 1 7 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 7 1 12 11 1 1 1 1 1 1 7 1 1 12 11 1 12 10 1 12 10 1 1 12 11 1 7 1 1 12 11 1 1 12 11 1 100 1 12 10 1 1 12 11 1 1 12 11 1 12 11 1 1 12 10 12 10 12 10 1 100 1 1 12 11 1 1 1 1 12 10 1 12 11 1 1 12 11 1 12 9 1 100 10 1 12 10 1 1 1 12 10 1 1 12 11 1 100 1 12 10 1 1 12 11 1 1 1 1 1 1 1 100 1 8 1 12 10 1 1 1 1 7 1 1 12 11 1 100 11 1 1 12 11 1 1 12 10 1 12 10 1 1 1 1 12 10 1 12 10 1 1 1 12 11 12 10 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 12 10 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 10 1 1 1 12 10 1 1 10 1 1 1 12 10 1 10 1 10 1 1 10 10 1 1 12 10 10 1 1 10 1 1 10 1 1 10 1 1 1 1 12 10 1 1 1 1 100 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 9 1 1 1 12 9 1 1 1 1 1 12 9 1 1 1 12 9 1 1 1 1 1 12 9 1 1 1 1 1 1 12 9 1 1 1 12 9 1 1 1 1 1 12 9 1 1 1 1 1 1 12 10 1 1 1 10 1 1 1 10 1 1 1 10 1 1 1 1 12 10 1 1 1 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 10 1 12 10 1 1 1 1 1 12 9 1 1 1 1 12 9 1 1 12 9 12 10 1 1 1 10 1 1 1 1 7 10 1 7 1 12 11 1 12 11 1 12 10 1 1 1 1 1 1 1 100 11 1 12 11 1 1 1 1 11 1 1 1 10 1 1 1 1 100 1 1 12 10 1 100 1 12 10 1 10 10 10 1 1 1 1 1 1 1
staticfield java/util/Collections EMPTY_SET Ljava/util/Set; java/util/Collections$EmptySet
staticfield java/util/Collections EMPTY_LIST Ljava/util/List; java/util/Collections$EmptyList
staticfield java/util/Collections EMPTY_MAP Ljava/util/Map; java/util/Collections$EmptyMap
instanceKlass java/nio/charset/UnsupportedCharsetException
instanceKlass java/nio/charset/IllegalCharsetNameException
instanceKlass java/lang/NumberFormatException
ciInstanceKlass java/lang/IllegalArgumentException 1 1 24 1 100 1 7 1 1 5 0 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1
ciInstanceKlass java/lang/StringUTF16 1 1 468 1 7 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 3 1 1 1 1 12 10 1 1 1 100 10 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 8 1 1 12 10 1 12 10 1 1 1 12 9 1 1 12 10 1 100 1 8 1 12 10 12 9 12 9 1 1 1 1 10 1 1 12 10 12 10 1 100 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 1 1 12 10 12 10 1 1 1 1 12 10 1 1 12 10 1 100 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 100 1 100 1 12 10 10 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 100 1 1 12 10 1 1 100 1 12 10 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 3 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 7 1 12 10 1 12 10 1 1 12 10 1 1 1 12 9 1 12 10 12 10 1 12 10 1 1 100 12 10 12 10 10 1 100 12 10 12 10 10 1 100 1 1 12 10 1 7 1 1 12 10 1 12 10 1 12 10 1 8 1 8 1 1 1 1 12 10 1 1 100 10 1 100 1 1 12 10 1 100 1 12 10 1 8 1 8 1 8 1 1 12 10 1 1 12 10 12 10 1 12 10 1 100 1 12 10 1 1 12 10 1 12 10 1 100 1 12 10 12 10 12 10 1 12 10 12 10 1 1 1 12 10 1 1 12 10 1 12 10 1 12 10 12 10 1 1 1 1 1 1 1 12 10 1 100 1 1 12 10 1 1 1 1 1 12 10 1 1 12 10 1 1 100 1 12 11 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 12 9 1 12 9 5 0 5 0 1 12 10 12 10 12 10 1 1 7 1 12 10 12 10 1 1 1 1 1 1 1
staticfield java/lang/StringUTF16 HI_BYTE_SHIFT I 0
staticfield java/lang/StringUTF16 LO_BYTE_SHIFT I 8
staticfield java/lang/StringUTF16 $assertionsDisabled Z 1
ciInstanceKlass java/util/regex/Pattern 1 1 1375 1 7 1 7 1 100 1 100 1 7 1 7 1 100 1 7 1 100 1 100 1 7 1 100 1 100 1 7 1 100 1 100 1 7 1 100 1 100 1 100 1 7 1 7 1 7 1 7 1 7 1 7 1 7 1 7 1 7 1 7 1 7 1 100 1 100 1 100 1 100 1 7 1 7 1 100 1 100 1 100 1 100 1 7 1 7 1 100 1 100 1 7 1 100 1 100 1 100 1 100 1 7 1 7 1 7 1 7 1 7 1 7 1 7 1 7 1 100 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 12 10 1 1 12 9 1 1 1 12 9 1 12 10 1 100 1 7 1 12 10 1 12 9 1 1 12 10 12 10 1 12 10 1 1 1 7 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 7 1 1 12 11 12 11 1 1 12 10 1 12 11 1 7 1 1 12 10 1 12 10 1 1 12 10 1 7 1 1 12 11 1 7 1 12 10 1 1 1 8 1 1 12 10 1 100 10 1 8 1 1 12 10 10 10 3 1 12 10 1 12 10 1 8 1 12 10 1 1 1 100 1 100 1 100 1 12 10 12 9 12 9 12 9 12 9 12 9 1 12 10 12 9 12 9 1 100 10 1 100 1 8 1 100 1 1 12 10 1 12 10 1 8 1 1 12 10 1 1 1 12 10 1 1 12 10 1 12 10 12 9 1 100 10 1 7 1 1 12 10 1 1 12 10 1 8 12 10 1 12 10 1 100 1 1 12 10 1 1 12 10 1 1 12 9 1 12 10 1 1 12 10 1 7 1 12 10 1 12 10 1 100 10 1 100 11 1 1 12 10 1 8 1 16 1 1 12 10 15 16 1 7 1 1 12 10 15 1 12 18 1 1 12 11 10 1 1 12 10 1 8 1 12 9 1 12 10 1 8 1 1 12 10 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 100 10 1 100 1 1 12 10 1 100 1 12 10 1 1 100 12 9 12 9 1 7 1 12 10 1 12 10 1 100 1 8 10 1 12 10 1 12 10 1 100 1 1 12 10 1 12 10 12 10 12 9 12 9 1 12 10 12 10 12 9 12 9 12 9 10 12 9 1 1 12 10 1 12 9 1 1 12 10 12 9 1 12 10 1 8 1 8 1 12 10 10 12 9 1 1 12 11 1 7 1 12 11 1 12 11 1 12 9 1 1 1 100 10 1 100 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 100 1 12 10 1 1 3 1 1 12 10 1 12 10 10 12 10 1 12 10 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 12 10 12 10 1 1 12 10 1 12 10 10 10 10 1 12 10 10 1 1 12 10 10 1 12 10 1 12 10 1 8 1 8 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 100 1 1 12 10 1 1 12 10 1 12 10 10 12 10 1 12 10 12 10 1 12 9 10 1 7 1 12 10 1 1 12 10 12 9 1 12 11 10 1 12 10 11 1 12 10 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 10 10 1 12 9 1 12 10 1 8 1 12 10 12 10 1 12 11 1 8 1 8 1 12 11 1 12 10 1 12 10 1 12 10 10 1 8 10 1 1 12 11 1 8 1 12 11 1 8 1 1 12 10 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 12 10 1 8 1 1 12 10 1 12 10 1 12 10 1 8 1 12 10 1 1 12 10 1 12 10 12 10 1 8 1 8 1 100 1 1 12 9 1 12 10 1 12 10 1 8 1 8 1 8 1 8 1 8 1 8 1 1 12 10 1 12 10 1 1 12 10 1 8 1 8 1 8 1 8 1 1 12 10 1 8 1 12 10 1 12 10 1 8 1 8 1 12 10 1 8 1 12 10 1 8 11 1 1 12 10 10 10 1 1 12 9 1 12 10 1 8 1 8 1 12 10 1 1 12 11 1 1 12 9 10 1 1 12 10 1 12 9 1 8 12 10 1 12 9 1 12 9 1 12 10 10 10 10 11 1 12 11 1 8 1 12 10 1 8 1 8 12 10 1 12 9 1 12 9 1 12 9 9 1 12 9 1 12 9 1 12 9 1 12 9 1 12 10 9 12 10 11 10 1 12 10 9 9 1 12 9 1 8 10 10 1 1 12 9 1 12 10 1 1 1 12 9 1 1 12 10 1 12 10 1 12 10 1 12 10 12 10 1 8 1 8 1 8 1 8 1 8 1 12 10 1 12 10 3 1 8 1 8 1 8 1 1 1 8 12 10 1 12 10 12 10 1 12 10 1 1 12 10 12 10 1 8 1 12 10 1 8 1 8 1 8 11 1 12 10 10 10 10 10 10 1 1 1 12 9 1 12 9 1 12 10 16 1 12 10 15 1 12 18 1 12 10 15 18 1 12 10 15 12 18 1 12 10 15 18 1 12 10 15 18 1 12 10 15 12 18 1 12 10 15 12 18 1 1 12 10 15 12 18 1 12 10 15 18 1 3 3 1 12 10 15 18 1 12 10 15 12 18 1 12 10 15 18 1 12 10 15 18 1 1 1 16 1 12 10 15 16 1 1 12 18 1 1 12 10 15 18 1 1 1 10 1 100 1 1 12 10 1 100 1 1 12 10 12 10 1 1 7 1 12 10 10 12 9 10 1 1 1 1 1 1 1 1
staticfield java/util/regex/Pattern accept Ljava/util/regex/Pattern$Node; java/util/regex/Pattern$Node
staticfield java/util/regex/Pattern lastAccept Ljava/util/regex/Pattern$Node; java/util/regex/Pattern$LastNode
staticfield java/util/regex/Pattern $assertionsDisabled Z 1
ciInstanceKlass java/util/regex/Matcher 1 1 401 1 7 1 7 1 100 1 100 1 100 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 12 9 12 9 12 9 1 12 9 12 9 1 12 9 1 7 1 1 12 10 12 9 1 12 9 12 9 1 12 9 1 7 12 9 1 1 12 10 1 1 1 1 1 7 1 1 12 11 1 12 10 1 1 12 10 100 1 1 12 10 1 12 10 1 1 1 100 1 8 1 12 10 12 9 1 12 10 12 9 1 12 10 12 9 1 1 1 100 1 8 10 1 1 100 1 100 10 1 8 1 1 12 10 1 12 10 10 10 1 1 12 10 1 1 1 12 10 1 8 1 1 12 10 1 1 1 1 1 12 10 1 1 1 12 10 1 8 1 1 1 100 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1 1 12 10 1 100 1 12 10 1 12 10 1 1 12 10 1 12 10 1 8 1 8 1 100 1 12 10 1 12 10 1 12 10 10 1 8 1 8 1 8 1 8 1 1 12 10 1 100 1 1 12 11 1 8 1 8 1 1 12 11 1 100 1 12 10 1 8 12 10 12 10 1 1 1 1 12 10 12 10 12 10 1 1 1 100 1 12 10 1 100 1 12 11 1 100 10 1 1 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 1 100 1 8 10 1 1 8 8 1 8 1 1 1 1 1 1 1 1 8 1 8 12 10 1 12 10 1 8 12 10 12 10 1 8 12 10 12 9 12 9 1 1 12 9 1 12 10 1 12 9 11 1 12 11 11 1 8 1 12 10 1 8 1 8 1 1 1 1 1 1
ciInstanceKlass java/util/regex/IntHashSet 1 1 37 1 7 1 7 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 1 7 1 1 12 10 1 1 1 1 1 12 10 1 100 1 1
ciInstanceKlass java/lang/AssertionError 0 0 62 1 100 1 100 1 1 5 0 1 1 12 10 1 12 10 1 1 100 1 1 12 10 10 1 100 1 1 12 10 1 100 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 1
instanceKlass java/util/Collections$UnmodifiableRandomAccessList
ciInstanceKlass java/util/Collections$UnmodifiableList 1 1 103 1 7 1 1 7 1 7 1 100 1 1 1 100 1 100 1 1 1 5 0 1 1 1 1 1 1 1 12 10 12 9 1 1 12 11 1 1 12 11 1 1 1 12 11 1 1 1 1 100 1 12 10 1 1 1 1 1 1 12 11 1 12 11 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 12 11 12 10 1 1 1 100 10 1 100 1 1 1 1 1 1
ciInstanceKlass java/util/Collections$UnmodifiableRandomAccessList 1 1 40 1 7 1 1 7 1 100 1 100 1 1 1 1 5 0 1 1 1 12 10 1 1 1 1 1 12 9 1 7 12 11 10 1 1 1 1 1 1 1
instanceKlass org/eclipse/m2e/core/internal/project/registry/StaleMutableProjectRegistryException
instanceKlass java/util/concurrent/CancellationException
instanceKlass java/nio/channels/OverlappingFileLockException
ciInstanceKlass java/lang/IllegalStateException 1 0 24 1 100 1 100 1 1 5 0 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1
ciInstanceKlass lombok/patcher/TargetMatcher 1 0 15 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass lombok/patcher/MethodTarget 1 1 305 7 1 7 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 1 1 1 1 1 1 1 10 7 1 12 1 1 9 12 8 1 9 12 8 1 9 12 1 1 1 1 9 12 10 7 1 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 9 12 10 12 1 1 1 1 1 1 1 1 1 1 1 9 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 1 1 1 1 8 1 10 12 1 1 10 100 1 12 1 10 12 100 1 8 10 8 8 8 8 1 10 12 1 1 8 1 100 1 8 1 10 10 7 1 12 1 1 10 7 1 12 1 1 1 1 1 10 12 1 1 10 7 1 12 1 8 1 7 1 10 10 12 1 11 7 1 12 1 10 12 1 1 1 1 1 1 1 1 1 10 12 1 1 1 1 1 10 12 1 1 1 10 12 10 12 1 1 10 12 11 12 1 1 11 7 1 12 1 1 10 12 1 11 12 1 1 1 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 1 1 1 1 1 8 1 10 12 1 1 1 10 12 11 1 10 12 1 1 11 1 1 1 8 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 1 1
staticfield lombok/patcher/MethodTarget PARAM_SPEC Ljava/util/regex/Pattern; java/util/regex/Pattern
staticfield lombok/patcher/MethodTarget COMPLETE_SPEC Ljava/util/regex/Pattern; java/util/regex/Pattern
staticfield lombok/patcher/MethodTarget BRACE_PAIRS Ljava/util/regex/Pattern; java/util/regex/Pattern
ciInstanceKlass lombok/patcher/Hook 1 1 233 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 10 12 1 8 1 8 1 11 7 1 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 7 1 12 1 1 9 12 1 1 1 1 1 10 100 1 8 1 10 12 1 8 8 8 9 12 9 12 9 12 7 1 10 11 7 1 12 1 1 10 12 1 1 9 12 1 1 1 1 1 1 1 8 10 7 1 12 1 1 1 1 1 1 1 1 1 10 12 1 1 1 7 1 10 8 1 10 12 1 1 11 12 1 1 11 7 1 12 1 1 10 12 1 11 12 1 8 1 10 12 1 1 1 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 11 12 1 1 8 1 10 12 1 1 8 1 10 12 1 1 10 1 1 10 12 11 1 10 12 1 1 11 1 1 1 8 1 10 8 1 8 1 8 1 10 12 1 8 1 1 1
staticfield lombok/patcher/Hook PRIMITIVES Ljava/util/Map; java/util/Collections$UnmodifiableMap
instanceKlass lombok/patcher/scripts/AddFieldScript$1
instanceKlass lombok/patcher/PatchScript$MethodPatcher
instanceKlass org/lombokweb/asm/ClassWriter
instanceKlass lombok/patcher/PatchScript$NoopClassVisitor
ciInstanceKlass org/lombokweb/asm/ClassVisitor 1 1 165 1 7 1 7 1 1 1 1 1 1 1 1 12 10 1 1 12 10 3 3 3 3 3 3 3 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 100 1 1 12 10 12 9 12 9 1 1 1 1 1 3 1 100 1 8 10 12 10 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 8 12 10 1 1 8 12 10 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 1 8 12 10 1 1 1 1 1 12 10 1 1 1 1 8 12 10 1 1 1 8 12 10 1 1 1 12 10 1 1 1 1 1 8 12 10 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1
instanceKlass lombok/patcher/PatchScript$FixedClassWriter
ciInstanceKlass org/lombokweb/asm/ClassWriter 1 1 578 1 7 1 7 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 3 12 10 12 9 1 7 1 12 10 1 12 10 12 9 12 9 1 1 1 1 1 1 12 9 12 9 3 1 1 12 10 12 9 1 1 12 10 12 9 1 1 12 10 1 7 1 12 9 12 9 12 9 12 9 1 1 1 1 1 1 1 1 1 12 9 1 7 1 12 10 3 1 1 12 10 12 9 1 1 1 1 1 100 1 12 10 1 12 10 12 9 1 1 12 9 1 1 1 12 9 1 1 12 10 12 9 1 1 1 1 12 9 1 7 1 1 12 10 12 9 1 1 1 1 12 9 1 12 10 12 9 1 1 1 1 1 12 9 1 12 9 1 1 12 9 12 9 1 1 12 10 1 1 12 9 12 9 1 1 1 12 9 1 12 9 12 9 1 1 1 1 1 1 1 100 1 12 10 12 9 12 9 1 1 12 9 1 1 1 1 7 1 12 10 12 9 12 9 1 1 12 9 1 1 1 1 1 1 7 1 12 10 12 9 12 9 1 1 12 9 1 1 1 1 1 1 1 12 10 1 12 10 1 12 9 1 8 1 8 1 8 1 8 1 8 1 8 3 1 8 1 8 1 12 10 1 8 1 8 1 8 1 12 10 1 12 10 1 12 10 1 8 1 8 1 8 3 1 12 10 1 8 10 1 12 10 1 12 10 1 12 10 1 100 1 1 12 10 1 12 10 10 3 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 9 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 12 10 1 100 1 12 10 1 1 12 10 12 10 1 1 1 10 1 12 10 1 1 12 10 10 10 1 12 10 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 12 10 1 1 12 10 12 10 1 8 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 100 1 12 10 10 1 1 1 1 1
ciInstanceKlass lombok/patcher/PatchScript$FixedClassWriter 1 1 35 100 1 7 1 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 10 12 8 1 100 1 1 1 1 1 1 1 100 1 1
instanceKlass lombok/patcher/scripts/SetSymbolDuringMethodCallScript$2
ciInstanceKlass lombok/patcher/PatchScript$MethodPatcher 1 1 184 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 10 12 1 7 1 10 12 1 9 12 9 12 9 12 9 12 1 1 1 1 1 1 1 1 9 12 1 1 11 7 1 12 1 1 1 1 1 1 9 12 10 12 1 1 1 1 1 1 1 1 1 100 1 8 1 10 12 1 1 1 11 12 1 1 11 7 1 12 1 1 7 1 7 1 8 1 10 10 12 1 10 7 1 12 1 1 8 1 10 12 1 1 10 12 1 11 7 1 12 1 1 9 12 10 7 1 12 1 1 11 12 1 1 1 1 1 10 12 10 12 1 10 12 1 10 12 1 11 12 1 7 1 11 12 1 1 7 1 10 12 1 11 7 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass lombok/patcher/PatchScript$MethodPatcherFactory 1 0 13 100 1 100 1 1 1 1 1 1 100 1 1
ciInstanceKlass lombok/patcher/scripts/AddFieldScript$1 1 1 80 7 1 7 1 1 1 1 1 1 1 1 9 12 10 12 1 9 12 1 1 1 1 1 1 1 1 1 1 10 7 1 12 1 1 10 7 1 12 1 1 10 12 1 1 1 1 1 1 1 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 10 7 1 12 10 1 1 1 1 1 12 1 1 1
ciInstanceKlass org/lombokweb/asm/ClassReader 1 1 1052 1 7 1 7 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 12 10 12 9 12 9 1 1 12 10 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 12 9 1 7 12 9 10 12 9 12 9 1 100 12 9 1 1 12 10 12 9 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 12 10 1 1 1 1 12 10 1 8 1 100 1 1 12 10 1 1 1 100 1 8 10 1 1 12 10 1 100 10 1 100 1 1 12 10 1 12 10 1 12 10 1 12 10 10 1 1 12 10 1 1 1 1 1 1 1 1 12 10 1 100 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 7 1 12 10 1 1 1 1 7 10 1 1 12 9 12 9 12 9 1 12 10 1 12 10 1 12 10 1 8 1 1 12 10 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 3 1 8 1 8 1 1 12 10 1 8 1 8 1 8 3 1 8 1 8 1 8 1 8 1 1 12 10 1 1 12 9 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 9 1 1 12 9 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 100 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 100 10 10 10 10 1 1 1 1 1 1 8 1 1 12 10 1 1 12 10 1 7 10 10 10 10 1 1 1 1 1 1 1 12 9 1 12 9 1 12 9 1 8 1 8 1 8 1 8 1 8 1 8 12 10 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 100 10 10 10 1 1 12 10 10 1 12 10 1 1 12 10 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 12 9 1 1 12 10 1 1 12 10 1 8 1 1 12 10 1 8 1 8 1 1 12 10 1 1 12 10 1 8 1 8 1 12 9 1 12 9 1 12 9 1 12 9 1 1 12 9 1 12 9 1 12 9 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 100 10 1 12 10 1 1 12 10 1 1 12 10 1 12 9 1 12 9 1 12 9 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 12 10 1 1 12 9 1 1 100 1 12 10 1 12 10 1 1 1 1 1 1 1 1 3 3 3 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 100 1 1 12 10 1 12 10 1 100 1 12 10 1 100 1 12 10 1 100 1 1 12 9 1 12 9 1 12 10 1 7 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 7 1 1 12 9 1 1 12 10 1 12 9 1 12 9 1 12 9 1 12 9 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 12 9 1 12 9 1 1 1 1 1 1 12 9 1 12 10 10 1 1 1 1 1 1 5 0 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 7 1 12 10 1 12 10 1 7 1 12 10 1 12 10 1 12 10 1 12 10 12 10 12 10 1 1
ciInstanceKlass java/util/NoSuchElementException 0 0 24 1 100 1 100 1 1 5 0 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1
instanceKlass org/objectweb/asm/MethodTooLargeException
instanceKlass org/objectweb/asm/ClassTooLargeException
instanceKlass org/lombokweb/asm/MethodTooLargeException
instanceKlass org/lombokweb/asm/ClassTooLargeException
instanceKlass java/lang/ArrayIndexOutOfBoundsException
ciInstanceKlass java/lang/IndexOutOfBoundsException 1 0 39 1 100 1 100 1 1 5 0 1 1 12 10 1 12 10 1 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1
instanceKlass org/lombokweb/asm/AnnotationWriter
ciInstanceKlass org/lombokweb/asm/AnnotationVisitor 1 1 88 1 100 1 100 1 1 1 1 1 1 1 1 12 10 1 1 12 10 3 3 3 3 3 3 3 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 100 1 1 12 10 12 9 12 9 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1
ciInstanceKlass org/lombokweb/asm/AnnotationWriter 1 1 254 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 12 10 12 9 12 9 12 9 1 100 1 12 9 12 9 12 9 12 9 1 1 1 1 12 10 1 100 1 1 12 10 1 1 12 10 12 10 1 1 1 1 100 1 1 12 10 1 100 1 1 12 10 1 1 1 1 1 1 12 9 1 100 1 1 12 10 1 100 1 1 12 10 1 1 12 10 1 100 1 12 9 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 100 1 100 1 100 1 100 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 1 12 10 1 8 1 12 9 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 1 1 1 1 1 8 12 10 1 8 1 8 1 8 1 1 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1
instanceKlass lombok/patcher/scripts/ReplaceMethodCallScript$ReplaceMethodCall
instanceKlass lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues
instanceKlass lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly
instanceKlass org/lombokweb/asm/MethodWriter
ciInstanceKlass org/lombokweb/asm/MethodVisitor 1 1 252 1 7 1 7 1 1 1 1 8 1 1 1 1 1 1 1 12 10 1 1 12 10 3 3 3 3 3 3 3 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 100 1 1 12 10 12 9 12 9 1 1 1 1 1 1 100 10 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 8 12 10 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 100 1 100 1 1 12 10 1 100 1 8 12 10 1 1 1 12 10 1 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/MethodWriter 1 1 809 1 7 1 7 1 1 100 1 7 1 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 12 10 1 7 1 12 10 12 9 12 9 8 1 7 1 1 12 10 3 12 9 1 7 1 1 12 10 12 9 12 9 12 9 12 9 12 9 12 9 12 9 1 1 12 10 1 7 1 12 9 12 9 1 7 1 12 10 12 9 12 9 1 7 10 12 9 1 1 12 10 1 1 1 1 1 1 1 1 1 1 12 9 12 9 1 1 12 9 12 9 1 1 12 10 1 1 12 9 1 7 1 12 10 1 1 12 9 1 1 12 10 12 9 1 1 1 12 9 1 12 10 12 9 1 1 1 1 1 12 9 12 9 1 1 1 12 9 1 12 10 12 9 1 1 1 1 1 12 10 12 9 1 12 9 12 9 1 1 1 1 12 9 1 1 12 9 1 100 12 10 1 100 1 1 12 10 1 1 12 10 1 1 12 10 12 9 10 1 12 9 1 1 12 10 12 9 1 1 12 10 1 12 10 1 1 12 10 1 100 1 8 1 12 10 12 9 12 9 1 100 10 1 12 10 1 1 12 10 10 12 9 1 7 1 1 12 9 1 12 9 12 9 12 9 1 7 1 1 12 10 1 1 1 1 1 1 1 1 1 1 12 9 1 1 12 10 12 9 1 12 10 1 1 1 1 1 1 12 10 1 12 10 1 1 1 1 12 9 1 12 9 12 9 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 12 10 12 10 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 12 9 1 1 12 10 1 1 12 10 1 12 10 12 9 1 1 1 1 1 1 12 9 1 1 12 10 12 9 12 9 12 9 1 12 9 1 1 1 12 10 1 12 9 1 12 9 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 3 12 9 12 9 1 1 1 7 1 12 10 12 9 1 12 9 1 1 1 1 1 1 1 12 9 12 9 12 9 12 9 1 1 1 100 1 12 10 1 1 12 9 12 9 1 1 1 12 10 1 12 10 1 12 9 1 8 1 1 12 10 1 12 9 1 12 9 1 12 9 1 100 1 1 12 9 1 12 10 1 12 9 1 12 9 1 12 10 1 12 9 1 12 9 1 1 12 10 1 12 9 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 12 10 1 12 9 1 12 10 1 12 9 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 1 1 1 12 10 1 7 1 12 10 1 12 10 1 1 1 1 12 10 3 1 7 1 1 12 10 1 1 1 1 1 1 1 1 12 9 12 9 1 1 1 3 1 100 1 1 12 10 1 12 10 1 8 1 1 12 10 1 8 1 8 1 8 1 8 1 8 1 8 1 12 10 1 8 1 1 12 10 1 8 1 12 10 1 12 10 1 8 1 1 12 10 1 8 1 8 1 8 1 12 10 1 1 1 12 9 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1
staticfield org/lombokweb/asm/MethodWriter STACK_SIZE_DELTA [I 202
ciInstanceKlass org/lombokweb/asm/SymbolTable 1 1 623 1 7 1 7 1 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 1 7 10 12 9 1 1 1 1 7 1 1 12 9 1 1 12 10 1 12 9 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 100 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 8 1 7 1 1 12 10 12 9 12 9 1 1 12 10 1 12 10 3 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 1 12 9 1 1 1 1 12 10 1 7 1 12 9 1 1 1 12 9 1 1 1 1 12 10 1 12 9 1 1 1 12 10 1 1 12 10 1 1 1 1 12 9 12 9 1 1 12 9 1 1 1 1 1 1 1 1 1 1 7 1 12 10 1 1 12 10 1 7 10 1 7 1 1 12 10 1 7 10 1 7 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 7 1 12 10 1 12 10 1 12 10 1 12 10 1 100 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 100 10 10 1 1 12 10 1 1 12 10 1 1 12 10 1 100 10 1 8 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 12 10 1 1 1 1 1 1 1 1 12 10 12 10 12 9 12 9 12 9 12 9 1 12 10 1 1 12 10 1 12 10 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 9 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 9 12 10 12 10 1 1 12 10 1 12 10 1 12 10 12 10 1 12 10 12 10 1 1 1 1 12 10 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 12 10 12 10 10 1 12 10 1 1 1 1 1 1 1 1 1 1 12 9 1 1 1 12 9 1 1 12 9 1 12 9 1 1 12 10 1 1 1 12 10 1 12 10 1 1 1 1 1 12 10 9 1 1 1 12 9 1 100 1 1 12 10 12 10 1 1 1 1 1 1 100 1 1 12 10 1 12 9 1 1 12 10 1 12 9 12 9 1 12 10 1 1 1 10 1 1 1 1 1 1 1 1 1
instanceKlass org/lombokweb/asm/SymbolTable$Entry
ciInstanceKlass org/lombokweb/asm/Symbol 1 1 93 1 7 1 7 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 12 9 12 9 1 1 1 1 12 9 1 7 1 12 10 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/SymbolTable$Entry 1 1 38 1 7 1 7 1 1 100 1 1 1 1 1 1 1 1 12 10 12 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/ByteVector 1 1 107 1 7 1 7 1 1 1 1 1 1 1 12 10 12 9 1 1 1 1 1 12 9 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 12 10 3 1 100 1 8 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 12 10 1 1 1 100 1 8 1 12 10 1 1 1 1 1 1 1
ciInstanceKlass lombok/patcher/scripts/ExitFromMethodEarlyScript$1 1 1 90 7 1 7 1 100 1 1 1 1 1 1 1 1 9 12 9 12 10 12 1 1 1 1 1 1 1 10 7 1 12 1 1 10 12 1 1 10 7 1 12 1 1 100 1 100 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 7 1 10 12 1 1 1 1 1 1 1 1 1 1 12 1 1 1 100 1 100 1 1 1 1
ciInstanceKlass lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1 1 160 7 1 7 1 1 1 1 1 1 1 1 1 1 9 12 3 10 12 1 9 12 9 12 1 1 1 1 1 1 1 1 10 7 1 12 1 1 10 7 1 12 1 1 9 12 10 12 1 1 10 12 1 10 12 1 1 9 7 1 12 1 1 11 7 1 12 1 1 10 12 1 1 9 12 1 1 11 7 1 12 1 1 11 7 1 12 1 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 1 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 7 1 10 12 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/Attribute 1 1 137 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 12 10 12 9 1 1 1 1 1 1 1 100 1 1 12 10 12 9 1 100 1 12 9 1 100 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 12 10 1 1 1 1 1 1 1 1 12 9 1 1 1 1 12 10 1 1 1 7 12 9 1 1 12 10 12 10 12 9 1 1 1 12 10 1 8 1 8 3 1 8 1 1 1 1 1 12 10 1 1 1 1 12 10 1 12 10 1 12 9 1 1 12 10 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/Context 1 1 43 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1
instanceKlass org/lombokweb/asm/CurrentFrame
ciInstanceKlass org/lombokweb/asm/Frame 0 0 486 1 100 1 100 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 3 1 3 1 3 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 1 1 1 1 12 9 12 9 12 9 12 9 12 9 12 9 12 9 12 9 1 1 1 1 100 1 1 12 10 1 100 1 100 1 1 12 10 1 1 12 10 1 1 12 10 1 100 1 12 9 1 8 1 12 9 1 100 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 12 10 1 12 10 1 8 1 1 1 1 1 1 3 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 12 10 1 100 1 12 9 12 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 1 100 1 1 12 10 1 1 1 1 1 12 9 1 1 1 1 1 12 10 12 10 1 1 1 1 12 10 12 10 1 1 1 1 1 1 1 3 1 1 12 10 1 100 1 12 9 1 1 1 1 1 1 12 9 1 8 8 1 8 1 8 12 10 1 100 10 12 10 12 10 12 10 1 8 12 10 1 12 9 12 10 3 3 3 3 3 3 3 3 10 1 12 10 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 10 1 12 10 1 1 1 1 1 3 1 12 10 8 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 1 1 12 10 1 12 9 1 12 10 1 1 12 9 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/Type 1 1 362 1 7 1 7 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 1 1 1 1 7 1 1 12 10 1 1 12 10 1 1 1 1 100 1 1 12 10 1 100 1 1 12 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 10 1 1 12 10 12 10 1 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 12 10 12 10 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 7 1 12 10 1 1 1 1 1 1 1 100 1 1 12 10 12 10 1 1 1 1 1 1 12 10 1 12 10 1 12 10 1 100 1 100 10 1 8 1 1 12 10 1 12 10 1 12 10 1 1 1 1 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 12 10 12 10 10 1 8 1 1 12 10 1 1 12 10 1 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 100 10 1 8 1 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 12 10 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 100 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield org/lombokweb/asm/Type VOID_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type BOOLEAN_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type CHAR_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type BYTE_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type SHORT_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type INT_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type FLOAT_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type LONG_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type DOUBLE_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
ciInstanceKlass org/lombokweb/asm/Label 1 1 221 1 7 1 7 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 12 9 1 100 1 8 1 12 10 12 9 1 1 12 9 1 100 1 12 9 1 1 12 9 12 9 1 7 1 1 12 10 1 1 1 1 1 7 1 1 12 10 3 1 1 12 10 1 1 1 1 1 1 1 1 7 1 12 9 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 1 12 9 1 1 1 1 1 1 1 1 12 9 1 1 1 1 1 1 1 12 9 12 9 12 9 1 1 12 10 1 1 1 1 100 12 9 12 9 1 12 9 1 12 10 1 1 1 1 12 9 1 1 1 1 1 1 1 100 10 1 8 1 1 12 10 1 1 12 10 1 12 10 12 10 1 10 1 1 1 1 1
staticfield org/lombokweb/asm/Label EMPTY_LIST Lorg/lombokweb/asm/Label; org/lombokweb/asm/Label
ciInstanceKlass lombok/patcher/MethodLogistics 1 1 169 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 12 1 9 12 10 7 1 12 1 1 11 7 1 12 1 1 11 7 1 12 1 1 7 1 10 12 1 1 9 12 10 12 1 9 12 7 1 10 10 7 1 12 1 1 11 12 1 1 10 12 1 11 12 1 1 10 7 1 12 1 1 9 12 9 12 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 11 12 1 1 1 11 12 1 1 10 12 1 10 7 1 12 1 1 1 1 1 1 10 12 10 12 1 1 1 1 1 1 1 1 10 12 1 1 100 1 8 1 10 12 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 1 10 1 1
ciInstanceKlass lombok/patcher/scripts/WrapReturnValuesScript$1 1 1 54 7 1 7 1 100 1 1 1 1 1 1 1 1 9 12 9 12 10 12 1 1 1 1 1 1 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 100 1 12 1 1 1 100 1 100 1 1 1 1
ciInstanceKlass lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 1 1 155 7 1 7 1 1 1 1 1 1 1 1 1 1 1 9 12 3 10 12 1 9 12 9 12 10 7 1 12 1 1 9 12 1 1 1 1 1 1 1 1 1 10 7 1 12 1 1 10 12 10 12 1 1 9 7 1 12 1 1 11 7 1 12 1 1 10 12 1 1 9 12 10 12 1 1 10 12 1 9 12 1 10 12 1 9 12 1 1 11 7 1 12 1 1 11 7 1 12 1 1 10 12 1 10 12 1 11 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 7 1 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/Handle 1 1 82 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 12 10 12 9 12 9 12 9 12 9 12 9 1 1 1 1 1 1 1 1 1 1 7 12 10 1 1 1 1 12 10 1 1 100 10 1 1 12 10 1 12 10 1 8 1 12 10 1 8 1 8 12 10 1 1 1 1 1 1
ciInstanceKlass lombok/patcher/scripts/ReplaceMethodCallScript$ReplaceMethodCall 1 1 134 7 1 7 1 1 1 1 1 1 1 1 1 1 9 12 3 10 12 1 9 12 9 12 1 1 1 1 1 1 1 1 10 7 1 12 1 1 10 7 1 12 1 1 10 7 1 12 1 1 10 12 1 10 12 1 10 12 1 1 9 7 1 12 1 1 11 7 1 12 1 9 12 10 7 1 12 1 1 9 12 1 1 11 7 1 12 1 1 11 7 1 12 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/ConcurrentModificationException 0 0 24 1 100 1 100 1 1 5 0 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1
ciMethod java/lang/Object <init> ()V 1024 0 482205 0 128
ciMethod java/lang/Object getClass ()Ljava/lang/Class; 512 0 256 0 -1
ciMethod java/lang/CharSequence length ()I 0 0 1 0 -1
ciMethod java/lang/String <init> ([CII)V 324 0 7336 0 1120
ciMethod java/lang/String rangeCheck ([CII)Ljava/lang/Void; 1024 0 7336 0 0
ciMethod java/lang/String length ()I 742 0 526097 0 -1
ciMethod java/lang/String charAt (I)C 618 0 1597711 0 160
ciMethod java/lang/String equals (Ljava/lang/Object;)Z 588 0 8562 0 416
ciMethod java/lang/String hashCode ()I 514 0 10907 0 352
ciMethod java/lang/String indexOf (II)I 518 0 309293 0 512
ciMethod java/lang/String replace (Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String; 512 0 15064 0 -1
ciMethod java/lang/String <init> ([CIILjava/lang/Void;)V 706 0 8667 0 896
ciMethod java/lang/String isLatin1 ()Z 544 0 1992094 0 96
ciMethod java/lang/String checkBoundsOffCount (III)V 602 0 11572 0 160
ciMethod java/lang/System arraycopy (Ljava/lang/Object;ILjava/lang/Object;II)V 512 0 256 0 -1
ciMethod java/lang/StringBuilder <init> ()V 172 0 13852 0 -1
ciMethod java/lang/StringBuilder <init> (Ljava/lang/String;)V 628 0 7117 0 -1
ciMethod java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 392 0 61557 0 -1
ciMethod java/lang/StringBuilder append (I)Ljava/lang/StringBuilder; 54 0 1887 0 -1
ciMethod java/lang/StringBuilder toString ()Ljava/lang/String; 290 0 22025 0 -1
ciMethod java/util/List iterator ()Ljava/util/Iterator; 0 0 1 0 -1
ciMethod java/util/List add (Ljava/lang/Object;)Z 0 0 1 0 -1
ciMethod java/util/ArrayList <init> ()V 74 0 9237 0 96
ciMethod java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 564 0 25993 0 -1
ciMethod java/util/ArrayList add (Ljava/lang/Object;)Z 550 0 25993 0 1344
ciMethod java/util/ArrayList remove (I)Ljava/lang/Object; 512 0 1449 0 -1
ciMethod java/util/ArrayList iterator ()Ljava/util/Iterator; 516 0 5916 0 192
ciMethod java/util/AbstractList <init> ()V 126 0 26602 0 0
ciMethod java/util/AbstractCollection <init> ()V 520 0 50248 0 0
ciMethod java/lang/Character valueOf (C)Ljava/lang/Character; 72 0 36 0 -1
ciMethod java/lang/Float intBitsToFloat (I)F 12 0 6 0 -1
ciMethod java/lang/Number <init> ()V 786 0 17811 0 0
ciMethod java/lang/Double longBitsToDouble (J)D 422 0 211 0 -1
ciMethod java/lang/Byte valueOf (B)Ljava/lang/Byte; 106 0 53 0 -1
ciMethod java/lang/Short valueOf (S)Ljava/lang/Short; 104 0 52 0 -1
ciMethod java/lang/Integer valueOf (I)Ljava/lang/Integer; 514 0 28212 0 192
ciMethod java/lang/Integer <init> (I)V 712 0 15257 0 0
ciMethod java/util/Iterator hasNext ()Z 0 0 1 0 -1
ciMethod java/util/Iterator next ()Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/util/Iterator remove ()V 0 0 1 0 -1
ciMethod java/lang/NullPointerException <init> ()V 0 0 4 0 -1
ciMethod java/lang/StringLatin1 charAt ([BI)C 820 0 1575246 0 128
ciMethod java/lang/StringLatin1 canEncode (I)Z 546 0 175604 0 0
ciMethod java/lang/StringLatin1 equals ([B[B)Z 556 462 7155 0 -1
ciMethod java/lang/StringLatin1 hashCode ([B)I 224 4822 1205 0 288
ciMethod java/lang/StringLatin1 indexOf ([BII)I 518 0 6787 0 448
ciMethod java/lang/StringLatin1 indexOfChar ([BIII)I 276 4108 6749 0 -1
ciMethod java/lang/Math max (II)I 520 0 48881 0 -1
ciMethodData java/lang/Object <init> ()V 2 482205 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethod java/util/Collections unmodifiableList (Ljava/util/List;)Ljava/util/List; 516 0 1606 0 0
ciMethod java/lang/IllegalArgumentException <init> (Ljava/lang/String;)V 0 0 12 0 -1
ciMethodData java/lang/String isLatin1 ()Z 2 1992094 orig 80 1 0 0 0 2 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x30007 0x0 0x58 0x1e648d 0x80000006000a0007 0x7 0x38 0x1e648c 0xe0003 0x1e648d 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String hashCode ()I 2 10907 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 42 0x60007 0x23e9 0x108 0x5b1 0xd0007 0x3 0xe8 0x5ae 0x110005 0x5ae 0x0 0x0 0x0 0x0 0x0 0x140007 0x0 0x48 0x5ae 0x1b0002 0x5ae 0x1e0003 0x5ae 0x28 0x250002 0x0 0x2a0007 0x5ad 0x38 0x1 0x320003 0x1 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xe oops 0 methods 0
ciMethodData java/lang/StringLatin1 hashCode ([B)I 2 30238 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0xd0007 0x445 0x38 0x6cb3 0x250003 0x6cb3 0xffffffffffffffe0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String equals (Ljava/lang/Object;)Z 2 8562 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 49 0x20007 0x1c2f 0x20 0x41d 0x80104 0x0 0x0 0x1e035942bc0 0x1bf8 0x0 0x0 0xb0007 0x37 0xe0 0x1bf8 0xf0004 0x0 0x0 0x1e035942bc0 0x1bf8 0x0 0x0 0x160007 0x0 0x40 0x1bf8 0x210007 0x0 0x68 0x1bf8 0x2c0002 0x1bf8 0x2f0007 0x199e 0x38 0x25a 0x330003 0x25a 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 7 java/lang/String 18 java/lang/String methods 0
ciMethodData java/util/AbstractCollection <init> ()V 2 50250 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0xc346 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String charAt (I)C 2 1597711 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x10005 0x185fda 0x0 0x0 0x0 0x0 0x0 0x40007 0x1 0x30 0x185fda 0xc0002 0x185fda 0x150002 0x1 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 charAt ([BI)C 2 1575246 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x10007 0x0 0x40 0x1807b4 0x70007 0x1807b4 0x30 0x0 0xf0002 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethod java/lang/StringUTF16 getChar ([BI)C 512 0 10111 0 -1
ciMethod java/lang/StringUTF16 toBytes ([CII)[B 0 0 1 0 -1
ciMethod java/lang/StringUTF16 compress ([CII)[B 1020 0 8649 0 0
ciMethod java/lang/StringUTF16 compress ([CI[BII)I 296 8320 2421 0 -1
ciMethod java/lang/StringUTF16 hashCode ([B)I 0 0 1 0 -1
ciMethod java/lang/StringUTF16 indexOf ([BII)I 0 0 1 0 0
ciMethod java/lang/StringUTF16 indexOfChar ([BIII)I 2 0 1 0 -1
ciMethod java/lang/StringUTF16 indexOfSupplementary ([BIII)I 0 0 1 0 -1
ciMethod java/lang/StringUTF16 charAt ([BI)C 0 0 126 0 0
ciMethod java/lang/StringUTF16 checkIndex (I[B)V 0 0 177 0 -1
ciMethod java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 520 0 5920 0 0
ciMethod java/util/ArrayList$Itr hasNext ()Z 784 0 6620 0 96
ciMethod java/util/ArrayList$Itr next ()Ljava/lang/Object; 538 0 7186 0 256
ciMethod java/util/ArrayList$Itr remove ()V 14 0 7 0 0
ciMethod java/util/ArrayList$Itr checkForComodification ()V 538 0 7193 0 0
ciMethodData java/lang/StringLatin1 canEncode (I)Z 2 175604 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x8000000600040007 0x4 0x38 0x2ace1 0x80003 0x2ace1 0x18 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethod java/util/Collections$UnmodifiableCollection <init> (Ljava/util/Collection;)V 520 0 3975 0 -1
ciMethodData java/lang/StringUTF16 charAt ([BI)C 1 126 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x20002 0x7e 0x70002 0x7e 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String indexOf (II)I 2 309293 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 29 0x10005 0x4b72a 0x0 0x0 0x0 0x0 0x0 0x40007 0x1 0x48 0x4b72a 0xd0002 0x4b72a 0x100003 0x4b72a 0x28 0x190002 0x1 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 indexOf ([BII)I 2 6787 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x10002 0x1980 0x40007 0x1980 0x20 0x0 0xd0007 0x1980 0x38 0x0 0x120003 0x0 0x38 0x170007 0x195b 0x20 0x25 0x200002 0x195b 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringUTF16 indexOf ([BII)I 1 1 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x60007 0x1 0x38 0x0 0xb0003 0x0 0x38 0x100007 0x1 0x20 0x0 0x180007 0x0 0x30 0x1 0x1f0002 0x1 0x270002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethod lombok/patcher/TargetMatcher matches (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z 0 0 1 0 -1
ciMethod lombok/patcher/MethodTarget decomposeFullDesc (Ljava/lang/String;)Ljava/util/List; 120 214 50 0 0
ciMethod lombok/patcher/MethodTarget classMatches (Ljava/lang/String;)Z 26 0 38 0 0
ciMethod lombok/patcher/MethodTarget matches (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z 1024 0 5120 0 0
ciMethod lombok/patcher/MethodTarget descriptorMatch (Ljava/lang/String;)Z 22 16 36 0 0
ciMethod lombok/patcher/MethodTarget typeSpecMatch (Ljava/lang/String;Ljava/lang/String;)Z 160 108 63 0 -1
ciMethod lombok/patcher/MethodTarget typeMatches (Ljava/lang/String;Ljava/lang/String;)Z 570 0 39513 0 -1
ciMethod lombok/patcher/Hook getMethodName ()Ljava/lang/String; 1280 0 640 0 0
ciMethod lombok/patcher/Hook getMethodDescriptor ()Ljava/lang/String; 266 434 153 0 0
ciMethod lombok/patcher/Hook toSpec (Ljava/lang/String;)Ljava/lang/String; 778 34 401 0 -1
ciMethod org/lombokweb/asm/ClassVisitor visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 906 0 5449 0 0
ciMethod org/lombokweb/asm/ClassWriter visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 912 0 5454 0 0
ciMethod lombok/patcher/PatchScript$MethodPatcher visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 992 2048 5032 0 0
ciMethod lombok/patcher/PatchScript$MethodPatcherFactory createMethodVisitor (Ljava/lang/String;Ljava/lang/String;Lorg/lombokweb/asm/MethodVisitor;Llombok/patcher/MethodLogistics;)Lorg/lombokweb/asm/MethodVisitor; 0 0 1 0 -1
ciMethodData lombok/patcher/MethodTarget typeMatches (Ljava/lang/String;Ljava/lang/String;)Z 2 39516 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x60005 0x9940 0x0 0x0 0x0 0x0 0x0 0xa0005 0x9954 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0x0 oops 0 methods 0
ciMethodData java/lang/String checkBoundsOffCount (III)V 2 11572 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 76 0x10007 0x0 0x60 0x2c07 0x50007 0x0 0x40 0x2c07 0xc0007 0x2c07 0x1c8 0x0 0x170002 0x0 0x1d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x210005 0x0 0x0 0x0 0x0 0x0 0x0 0x270005 0x0 0x0 0x0 0x0 0x0 0x0 0x2b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x310005 0x0 0x0 0x0 0x0 0x0 0x0 0x350005 0x0 0x0 0x0 0x0 0x0 0x0 0x380005 0x0 0x0 0x0 0x0 0x0 0x0 0x3b0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String <init> ([CII)V 2 7336 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x70002 0x1c06 0xa0002 0x1c06 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethod java/util/Collections$UnmodifiableList <init> (Ljava/util/List;)V 526 0 3485 0 -1
ciMethod java/util/Collections$UnmodifiableRandomAccessList <init> (Ljava/util/List;)V 526 0 3482 0 -1
ciMethodData java/lang/String <init> ([CIILjava/lang/Void;)V 2 8667 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 31 0x10002 0x207a 0x50007 0x2069 0x20 0x11 0x1e0007 0x0 0x50 0x2069 0x240002 0x2069 0x2b0007 0x1 0x20 0x2068 0x430002 0x1 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0xc 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringUTF16 compress ([CII)[B 2 8649 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x90002 0x1fcb 0xd0007 0x1 0x20 0x1fca 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String rangeCheck ([CII)Ljava/lang/Void; 2 7336 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x40002 0x1aa9 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethod java/lang/IllegalStateException <init> ()V 0 0 1 0 -1
ciMethodData java/util/ArrayList <init> ()V 2 9237 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x23f0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/AbstractList <init> ()V 2 26602 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x67ab 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList add (Ljava/lang/Object;)Z 2 26050 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x140005 0x64af 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xe 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr hasNext ()Z 2 6620 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0xb0007 0x539 0x38 0x131b 0xf0003 0x131b 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList iterator ()Ljava/util/Iterator; 2 5916 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x50002 0x161a 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 2 5920 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x60002 0x161c 0x0 0x0 0x0 0x0 0x9 0x2 0xc 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr next ()Ljava/lang/Object; 2 7186 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 28 0x10005 0x1b05 0x0 0x0 0x0 0x0 0x0 0x110007 0x1b05 0x30 0x0 0x180002 0x0 0x270007 0x1b05 0x30 0x0 0x2e0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x6 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr checkForComodification ()V 2 7193 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0xb0007 0x1b0c 0x30 0x0 0x120002 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/Collections$UnmodifiableCollection <init> (Ljava/util/Collection;)V 2 3975 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x10002 0xe83 0x50007 0xe83 0x30 0x0 0xc0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x6 0x0 oops 0 methods 0
ciMethodData java/util/Collections unmodifiableList (Ljava/util/List;)Ljava/util/List; 2 1612 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 49 0x10005 0x54a 0x0 0x0 0x0 0x0 0x0 0x60007 0x0 0x78 0x54a 0xa0005 0x54a 0x0 0x0 0x0 0x0 0x0 0xf0007 0x515 0x20 0x35 0x150004 0xfffffffffffffffd 0x0 0x1e035945ed0 0x500 0x1e03850e160 0xe 0x180007 0x3 0x48 0x512 0x200002 0x512 0x230003 0x512 0x28 0x2b0002 0x3 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 2 25 java/util/ArrayList 27 java/util/Arrays$ArrayList methods 0
ciMethodData java/util/Collections$UnmodifiableList <init> (Ljava/util/List;)V 2 3491 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x20002 0xc9c 0x0 0x0 0x0 0x0 0x9 0x2 0x6 0x0 oops 0 methods 0
ciMethodData java/lang/String replace (Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String; 2 15064 orig 80 2 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 178 0x10005 0x0 0x0 0x1e035942bc0 0x39d8 0x0 0x0 0x80005 0x0 0x0 0x1e035942bc0 0x39d8 0x0 0x0 0x100005 0x39d8 0x0 0x0 0x0 0x0 0x0 0x160005 0x39d8 0x0 0x0 0x0 0x0 0x0 0x1d0005 0x39d8 0x0 0x0 0x0 0x0 0x0 0x240007 0x0 0x268 0x39d8 0x80000006002a0007 0x1 0xe8 0x39d8 0x8000000600300007 0x5 0xc8 0x39d4 0x360005 0x39d4 0x0 0x0 0x0 0x0 0x0 0x3c0005 0x39d4 0x0 0x0 0x0 0x0 0x0 0x3f0005 0x39d4 0x0 0x0 0x0 0x0 0x0 0x440005 0x6 0x0 0x0 0x0 0x0 0x0 0x4a0005 0x6 0x0 0x0 0x0 0x0 0x0 0x510005 0x6 0x0 0x0 0x0 0x0 0x0 0x580007 0x0 0x88 0x6 0x5d0007 0x0 0x68 0x6 0x620007 0x0 0x48 0x6 0x780002 0x6 0x7b0003 0x6 0x28 0x970002 0x0 0x9e0007 0x6 0x20 0x0 0xab0002 0x0 0xb00002 0x0 0xb30002 0x0 0xb80003 0x0 0x28 0xc40002 0x0 0xce0002 0x0 0xd70005 0x0 0x0 0x0 0x0 0x0 0x0 0xe20007 0x0 0xe0 0x0 0xea0005 0x0 0x0 0x0 0x0 0x0 0x0 0xed0005 0x0 0x0 0x0 0x0 0x0 0x0 0xf20005 0x0 0x0 0x0 0x0 0x0 0x0 0xf90003 0x0 0xffffffffffffff38 0xfe0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 2 3 java/lang/String 10 java/lang/String methods 0
ciMethod java/util/regex/Pattern matcher (Ljava/lang/CharSequence;)Ljava/util/regex/Matcher; 314 0 404 0 -1
ciMethod java/util/regex/Pattern compile ()V 34 812 17 0 -1
ciMethod java/util/regex/Matcher <init> (Ljava/util/regex/Pattern;Ljava/lang/CharSequence;)V 556 0 415 0 -1
ciMethod java/util/regex/Matcher reset ()Ljava/util/regex/Matcher; 316 6404 438 0 -1
ciMethod java/util/regex/Matcher group (I)Ljava/lang/String; 470 0 193 0 -1
ciMethod java/util/regex/Matcher matches ()Z 268 0 320 0 -1
ciMethod java/util/regex/Matcher find ()Z 420 0 178 0 -1
ciMethod java/util/regex/Matcher getTextLength ()I 316 0 460 0 -1
ciMethod java/util/regex/IntHashSet clear ()V 0 0 1 0 -1
ciMethodData java/lang/Number <init> ()V 2 17811 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x440a 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethod org/lombokweb/asm/ClassReader readTypeAnnotations (Lorg/lombokweb/asm/MethodVisitor;Lorg/lombokweb/asm/Context;IZ)[I 0 0 1 0 -1
ciMethod org/lombokweb/asm/ClassReader getTypeAnnotationBytecodeOffset ([II)I 96 0 40 0 -1
ciMethod org/lombokweb/asm/ClassReader readMethod (Lorg/lombokweb/asm/ClassVisitor;Lorg/lombokweb/asm/Context;I)I 702 1166 5504 0 -1
ciMethod org/lombokweb/asm/ClassReader readCode (Lorg/lombokweb/asm/MethodVisitor;Lorg/lombokweb/asm/Context;I)V 12 5296 20 0 0
ciMethod org/lombokweb/asm/ClassReader readBytecodeInstructionOffset (I)V 1024 0 1704 0 -1
ciMethod org/lombokweb/asm/ClassReader createLabel (I[Lorg/lombokweb/asm/Label;)Lorg/lombokweb/asm/Label; 556 0 457 0 -1
ciMethod org/lombokweb/asm/ClassReader createDebugLabel (I[Lorg/lombokweb/asm/Label;)V 786 0 725 0 -1
ciMethod org/lombokweb/asm/ClassReader readTypeAnnotationTarget (Lorg/lombokweb/asm/Context;I)I 0 0 1 0 0
ciMethod org/lombokweb/asm/ClassReader readParameterAnnotations (Lorg/lombokweb/asm/MethodVisitor;Lorg/lombokweb/asm/Context;IZ)V 0 0 1 0 0
ciMethod org/lombokweb/asm/ClassReader readElementValues (Lorg/lombokweb/asm/AnnotationVisitor;IZ[C)I 0 0 1 0 0
ciMethod org/lombokweb/asm/ClassReader readElementValue (Lorg/lombokweb/asm/AnnotationVisitor;ILjava/lang/String;[C)I 0 0 1 0 0
ciMethod org/lombokweb/asm/ClassReader computeImplicitFrame (Lorg/lombokweb/asm/Context;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/ClassReader readStackMapFrame (IZZLorg/lombokweb/asm/Context;)I 456 1892 155 0 -1
ciMethod org/lombokweb/asm/ClassReader readAttribute ([Lorg/lombokweb/asm/Attribute;Ljava/lang/String;II[CI[Lorg/lombokweb/asm/Label;)Lorg/lombokweb/asm/Attribute; 0 0 1 0 0
ciMethod org/lombokweb/asm/ClassReader readByte (I)I 36 0 876 0 0
ciMethod org/lombokweb/asm/ClassReader readUnsignedShort (I)I 536 0 9937 0 160
ciMethod org/lombokweb/asm/ClassReader readShort (I)S 512 0 453 0 -1
ciMethod org/lombokweb/asm/ClassReader readInt (I)I 1024 0 2268 0 192
ciMethod org/lombokweb/asm/ClassReader readLong (I)J 46 0 282 0 -1
ciMethod org/lombokweb/asm/ClassReader readUTF8 (I[C)Ljava/lang/String; 860 0 41929 0 2464
ciMethod org/lombokweb/asm/ClassReader readUtf (I[C)Ljava/lang/String; 724 0 12475 0 2336
ciMethod org/lombokweb/asm/ClassReader readUtf (II[C)Ljava/lang/String; 400 10240 1609 0 1760
ciMethod org/lombokweb/asm/ClassReader readStringish (I[C)Ljava/lang/String; 1024 0 14669 0 -1
ciMethod org/lombokweb/asm/ClassReader readClass (I[C)Ljava/lang/String; 1024 0 14669 0 2560
ciMethod org/lombokweb/asm/ClassReader readConst (I[C)Ljava/lang/Object; 560 0 1994 0 -1
ciMethodData org/lombokweb/asm/ClassReader readUnsignedShort (I)I 2 9937 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethod org/lombokweb/asm/AnnotationVisitor visit (Ljava/lang/String;Ljava/lang/Object;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/AnnotationVisitor visitEnum (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/AnnotationVisitor visitAnnotation (Ljava/lang/String;Ljava/lang/String;)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/AnnotationVisitor visitArray (Ljava/lang/String;)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/AnnotationVisitor visitEnd ()V 0 0 1 0 -1
ciMethod org/lombokweb/asm/AnnotationWriter visitEnd ()V 0 0 1 0 0
ciMethod org/lombokweb/asm/MethodVisitor <init> (I)V 912 0 5454 0 0
ciMethod org/lombokweb/asm/MethodVisitor <init> (ILorg/lombokweb/asm/MethodVisitor;)V 918 0 5469 0 0
ciMethod org/lombokweb/asm/MethodVisitor visitParameter (Ljava/lang/String;I)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitAnnotationDefault ()Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitAnnotation (Ljava/lang/String;Z)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitTypeAnnotation (ILorg/lombokweb/asm/TypePath;Ljava/lang/String;Z)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitAnnotableParameterCount (IZ)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitParameterAnnotation (ILjava/lang/String;Z)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitAttribute (Lorg/lombokweb/asm/Attribute;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitCode ()V 38 0 15 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitFrame (II[Ljava/lang/Object;I[Ljava/lang/Object;)V 372 0 113 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitInsn (I)V 836 0 316 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitIntInsn (II)V 70 0 16 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitVarInsn (II)V 1024 0 582 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitTypeInsn (ILjava/lang/String;)V 180 0 34 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitFieldInsn (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V 828 0 421 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitMethodInsn (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V 626 0 181 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitInvokeDynamicInsn (Ljava/lang/String;Ljava/lang/String;Lorg/lombokweb/asm/Handle;[Ljava/lang/Object;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitJumpInsn (ILorg/lombokweb/asm/Label;)V 536 0 170 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitLabel (Lorg/lombokweb/asm/Label;)V 580 0 451 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitLdcInsn (Ljava/lang/Object;)V 60 0 17 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitIincInsn (II)V 8 0 3 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitTableSwitchInsn (IILorg/lombokweb/asm/Label;[Lorg/lombokweb/asm/Label;)V 4 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitLookupSwitchInsn (Lorg/lombokweb/asm/Label;[I[Lorg/lombokweb/asm/Label;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitMultiANewArrayInsn (Ljava/lang/String;I)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitInsnAnnotation (ILorg/lombokweb/asm/TypePath;Ljava/lang/String;Z)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitTryCatchBlock (Lorg/lombokweb/asm/Label;Lorg/lombokweb/asm/Label;Lorg/lombokweb/asm/Label;Ljava/lang/String;)V 22 0 7 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitLocalVariable (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/lombokweb/asm/Label;Lorg/lombokweb/asm/Label;I)V 278 0 65 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitLocalVariableAnnotation (ILorg/lombokweb/asm/TypePath;[Lorg/lombokweb/asm/Label;[Lorg/lombokweb/asm/Label;[ILjava/lang/String;Z)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitMaxs (II)V 38 0 15 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitEnd ()V 38 0 15 0 -1
ciMethod org/lombokweb/asm/MethodWriter <init> (Lorg/lombokweb/asm/SymbolTable;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;I)V 912 296 5454 0 0
ciMethod org/lombokweb/asm/MethodWriter visitLabel (Lorg/lombokweb/asm/Label;)V 488 0 5623 0 -1
ciMethod org/lombokweb/asm/MethodWriter addSuccessorToCurrentBasicBlock (ILorg/lombokweb/asm/Label;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodWriter canCopyMethodAttributes (Lorg/lombokweb/asm/ClassReader;ZZIII)Z 872 284 5439 0 0
ciMethod org/lombokweb/asm/MethodWriter setMethodAttributesSource (II)V 868 0 5434 0 0
ciMethod org/lombokweb/asm/SymbolTable getSource ()Lorg/lombokweb/asm/ClassReader; 584 0 292 0 0
ciMethod org/lombokweb/asm/SymbolTable getMajorVersion ()I 716 0 358 0 0
ciMethod org/lombokweb/asm/SymbolTable get (I)Lorg/lombokweb/asm/SymbolTable$Entry; 568 0 16987 0 160
ciMethod org/lombokweb/asm/SymbolTable put (Lorg/lombokweb/asm/SymbolTable$Entry;)Lorg/lombokweb/asm/SymbolTable$Entry; 442 0 330 0 0
ciMethod org/lombokweb/asm/SymbolTable addConstantClass (Ljava/lang/String;)Lorg/lombokweb/asm/Symbol; 862 0 1085 0 0
ciMethod org/lombokweb/asm/SymbolTable addConstantUtf8 (Ljava/lang/String;)I 474 204 14739 0 1312
ciMethod org/lombokweb/asm/SymbolTable addConstantUtf8Reference (ILjava/lang/String;)Lorg/lombokweb/asm/Symbol; 808 358 1177 0 0
ciMethod org/lombokweb/asm/SymbolTable hash (ILjava/lang/String;)I 816 0 30010 0 0
ciMethod org/lombokweb/asm/Symbol <init> (IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V 932 0 10729 0 128
ciMethod org/lombokweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;I)V 810 0 6903 0 128
ciMethod org/lombokweb/asm/ByteVector <init> ()V 576 0 5548 0 0
ciMethod org/lombokweb/asm/ByteVector putByte (I)Lorg/lombokweb/asm/ByteVector; 1024 0 2187 0 0
ciMethod org/lombokweb/asm/ByteVector put12 (II)Lorg/lombokweb/asm/ByteVector; 424 0 899 0 -1
ciMethod org/lombokweb/asm/ByteVector putUTF8 (Ljava/lang/String;)Lorg/lombokweb/asm/ByteVector; 238 4430 174 0 0
ciMethod org/lombokweb/asm/ByteVector encodeUtf8 (Ljava/lang/String;II)Lorg/lombokweb/asm/ByteVector; 0 0 1 0 -1
ciMethod org/lombokweb/asm/ByteVector enlarge (I)V 22 0 88 0 -1
ciMethodData org/lombokweb/asm/ClassReader readUtf (I[C)Ljava/lang/String; 2 12475 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 31 0x80007 0xd6f 0x20 0x21e2 0x220005 0x0 0x0 0x1e037f4c9b0 0xd6f 0x0 0x0 0x260002 0xd6f 0x2a0004 0x0 0x0 0x1e035942bc0 0xd6f 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 2 7 org/lombokweb/asm/ClassReader 16 java/lang/String methods 0
ciMethodData org/lombokweb/asm/ClassReader readUtf (II[C)Ljava/lang/String; 2 36521 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 33 0x160007 0x582 0xa8 0x7aa9 0x290007 0x0 0x38 0x7aa9 0x390003 0x7aa9 0x50 0x450007 0x0 0x38 0x0 0x640003 0x0 0x18 0x920003 0x7aa9 0xffffffffffffff70 0x9d0002 0x582 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/Symbol <init> (IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V 2 10729 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10002 0x2817 0x0 0x0 0x0 0x0 0x9 0x8 0x3e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readUTF8 (I[C)Ljava/lang/String; 2 41929 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 33 0x20005 0x0 0x0 0x1e037f4c9b0 0xa21b 0x0 0x0 0x70007 0x2a 0x40 0xa1f1 0xb0007 0xa1eb 0x20 0x6 0x130005 0xa1eb 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 1 3 org/lombokweb/asm/ClassReader methods 0
ciMethod org/lombokweb/asm/Attribute <init> (Ljava/lang/String;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/Attribute read (Lorg/lombokweb/asm/ClassReader;II[CI[Lorg/lombokweb/asm/Label;)Lorg/lombokweb/asm/Attribute; 0 0 1 0 -1
ciMethod org/lombokweb/asm/Type getType (Ljava/lang/String;)Lorg/lombokweb/asm/Type; 0 0 1 0 -1
ciMethod org/lombokweb/asm/Type getArgumentsAndReturnSizes (Ljava/lang/String;)I 318 334 5219 0 0
ciMethod org/lombokweb/asm/Label <init> ()V 490 0 5624 0 0
ciMethod org/lombokweb/asm/Label addLineNumber (I)V 546 0 503 0 -1
ciMethod org/lombokweb/asm/Label accept (Lorg/lombokweb/asm/MethodVisitor;Z)V 792 0 560 0 -1
ciMethod org/lombokweb/asm/Label resolve ([BLorg/lombokweb/asm/ByteVector;I)Z 1024 74 5623 0 -1
ciMethod lombok/patcher/MethodLogistics <init> (ILjava/lang/String;)V 8 10 15 0 0
ciMethod lombok/patcher/MethodLogistics loadOpcodeFor (Ljava/lang/String;)I 50 0 17 0 0
ciMethod lombok/patcher/MethodLogistics returnOpcodeFor (Ljava/lang/String;)I 38 0 15 0 0
ciMethod lombok/patcher/MethodLogistics sizeOf (Ljava/lang/String;)I 88 0 32 0 0
ciMethodData org/lombokweb/asm/SymbolTable hash (ILjava/lang/String;)I 2 30010 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x40005 0x73a2 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0xe oops 0 methods 0
ciMethodData org/lombokweb/asm/SymbolTable get (I)Lorg/lombokweb/asm/SymbolTable$Entry; 2 16987 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;I)V 2 6903 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x70002 0x1962 0x0 0x0 0x0 0x0 0x9 0x5 0x7e 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ByteVector putByte (I)Lorg/lombokweb/asm/ByteVector; 2 2239 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0xd0007 0x6a3 0x30 0x1d 0x120002 0x1d 0x0 0x0 0x0 0x0 0x9 0x2 0x6 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/SymbolTable addConstantUtf8Reference (ILjava/lang/String;)Lorg/lombokweb/asm/Symbol; 1 1218 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 59 0x20002 0x32e 0x80002 0x32e 0xf0007 0x15 0xd0 0x43e 0x180007 0x11b 0x98 0x323 0x210007 0xa 0x78 0x319 0x2a0005 0x319 0x0 0x0 0x0 0x0 0x0 0x2d0007 0x0 0x20 0x319 0x3a0003 0x125 0xffffffffffffff48 0x440005 0x15 0x0 0x0 0x0 0x0 0x0 0x470005 0x15 0x0 0x0 0x0 0x0 0x0 0x5e0002 0x15 0x610002 0x15 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x4c 0x0 0xffffffffffffffff oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readClass (I[C)Ljava/lang/String; 2 14675 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x30002 0x3754 0x0 0x0 0x9 0x3 0xffffffffffffffff 0x0 0xffffffffffffffff oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readStringish (I[C)Ljava/lang/String; 2 14675 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x70005 0x0 0x0 0x1e037f4c9b0 0x3754 0x0 0x0 0xc0005 0x0 0x0 0x1e037f4c9b0 0x3754 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 2 3 org/lombokweb/asm/ClassReader 10 org/lombokweb/asm/ClassReader methods 0
ciMethodData org/lombokweb/asm/SymbolTable addConstantUtf8 (Ljava/lang/String;)I 2 14739 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 58 0x20002 0x38a6 0x80002 0x38a6 0xd0007 0x35 0xd0 0x4f6d 0x150007 0xecf 0x98 0x409e 0x1d0007 0x82d 0x78 0x3871 0x250005 0x3871 0x0 0x0 0x0 0x0 0x0 0x280007 0x0 0x20 0x3871 0x350003 0x16fc 0xffffffffffffff48 0x3d0005 0x0 0x0 0x1e0371749b0 0x35 0x0 0x0 0x410005 0x0 0x0 0x1e0371749b0 0x35 0x0 0x0 0x580002 0x35 0x5b0002 0x35 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x4c 0xffffffffffffffff oops 2 33 org/lombokweb/asm/ByteVector 40 org/lombokweb/asm/ByteVector methods 0
ciMethodData org/lombokweb/asm/SymbolTable put (Lorg/lombokweb/asm/SymbolTable$Entry;)Lorg/lombokweb/asm/SymbolTable$Entry; 1 343 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 46 0xd0007 0x7a 0xc8 0x0 0x290007 0x0 0xa8 0x0 0x370007 0x0 0x70 0x0 0x5a0004 0x0 0x0 0x0 0x0 0x0 0x0 0x5f0003 0x0 0xffffffffffffffa8 0x650003 0x0 0xffffffffffffff70 0x940004 0x0 0x0 0x1e038516870 0x7a 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x44 0x40 oops 1 28 org/lombokweb/asm/SymbolTable$Entry methods 0
ciMethodData org/lombokweb/asm/ByteVector putUTF8 (Ljava/lang/String;)Lorg/lombokweb/asm/ByteVector; 1 4043 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 62 0x10005 0x3e 0x0 0x0 0x0 0x0 0x0 0x80007 0x3e 0x30 0x0 0x110002 0x0 0x240007 0x3e 0x30 0x0 0x2b0002 0x0 0x4f0007 0x3e 0x100 0x724 0x550005 0x724 0x0 0x0 0x0 0x0 0x0 0x5d0007 0x0 0x58 0x724 0x640007 0x0 0x38 0x724 0x710003 0x724 0x50 0x7f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x860003 0x724 0xffffffffffffff18 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0xffffffffffffffff oops 0 methods 0
ciMethodData org/lombokweb/asm/SymbolTable addConstantClass (Ljava/lang/String;)Lorg/lombokweb/asm/Symbol; 1 1085 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x40002 0x28e 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethod java/util/ConcurrentModificationException <init> ()V 0 0 1 0 -1
ciMethodData java/lang/Integer valueOf (I)Ljava/lang/Integer; 2 28432 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x30007 0x3 0x40 0x6df8 0xa0007 0x3a2c 0x20 0x33cc 0x1c0002 0x3a2f 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/Integer <init> (I)V 2 15257 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x10002 0x3a35 0x0 0x0 0x0 0x0 0x9 0x2 0x6 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern matcher (Ljava/lang/CharSequence;)Ljava/util/regex/Matcher; 1 415 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x40007 0x102 0x90 0x0 0xf0007 0x0 0x58 0x0 0x130005 0x0 0x0 0x0 0x0 0x0 0x0 0x180003 0x0 0x18 0x260002 0x102 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/regex/Matcher reset ()Ljava/util/regex/Matcher; 2 9411 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 53 0x170007 0x118 0x38 0x15e0 0x240003 0x15e0 0xffffffffffffffe0 0x2f0007 0x118 0x38 0x22d 0x3c0003 0x22d 0xffffffffffffffe0 0x470007 0x118 0x90 0x34 0x500007 0x34 0x58 0x0 0x590005 0x0 0x0 0x0 0x0 0x0 0x0 0x5f0003 0x34 0xffffffffffffff88 0x6e0005 0x118 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x7e oops 0 methods 0
ciMethodData java/util/regex/Matcher getTextLength ()I 1 460 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40005 0x0 0x0 0x1e035942bc0 0x12e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/lang/String methods 0
ciMethodData org/lombokweb/asm/ClassReader readInt (I)I 2 2268 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ByteVector <init> ()V 2 5548 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x148c 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readMethod (Lorg/lombokweb/asm/ClassVisitor;Lorg/lombokweb/asm/Context;I)I 2 6009 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 628 0xd0005 0x0 0x0 0x1e037f4c9b0 0x1421 0x0 0x0 0x1b0005 0x0 0x0 0x1e037f4c9b0 0x1421 0x0 0x0 0x290005 0x0 0x0 0x1e037f4c9b0 0x1421 0x0 0x0 0x5f0005 0x0 0x0 0x1e037f4c9b0 0x1421 0x0 0x0 0x6c0007 0x1421 0x7a0 0x14b8 0x740005 0x0 0x0 0x1e037f4c9b0 0x14b8 0x0 0x0 0x7e0005 0x0 0x0 0x1e037f4c9b0 0x14b8 0x0 0x0 0x8b0005 0x14b8 0x0 0x0 0x0 0x0 0x0 0x8e0007 0x98 0x58 0x1420 0x970007 0x0 0x6a0 0x1420 0x9e0003 0x1420 0x680 0xa60005 0x98 0x0 0x0 0x0 0x0 0x0 0xa90007 0x1e 0x118 0x7a 0xb30005 0x0 0x0 0x1e037f4c9b0 0x7a 0x0 0x0 0xc90007 0x7a 0xa8 0x7a 0xd50005 0x0 0x0 0x1e037f4c9b0 0x7a 0x0 0x0 0xd80004 0x0 0x0 0x1e035942bc0 0x7a 0x0 0x0 0xdf0003 0x7a 0xffffffffffffff70 0xe20003 0x7a 0x530 0xe90005 0x1e 0x0 0x0 0x0 0x0 0x0 0xec0007 0xe 0x70 0x10 0xf20005 0x0 0x0 0x1e037f4c9b0 0x10 0x0 0x0 0xf70003 0x10 0x488 0xfe0005 0xe 0x0 0x0 0x0 0x0 0x0 0x1010007 0x0 0x38 0xe 0x1100003 0xe 0x418 0x1170005 0x0 0x0 0x0 0x0 0x0 0x0 0x11a0007 0x0 0x38 0x0 0x1210003 0x0 0x3a8 0x1280005 0x0 0x0 0x0 0x0 0x0 0x0 0x12b0007 0x0 0x38 0x0 0x1320003 0x0 0x338 0x13a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x13d0007 0x0 0x38 0x0 0x1440003 0x0 0x2c8 0x14c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x14f0007 0x0 0x38 0x0 0x1610003 0x0 0x258 0x1690005 0x0 0x0 0x0 0x0 0x0 0x0 0x16c0007 0x0 0x38 0x0 0x1730003 0x0 0x1e8 0x17b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x17e0007 0x0 0x38 0x0 0x1850003 0x0 0x178 0x18d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1900007 0x0 0x38 0x0 0x1970003 0x0 0x108 0x19f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1a20007 0x0 0x38 0x0 0x1a90003 0x0 0x98 0x1b10005 0x0 0x0 0x0 0x0 0x0 0x0 0x1b40007 0x0 0x38 0x0 0x1bb0003 0x0 0x28 0x1cd0002 0x0 0x1e40003 0x14b8 0xfffffffffffff878 0x1f60007 0x10 0x38 0x1411 0x1fa0003 0x1411 0x50 0x2020005 0x10 0x0 0x0 0x0 0x0 0x0 0x2070005 0x2b 0x0 0x1e037f4e9c0 0xc0 0x1e037f4ea70 0x1336 0x20e0007 0x13f8 0x20 0x29 0x2160004 0xfffffffffffffff4 0x0 0x1e037f4eb20 0x13ec 0x0 0x0 0x2190007 0xc 0x158 0x13ec 0x21e0004 0x0 0x0 0x1e037f4eb20 0x13ec 0x0 0x0 0x2300007 0x13de 0x38 0xe 0x2340003 0xe 0x18 0x23c0005 0x0 0x0 0x1e037f4c9b0 0x13ec 0x0 0x0 0x2430005 0x13ec 0x0 0x0 0x0 0x0 0x0 0x2460007 0x2 0x58 0x13ea 0x2500005 0x13ea 0x0 0x0 0x0 0x0 0x0 0x2580007 0xe 0x158 0x0 0x2610007 0x0 0x138 0x0 0x2670005 0x0 0x0 0x0 0x0 0x0 0x0 0x2770007 0x0 0xe0 0x0 0x2810005 0x0 0x0 0x0 0x0 0x0 0x0 0x2890005 0x0 0x0 0x0 0x0 0x0 0x0 0x28c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2920003 0x0 0xffffffffffffff38 0x2970007 0xe 0xc0 0x0 0x29c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2a90002 0x0 0x2af0007 0x0 0x58 0x0 0x2b40005 0x0 0x0 0x0 0x0 0x0 0x0 0x2b90007 0xe 0x110 0x0 0x2bf0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2cf0007 0x0 0xb8 0x0 0x2d70005 0x0 0x0 0x0 0x0 0x0 0x0 0x2e50005 0x0 0x0 0x0 0x0 0x0 0x0 0x2ed0002 0x0 0x2f20003 0x0 0xffffffffffffff60 0x2f70007 0xe 0x110 0x0 0x2fd0005 0x0 0x0 0x0 0x0 0x0 0x0 0x30d0007 0x0 0xb8 0x0 0x3150005 0x0 0x0 0x0 0x0 0x0 0x0 0x3230005 0x0 0x0 0x0 0x0 0x0 0x0 0x32b0002 0x0 0x3300003 0x0 0xffffffffffffff60 0x3350007 0xe 0x120 0x0 0x33b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x34b0007 0x0 0xc8 0x0 0x3520002 0x0 0x35c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3720005 0x0 0x0 0x0 0x0 0x0 0x0 0x37a0002 0x0 0x37f0003 0x0 0xffffffffffffff50 0x3840007 0xe 0x120 0x0 0x38a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x39a0007 0x0 0xc8 0x0 0x3a10002 0x0 0x3ab0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3c10005 0x0 0x0 0x0 0x0 0x0 0x0 0x3c90002 0x0 0x3ce0003 0x0 0xffffffffffffff50 0x3d30007 0xe 0x30 0x0 0x3dd0002 0x0 0x3e20007 0xe 0x30 0x0 0x3ec0002 0x0 0x3f10007 0xe 0x70 0x0 0x4050005 0x0 0x0 0x0 0x0 0x0 0x0 0x40c0003 0x0 0xffffffffffffffa8 0x4110007 0x0 0x68 0xe 0x4160005 0x7 0x0 0x1e037f4ebd0 0x5 0x1e037f4eb20 0x2 0x41f0002 0xe 0x4240005 0x7 0x0 0x1e037f4ebd0 0x5 0x1e037f4eb20 0x2 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 19 3 org/lombokweb/asm/ClassReader 10 org/lombokweb/asm/ClassReader 17 org/lombokweb/asm/ClassReader 24 org/lombokweb/asm/ClassReader 35 org/lombokweb/asm/ClassReader 42 org/lombokweb/asm/ClassReader 78 org/lombokweb/asm/ClassReader 89 org/lombokweb/asm/ClassReader 96 java/lang/String 120 org/lombokweb/asm/ClassReader 289 lombok/patcher/scripts/AddFieldScript$1 291 lombok/patcher/PatchScript$MethodPatcher 300 org/lombokweb/asm/MethodWriter 311 org/lombokweb/asm/MethodWriter 325 org/lombokweb/asm/ClassReader 587 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 589 org/lombokweb/asm/MethodWriter 596 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 598 org/lombokweb/asm/MethodWriter methods 0
ciMethodData org/lombokweb/asm/ClassReader readAttribute ([Lorg/lombokweb/asm/Attribute;Ljava/lang/String;II[CI[Lorg/lombokweb/asm/Label;)Lorg/lombokweb/asm/Attribute; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 50 0xf0007 0x0 0xc8 0x0 0x1f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x220007 0x0 0x58 0x0 0x310005 0x0 0x0 0x0 0x0 0x0 0x0 0x380003 0x0 0xffffffffffffff50 0x400002 0x0 0x4a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x8 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/MethodWriter canCopyMethodAttributes (Lorg/lombokweb/asm/ClassReader;ZZIII)Z 2 5439 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 106 0x50005 0x138b 0x0 0x0 0x0 0x0 0x0 0x80007 0x2 0xb8 0x1389 0x110007 0x0 0x98 0x1389 0x1a0007 0x0 0x78 0x1389 0x260007 0x1389 0x38 0x0 0x2a0003 0x0 0x18 0x2e0007 0x1389 0x20 0x0 0x370005 0x1389 0x0 0x0 0x0 0x0 0x0 0x3c0007 0x1389 0x58 0x0 0x470007 0x0 0x38 0x0 0x4b0003 0x0 0x18 0x540007 0x1389 0x20 0x0 0x5b0007 0x3a 0x40 0x134f 0x620007 0x134f 0x108 0x0 0x6a0005 0x0 0x0 0x1e037f4c9b0 0x3a 0x0 0x0 0x710007 0x0 0xb0 0x3a 0x830007 0x3a 0x90 0x3a 0x890005 0x0 0x0 0x1e037f4c9b0 0x3a 0x0 0x0 0x930007 0x3a 0x20 0x0 0x9e0003 0x3a 0xffffffffffffff88 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x7 0x0 0x0 0x0 0x0 0x0 0x0 0x0 oops 2 63 org/lombokweb/asm/ClassReader 78 org/lombokweb/asm/ClassReader methods 0
ciMethodData org/lombokweb/asm/MethodWriter setMethodAttributesSource (II)V 2 5434 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readByte (I)I 1 885 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/AnnotationWriter visitEnd ()V 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x50007 0x0 0x20 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readElementValues (Lorg/lombokweb/asm/AnnotationVisitor;IZ[C)I 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 60 0x60005 0x0 0x0 0x0 0x0 0x0 0x0 0xf0007 0x0 0xa0 0x0 0x170007 0x0 0xc8 0x0 0x1f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2e0002 0x0 0x330003 0x0 0xffffffffffffff98 0x3b0007 0x0 0x48 0x0 0x450002 0x0 0x4a0003 0x0 0xffffffffffffffd0 0x4e0007 0x0 0x58 0x0 0x520005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/util/regex/Matcher <init> (Ljava/util/regex/Pattern;Ljava/lang/CharSequence;)V 1 415 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x10002 0x89 0x370002 0x89 0x5a0005 0x89 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x3fe 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readElementValue (Lorg/lombokweb/asm/AnnotationVisitor;ILjava/lang/String;[C)I 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 724 0x40007 0x0 0x90 0x0 0x120008 0x8 0x0 0x70 0x0 0x50 0x0 0x60 0x0 0x50 0x420002 0x0 0x4f0002 0x0 0x660008 0x6a 0x0 0x14c8 0x0 0x968 0x0 0x14c8 0x0 0x360 0x0 0x430 0x0 0x500 0x0 0x14c8 0x0 0x500 0x0 0x14c8 0x0 0x14c8 0x0 0x500 0x0 0x500 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x5c0 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x690 0x0 0xa00 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x8d0 0x0 0x14c8 0x0 0x810 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x14c8 0x0 0x788 0x14e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1520005 0x0 0x0 0x0 0x0 0x0 0x0 0x1560002 0x0 0x1590005 0x0 0x0 0x0 0x0 0x0 0x0 0x15f0003 0x0 0x10c0 0x16c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1700005 0x0 0x0 0x0 0x0 0x0 0x0 0x1740002 0x0 0x1770005 0x0 0x0 0x0 0x0 0x0 0x0 0x17d0003 0x0 0xff0 0x1860005 0x0 0x0 0x0 0x0 0x0 0x0 0x18b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x18e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1940003 0x0 0xf30 0x1a10005 0x0 0x0 0x0 0x0 0x0 0x0 0x1a50005 0x0 0x0 0x0 0x0 0x0 0x0 0x1a90002 0x0 0x1ac0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1b20003 0x0 0xe60 0x1bf0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1c30005 0x0 0x0 0x0 0x0 0x0 0x0 0x1c60007 0x0 0x38 0x0 0x1cc0003 0x0 0x18 0x1d20005 0x0 0x0 0x0 0x0 0x0 0x0 0x1d80003 0x0 0xd68 0x1e20005 0x0 0x0 0x0 0x0 0x0 0x0 0x1e50005 0x0 0x0 0x0 0x0 0x0 0x0 0x1eb0003 0x0 0xce0 0x1f50005 0x0 0x0 0x0 0x0 0x0 0x0 0x1ff0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2020005 0x0 0x0 0x0 0x0 0x0 0x0 0x2080003 0x0 0xc20 0x2120005 0x0 0x0 0x0 0x0 0x0 0x0 0x2150002 0x0 0x2180005 0x0 0x0 0x0 0x0 0x0 0x0 0x21e0003 0x0 0xb88 0x2290005 0x0 0x0 0x0 0x0 0x0 0x0 0x22c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2360002 0x0 0x23b0003 0x0 0xaf0 0x2410005 0x0 0x0 0x0 0x0 0x0 0x0 0x24b0007 0x0 0x68 0x0 0x2510005 0x0 0x0 0x0 0x0 0x0 0x0 0x25b0002 0x0 0x26a0008 0x34 0x0 0x9c8 0x0 0x1b0 0x0 0x4d0 0x0 0x8c0 0x0 0x9c8 0x0 0x7b8 0x0 0x9c8 0x0 0x9c8 0x0 0x5c8 0x0 0x6c0 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x3d8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x2a8 0x2e90007 0x0 0xa8 0x0 0x2fa0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2fe0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3090003 0x0 0xffffffffffffff70 0x3100005 0x0 0x0 0x0 0x0 0x0 0x0 0x3130003 0x0 0x7a8 0x3230007 0x0 0xe0 0x0 0x3340005 0x0 0x0 0x0 0x0 0x0 0x0 0x3380005 0x0 0x0 0x0 0x0 0x0 0x0 0x33b0007 0x0 0x38 0x0 0x33f0003 0x0 0x18 0x34a0003 0x0 0xffffffffffffff38 0x3510005 0x0 0x0 0x0 0x0 0x0 0x0 0x3540003 0x0 0x678 0x3640007 0x0 0xa8 0x0 0x3750005 0x0 0x0 0x0 0x0 0x0 0x0 0x3790005 0x0 0x0 0x0 0x0 0x0 0x0 0x3840003 0x0 0xffffffffffffff70 0x38b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x38e0003 0x0 0x580 0x39e0007 0x0 0xa8 0x0 0x3af0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3b30005 0x0 0x0 0x0 0x0 0x0 0x0 0x3be0003 0x0 0xffffffffffffff70 0x3c50005 0x0 0x0 0x0 0x0 0x0 0x0 0x3c80003 0x0 0x488 0x3d80007 0x0 0xa8 0x0 0x3e90005 0x0 0x0 0x0 0x0 0x0 0x0 0x3ed0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3f70003 0x0 0xffffffffffffff70 0x3fe0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4010003 0x0 0x390 0x4110007 0x0 0xa8 0x0 0x4220005 0x0 0x0 0x0 0x0 0x0 0x0 0x4260005 0x0 0x0 0x0 0x0 0x0 0x0 0x4300003 0x0 0xffffffffffffff70 0x4370005 0x0 0x0 0x0 0x0 0x0 0x0 0x43a0003 0x0 0x298 0x44a0007 0x0 0xb8 0x0 0x45b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x45f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4620002 0x0 0x46c0003 0x0 0xffffffffffffff60 0x4730005 0x0 0x0 0x0 0x0 0x0 0x0 0x4760003 0x0 0x190 0x4860007 0x0 0xb8 0x0 0x4970005 0x0 0x0 0x0 0x0 0x0 0x0 0x49b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x49e0002 0x0 0x4a80003 0x0 0xffffffffffffff60 0x4af0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4b20003 0x0 0x88 0x4b80005 0x0 0x0 0x0 0x0 0x0 0x0 0x4c20002 0x0 0x4c70003 0x0 0x28 0x4ce0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readTypeAnnotationTarget (Lorg/lombokweb/asm/Context;I)I 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 266 0x40005 0x0 0x0 0x0 0x0 0x0 0x0 0xe0008 0x9a 0x0 0x718 0x0 0x4e0 0x0 0x4e0 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x6e8 0x0 0x6e8 0x0 0x6e8 0x0 0x4f8 0x0 0x4f8 0x0 0x4f8 0x0 0x4e0 0x0 0x6e8 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x718 0x0 0x510 0x0 0x510 0x0 0x6e8 0x0 0x700 0x0 0x700 0x0 0x700 0x0 0x700 0x0 0x6d0 0x0 0x6d0 0x0 0x6d0 0x0 0x6d0 0x0 0x6d0 0x1570003 0x0 0x248 0x1650003 0x0 0x230 0x1740005 0x0 0x0 0x0 0x0 0x0 0x0 0x19d0007 0x0 0x170 0x0 0x1a20005 0x0 0x0 0x0 0x0 0x0 0x0 0x1ab0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1b40005 0x0 0x0 0x0 0x0 0x0 0x0 0x1c90002 0x0 0x1cc0004 0x0 0x0 0x0 0x0 0x0 0x0 0x1dd0002 0x0 0x1e00004 0x0 0x0 0x0 0x0 0x0 0x0 0x1ed0003 0x0 0xfffffffffffffea8 0x1f00003 0x0 0x70 0x1fe0003 0x0 0x58 0x20c0003 0x0 0x40 0x21a0003 0x0 0x28 0x2210002 0x0 0x22d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2350007 0x0 0x38 0x0 0x2390003 0x0 0x28 0x2450002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readParameterAnnotations (Lorg/lombokweb/asm/MethodVisitor;Lorg/lombokweb/asm/Context;IZ)V 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 57 0x180005 0x0 0x0 0x0 0x0 0x0 0x0 0x280007 0x0 0x128 0x0 0x2e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3b0007 0x0 0xb8 0x0 0x430005 0x0 0x0 0x0 0x0 0x0 0x0 0x530005 0x0 0x0 0x0 0x0 0x0 0x0 0x5b0002 0x0 0x600003 0x0 0xffffffffffffff60 0x660003 0x0 0xfffffffffffffef0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/MethodWriter <init> (Lorg/lombokweb/asm/SymbolTable;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;I)V 2 5454 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 116 0x30002 0x1386 0xb0002 0x1386 0x1a0005 0x1386 0x0 0x0 0x0 0x0 0x0 0x1d0007 0x1370 0x38 0x16 0x240003 0x16 0x18 0x2e0005 0x1386 0x0 0x0 0x0 0x0 0x0 0x3d0005 0x1386 0x0 0x0 0x0 0x0 0x0 0x4c0007 0x9 0x38 0x137d 0x500003 0x137d 0x50 0x560005 0x9 0x0 0x0 0x0 0x0 0x0 0x5e0007 0x134e 0xc8 0x38 0x640007 0x0 0xa8 0x38 0x810007 0x38 0x70 0x38 0x900005 0x38 0x0 0x0 0x0 0x0 0x0 0x9a0003 0x38 0xffffffffffffffa8 0x9d0003 0x38 0x18 0xb20007 0x4e 0x98 0x1338 0xb70002 0x1338 0xc20007 0x11f4 0x20 0x144 0xd90002 0x1338 0xe40005 0x1338 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x8 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/MethodVisitor <init> (I)V 2 5454 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x30002 0x1386 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/MethodVisitor <init> (ILorg/lombokweb/asm/MethodVisitor;)V 2 5469 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 72 0x10002 0x1392 0x70007 0x1392 0x1a8 0x0 0xd0007 0x0 0x188 0x0 0x130007 0x0 0x168 0x0 0x190007 0x0 0x148 0x0 0x1f0007 0x0 0x128 0x0 0x250007 0x0 0x108 0x0 0x2b0007 0x0 0xe8 0x0 0x360002 0x0 0x3b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x420005 0x0 0x0 0x0 0x0 0x0 0x0 0x450002 0x0 0x4c0007 0x1392 0x30 0x0 0x500002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xffffffffffffffff 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/Type getArgumentsAndReturnSizes (Ljava/lang/String;)I 2 5219 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 97 0x60005 0x13c4 0x0 0x0 0x0 0x0 0x0 0xd0007 0x13c4 0x1d8 0x9f9 0x130007 0x10 0x40 0x9e9 0x190007 0x9e9 0x38 0x0 0x220003 0x10 0x128 0x270005 0xb83 0x0 0x0 0x0 0x0 0x0 0x2c0007 0x9e9 0x38 0x19a 0x320003 0x19a 0xffffffffffffffa8 0x3a0005 0x9e9 0x0 0x0 0x0 0x0 0x0 0x3f0007 0x5c1 0x68 0x428 0x460005 0x428 0x0 0x0 0x0 0x0 0x0 0x500002 0x428 0x590005 0x9f9 0x0 0x0 0x0 0x0 0x0 0x5d0003 0x9f9 0xfffffffffffffe40 0x640005 0x13c4 0x0 0x0 0x0 0x0 0x0 0x6b0007 0x33a 0x20 0x108a 0x750007 0x0 0x40 0x33a 0x7b0007 0x33a 0x38 0x0 0x7f0003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/Label <init> ()V 2 5624 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x1503 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/MethodWriter visitLabel (Lorg/lombokweb/asm/Label;)V 2 5819 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 111 0x180005 0x15c7 0x0 0x0 0x0 0x0 0x0 0x250007 0x1476 0x20 0x151 0x2e0007 0x1476 0xd8 0x0 0x350007 0x0 0x50 0x0 0x430007 0x0 0x20 0x0 0x680002 0x0 0x6f0007 0x0 0x40 0x0 0x7d0007 0x0 0x20 0x0 0xbf0002 0x0 0xc50003 0x0 0x150 0xcd0007 0x1476 0x70 0x0 0xd40007 0x0 0x38 0x0 0xdc0003 0x0 0xf8 0xea0003 0x0 0xe0 0xf20007 0x1476 0x88 0x0 0xf90007 0x0 0x30 0x0 0x10e0002 0x0 0x1240007 0x0 0x20 0x0 0x1340003 0x0 0x58 0x13c0007 0x0 0x40 0x1476 0x1430007 0x8c 0x20 0x13ea 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassWriter visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 2 5454 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x130002 0x1386 0x1c0007 0x1378 0x38 0xe 0x250003 0xe 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassVisitor visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 2 5449 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x40007 0x0 0x58 0x1384 0x120005 0x0 0x0 0x1e0372ff3e0 0x1384 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 1 7 lombok/patcher/PatchScript$FixedClassWriter methods 0
ciMethodData org/lombokweb/asm/ClassReader readCode (Lorg/lombokweb/asm/MethodVisitor;Lorg/lombokweb/asm/Context;I)V 1 7557 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 2531 0x120005 0x0 0x0 0x1e037f4c9b0 0x10 0x0 0x0 0x1c0005 0x0 0x0 0x1e037f4c9b0 0x10 0x0 0x0 0x260005 0x0 0x0 0x1e037f4c9b0 0x10 0x0 0x0 0x380007 0x10 0x30 0x0 0x3f0002 0x0 0x600007 0x10 0x1328 0x6f7 0x770008 0x1bc 0x0 0x12e0 0x0 0xdf0 0x22 0xdf0 0x3 0xdf0 0x18 0xdf0 0x30 0xdf0 0x3 0xdf0 0x1 0xdf0 0x1 0xdf0 0x0 0xdf0 0x1 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x8 0x1280 0x8 0x1298 0x4 0x1280 0xd 0x1298 0x3 0x1298 0x22 0x1280 0x0 0x1280 0x0 0x1280 0x0 0x1280 0x25 0x1280 0x0 0xdf0 0x5 0xdf0 0xd 0xdf0 0x5 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x11f 0xdf0 0x76 0xdf0 0xe 0xdf0 0x1e 0xdf0 0x14 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0xd 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x13 0x1280 0x0 0x1280 0x0 0x1280 0x0 0x1280 0x11 0x1280 0x0 0xdf0 0x1 0xdf0 0x1 0xdf0 0x2 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x1 0xdf0 0x3 0xdf0 0x4 0xdf0 0x7 0xdf0 0xc 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x5 0xdf0 0x0 0xdf0 0x28 0xdf0 0xe 0xdf0 0x0 0xdf0 0xc 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x9 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x19 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x1 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0xb 0xdf0 0x1 0xdf0 0xa 0xdf0 0x1 0xdf0 0x0 0xdf0 0x0 0xdf0 0x3 0x1298 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x2 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x43 0xe08 0x1c 0xe08 0x1 0xe08 0x3 0xe08 0x0 0xe08 0x1 0xe08 0x0 0xe08 0x3 0xe08 0x3 0xe08 0x1 0xe08 0x0 0xe08 0x3 0xe08 0x0 0xe08 0x3 0xe08 0x26 0xe08 0x0 0xe08 0x0 0x1280 0x0 0x1048 0x0 0x1180 0x2 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x5 0xdf0 0x1a 0xdf0 0x14 0x1298 0x1f 0x1298 0x126 0x1298 0x4f 0x1298 0x90 0x1298 0x9 0x1298 0x12 0x1298 0x1 0x12b0 0x0 0x12b0 0x9 0x1298 0x0 0x1280 0x4 0x1298 0x2 0xdf0 0x7 0xdf0 0xe 0x1298 0xb 0x1298 0x0 0xdf0 0x0 0xdf0 0x0 0xf28 0x0 0x12c8 0x15 0xe08 0x7 0xe08 0x0 0xec8 0x0 0xec8 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xe68 0x0 0xec8 0x3fb0003 0x338 0x500 0x4060005 0x0 0x0 0x1e037f4c9b0 0xb3 0x0 0x0 0x40c0002 0xb3 0x4130003 0xb3 0x4a0 0x41e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4240002 0x0 0x42b0003 0x0 0x440 0x4360005 0x0 0x0 0x0 0x0 0x0 0x0 0x43c0002 0x0 0x4430003 0x0 0x3e0 0x4510008 0x1a 0x0 0x110 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xf8 0x0 0xe0 0x4bf0003 0x0 0x2e8 0x4c50003 0x0 0x2d0 0x4cc0002 0x0 0x4e10005 0x0 0x0 0x0 0x0 0x0 0x0 0x4e70002 0x0 0x4f10005 0x0 0x0 0x0 0x0 0x0 0x0 0x4f90005 0x0 0x0 0x0 0x0 0x0 0x0 0x5090007 0x0 0x1f0 0x0 0x5120005 0x0 0x0 0x0 0x0 0x0 0x0 0x5180002 0x0 0x51f0003 0x0 0xffffffffffffff98 0x5330005 0x0 0x0 0x0 0x0 0x0 0x0 0x5390002 0x0 0x5420005 0x0 0x0 0x0 0x0 0x0 0x0 0x54f0007 0x0 0xf0 0x0 0x55a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5600002 0x0 0x5670003 0x0 0xffffffffffffff98 0x56d0003 0x77 0x70 0x5730003 0x294 0x58 0x5790003 0x1 0x40 0x57f0003 0x0 0x28 0x5860002 0x0 0x58a0003 0x6f7 0xffffffffffffecf0 0x5900005 0x0 0x0 0x1e037f4c9b0 0x10 0x0 0x0 0x59d0007 0x10 0x1b8 0x9 0x5a40005 0x0 0x0 0x1e037f4c9b0 0x9 0x0 0x0 0x5a90002 0x9 0x5b40005 0x0 0x0 0x1e037f4c9b0 0x9 0x0 0x0 0x5b90002 0x9 0x5c40005 0x0 0x0 0x1e037f4c9b0 0x9 0x0 0x0 0x5c90002 0x9 0x5d90005 0x0 0x0 0x1e037f4c9b0 0x9 0x0 0x0 0x5df0005 0x0 0x0 0x1e037f4c9b0 0x9 0x0 0x0 0x5f00005 0x2 0x0 0x1e037f4ebd0 0x6 0x1e037ff2f80 0x1 0x5f30003 0x9 0xfffffffffffffe60 0x6110005 0x0 0x0 0x1e037f4c9b0 0x10 0x0 0x0 0x61e0007 0x10 0x690 0x2e 0x6260005 0x0 0x0 0x1e037f4c9b0 0x2e 0x0 0x0 0x6300005 0x0 0x0 0x1e037f4c9b0 0x2e 0x0 0x0 0x63d0005 0x2e 0x0 0x0 0x0 0x0 0x0 0x6400007 0x1e 0x158 0x10 0x6490007 0x0 0x590 0x10 0x6570005 0x0 0x0 0x1e037f4c9b0 0x10 0x0 0x0 0x6640007 0x10 0xc8 0x44 0x66a0005 0x0 0x0 0x1e037f4c9b0 0x44 0x0 0x0 0x6740002 0x44 0x67c0005 0x0 0x0 0x1e037f4c9b0 0x44 0x0 0x0 0x6890002 0x44 0x68f0003 0x44 0xffffffffffffff50 0x6920003 0x10 0x470 0x69a0005 0x1e 0x0 0x0 0x0 0x0 0x0 0x69d0007 0x1e 0x38 0x0 0x6a40003 0x0 0x400 0x6ac0005 0x1e 0x0 0x0 0x0 0x0 0x0 0x6af0007 0xe 0x180 0x10 0x6b80007 0x0 0x390 0x10 0x6c20005 0x0 0x0 0x1e037f4c9b0 0x10 0x0 0x0 0x6cf0007 0x10 0xf0 0x1a4 0x6d50005 0x0 0x0 0x1e037f4c9b0 0x1a4 0x0 0x0 0x6df0005 0x0 0x0 0x1e037f4c9b0 0x1a4 0x0 0x0 0x6ec0002 0x1a4 0x6f60005 0x1a4 0x0 0x0 0x0 0x0 0x0 0x6f90003 0x1a4 0xffffffffffffff28 0x6fc0003 0x10 0x248 0x7030005 0xe 0x0 0x0 0x0 0x0 0x0 0x7060007 0xe 0x48 0x0 0x70f0002 0x0 0x7140003 0x0 0x1c8 0x71c0005 0xe 0x0 0x0 0x0 0x0 0x0 0x71f0007 0xe 0x48 0x0 0x7280002 0x0 0x72d0003 0x0 0x148 0x7350005 0xe 0x0 0x0 0x0 0x0 0x0 0x7380007 0x0 0x58 0xe 0x7410007 0x0 0xd8 0xe 0x7510003 0xe 0xb8 0x7590005 0x0 0x0 0x0 0x0 0x0 0x0 0x75c0007 0x0 0x58 0x0 0x7650007 0x0 0x48 0x0 0x7780003 0x0 0x28 0x78b0002 0x0 0x7a20003 0x2e 0xfffffffffffff988 0x7ac0007 0x10 0x38 0x0 0x7b00003 0x0 0x18 0x7b80007 0x2 0x150 0xe 0x7e80007 0xe 0x30 0x0 0x7ed0002 0x0 0x7fa0007 0xe 0x100 0x256 0x8040007 0x24f 0xc8 0x7 0x80c0005 0x0 0x0 0x1e037f4c9b0 0x7 0x0 0x0 0x8130007 0x0 0x70 0x7 0x81a0007 0x3 0x50 0x4 0x82c0007 0x0 0x30 0x4 0x8340002 0x4 0x83b0003 0x256 0xffffffffffffff18 0x8400007 0x10 0x78 0x0 0x84b0007 0x0 0x58 0x0 0x8550005 0x0 0x0 0x0 0x0 0x0 0x0 0x85f0002 0x10 0x86b0002 0x10 0x87b0007 0x0 0x38 0x10 0x8800003 0x10 0x18 0x88e0007 0x10 0x25d0 0x6f7 0x89b0005 0x0 0x0 0x1e037f4c9b0 0x6f7 0x0 0x0 0x8a70007 0x530 0x90 0x1c7 0x8b30007 0x0 0x38 0x1c7 0x8b70003 0x1c7 0x18 0x8bb0005 0x1c7 0x0 0x0 0x0 0x0 0x0 0x8c00007 0x6f 0x1a8 0x70e 0x8c90007 0x78 0x40 0x696 0x8d10007 0x688 0x168 0xe 0x8d90007 0xe 0xe8 0x78 0x8de0007 0x0 0x40 0x78 0x8e30007 0x78 0x70 0x0 0x8f80005 0x0 0x0 0x0 0x0 0x0 0x0 0x8fb0003 0x0 0x50 0x9130005 0x48 0x0 0x1e037f4ebd0 0x2c 0x1e037f4eb20 0x4 0x91d0007 0xe 0x48 0x78 0x9280002 0x78 0x92d0003 0x78 0xfffffffffffffe88 0x9330003 0xe 0xfffffffffffffe70 0x9380007 0x6f7 0x78 0x0 0x9420007 0x0 0x58 0x0 0x94d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x9600008 0x1bc 0x0 0x2030 0x0 0xdf0 0x22 0xdf0 0x3 0xdf0 0x18 0xdf0 0x30 0xdf0 0x3 0xdf0 0x1 0xdf0 0x1 0xdf0 0x0 0xdf0 0x1 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x8 0x1740 0x8 0x1790 0x4 0x1818 0xd 0x18a0 0x3 0x18a0 0x22 0x16f0 0x0 0x16f0 0x0 0x16f0 0x0 0x16f0 0x25 0x16f0 0x0 0xe40 0x5 0xe40 0xd 0xe40 0x5 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x11f 0xe40 0x76 0xe40 0xe 0xe40 0x1e 0xe40 0x14 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0xd 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x13 0x16f0 0x0 0x16f0 0x0 0x16f0 0x0 0x16f0 0x11 0x16f0 0x0 0xe90 0x1 0xe90 0x1 0xe90 0x2 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x1 0xe90 0x3 0xe90 0x4 0xe90 0x7 0xe90 0xc 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x5 0xdf0 0x0 0xdf0 0x28 0xdf0 0xe 0xdf0 0x0 0xdf0 0xc 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x9 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x19 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x1 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0xb 0xdf0 0x1 0xdf0 0xa 0xdf0 0x1 0xdf0 0x0 0xdf0 0x0 0xdf0 0x3 0x1f58 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x2 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x43 0xee0 0x1c 0xee0 0x1 0xee0 0x3 0xee0 0x0 0xee0 0x1 0xee0 0x0 0xee0 0x3 0xee0 0x3 0xee0 0x1 0xee0 0x0 0xee0 0x3 0xee0 0x0 0xee0 0x3 0xee0 0x26 0xee0 0x0 0xee0 0x0 0x16f0 0x0 0x13b0 0x0 0x1550 0x2 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x5 0xdf0 0x1a 0xdf0 0x14 0x1960 0x1f 0x1960 0x126 0x1960 0x4f 0x1960 0x90 0x1960 0x9 0x1960 0x12 0x1960 0x1 0x1960 0x0 0x1ba8 0x9 0x1ed0 0x0 0x1740 0x4 0x1ed0 0x2 0xdf0 0x7 0xdf0 0xe 0x1ed0 0xb 0x1ed0 0x0 0xdf0 0x0 0xdf0 0x0 0x1248 0x0 0x1fa8 0x15 0xee0 0x7 0xee0 0x0 0xf68 0x0 0xf68 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0x11c0 0xce70005 0x88 0x0 0x1e037f4ebd0 0xb8 0x1e037f4eb20 0xd 0xced0003 0x14d 0x1218 0xcff0005 0x106 0x0 0x1e037f4ebd0 0xc9 0x1e037f4eb20 0x9 0xd050003 0x1d8 0x11c8 0xd170005 0xe 0x0 0x1e037f4ebd0 0x4 0x1e037f4eb20 0x1 0xd1d0003 0x13 0x1178 0xd2c0005 0x0 0x0 0x1e037f4c9b0 0xb3 0x0 0x0 0xd310005 0x74 0x0 0x1e037f4ebd0 0x3b 0x1e037f4eb20 0x4 0xd370003 0xb3 0x10f0 0xd490005 0x0 0x0 0x0 0x0 0x0 0x0 0xd4e0005 0x0 0x0 0x0 0x0 0x0 0x0 0xd540003 0x0 0x1068 0xd5c0007 0x0 0x38 0x0 0xd640003 0x0 0x18 0xd770005 0x0 0x0 0x0 0x0 0x0 0x0 0xd830007 0x0 0x40 0x0 0xd8b0007 0x0 0x70 0x0 0xd960005 0x0 0x0 0x0 0x0 0x0 0x0 0xd990003 0x0 0xd0 0xda10007 0x0 0x38 0x0 0xdac0003 0x0 0x18 0xdbc0002 0x0 0xdc60005 0x0 0x0 0x0 0x0 0x0 0x0 0xdcf0005 0x0 0x0 0x0 0x0 0x0 0x0 0xdd80003 0x0 0xe98 0xde80005 0x0 0x0 0x0 0x0 0x0 0x0 0xded0005 0x0 0x0 0x0 0x0 0x0 0x0 0xdf60003 0x0 0xe10 0xe0b0007 0x0 0xe0 0x0 0xe140005 0x0 0x0 0x0 0x0 0x0 0x0 0xe1c0005 0x0 0x0 0x0 0x0 0x0 0x0 0xe1f0005 0x0 0x0 0x0 0x0 0x0 0x0 0xe250003 0x0 0xd30 0xe300005 0x0 0x0 0x0 0x0 0x0 0x0 0xe330005 0x0 0x0 0x0 0x0 0x0 0x0 0xe390003 0x0 0xca8 0xe4e0005 0x0 0x0 0x0 0x0 0x0 0x0 0xe5a0005 0x0 0x0 0x0 0x0 0x0 0x0 0xe650005 0x0 0x0 0x0 0x0 0x0 0x0 0xe810007 0x0 0xa8 0x0 0xe8f0005 0x0 0x0 0x0 0x0 0x0 0x0 0xe940004 0x0 0x0 0x0 0x0 0x0 0x0 0xe9b0003 0x0 0xffffffffffffff70 0xea70005 0x0 0x0 0x0 0x0 0x0 0x0 0xeaa0003 0x0 0xb08 0xebf0005 0x0 0x0 0x0 0x0 0x0 0x0 0xecb0005 0x0 0x0 0x0 0x0 0x0 0x0 0xee70007 0x0 0xe0 0x0 0xef10005 0x0 0x0 0x0 0x0 0x0 0x0 0xf020005 0x0 0x0 0x0 0x0 0x0 0x0 0xf070004 0x0 0x0 0x0 0x0 0x0 0x0 0xf0e0003 0x0 0xffffffffffffff38 0xf180005 0x0 0x0 0x0 0x0 0x0 0x0 0xf1b0003 0x0 0x968 0xf2c0005 0x2b 0x0 0x1e037f4ebd0 0x30 0x1e037ff2f80 0x10 0xf320003 0x6b 0x918 0xf3f0005 0x3 0x0 0x1e037f4ebd0 0x3 0x1e037ff2f80 0x2 0xf450003 0x8 0x8c8 0xf500005 0x0 0x0 0x1e037f4c9b0 0x8 0x0 0x0 0xf530005 0x0 0x0 0x1e037f4ebd0 0x6 0x1e037ff3030 0x2 0xf590003 0x8 0x840 0xf6b0005 0x0 0x0 0x1e037f4c9b0 0x4 0x0 0x0 0xf6e0005 0x0 0x0 0x1e037f4eb20 0x2 0x1e037ff2f80 0x2 0xf740003 0x4 0x7b8 0xf7e0005 0x0 0x0 0x1e037f4c9b0 0x10 0x0 0x0 0xf830005 0x0 0x0 0x1e037f4c9b0 0x10 0x0 0x0 0xf860005 0xb 0x0 0x1e037f4ebd0 0x4 0x1e037f4eb20 0x1 0xf8c0003 0x10 0x6f8 0xf980005 0x0 0x0 0x1e037f4c9b0 0x254 0x0 0x0 0xfa70005 0x0 0x0 0x1e037f4c9b0 0x254 0x0 0x0 0xfb20005 0x0 0x0 0x1e037f4c9b0 0x254 0x0 0x0 0xfbc0005 0x0 0x0 0x1e037f4c9b0 0x254 0x0 0x0 0xfc80005 0x0 0x0 0x1e037f4c9b0 0x254 0x0 0x0 0xfd20007 0xac 0x70 0x1a8 0xfde0005 0x69 0x0 0x1e037f4ebd0 0xbc 0x1e037ff2f80 0x83 0xfe10003 0x1a8 0x88 0xfed0007 0xab 0x38 0x1 0xff10003 0x1 0x18 0x10020005 0x75 0x0 0x1e037f4ebd0 0x30 0x1e037f4eb20 0x7 0x100a0007 0x253 0x38 0x1 0x10100003 0x1 0x4c8 0x10160003 0x253 0x4b0 0x10220005 0x0 0x0 0x0 0x0 0x0 0x0 0x10310005 0x0 0x0 0x0 0x0 0x0 0x0 0x103c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x10480005 0x0 0x0 0x0 0x0 0x0 0x0 0x10540005 0x0 0x0 0x0 0x0 0x0 0x0 0x105e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x10630005 0x0 0x0 0x0 0x0 0x0 0x0 0x10660004 0x0 0x0 0x0 0x0 0x0 0x0 0x10700005 0x0 0x0 0x0 0x0 0x0 0x0 0x10830007 0x0 0xe0 0x0 0x108e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x10930005 0x0 0x0 0x0 0x0 0x0 0x0 0x10960004 0x0 0x0 0x0 0x0 0x0 0x0 0x109d0003 0x0 0xffffffffffffff38 0x10a90005 0x0 0x0 0x0 0x0 0x0 0x0 0x10af0003 0x0 0x188 0x10bc0005 0x0 0x0 0x1e037f4c9b0 0x26 0x0 0x0 0x10bf0005 0x18 0x0 0x1e037f4ebd0 0xd 0x1e037f4eb20 0x1 0x10c50003 0x26 0x100 0x10db0005 0x0 0x0 0x1e037ff2f80 0x2 0x1e037f4ebd0 0x1 0x10e10003 0x3 0xb0 0x10ec0005 0x0 0x0 0x0 0x0 0x0 0x0 0x10fa0005 0x0 0x0 0x0 0x0 0x0 0x0 0x11000003 0x0 0x28 0x11070002 0x0 0x110d0007 0x6f7 0x138 0x0 0x11150007 0x0 0x118 0x0 0x111c0007 0x0 0xf8 0x0 0x11230007 0x0 0xb0 0x0 0x112d0002 0x0 0x11370005 0x0 0x0 0x0 0x0 0x0 0x0 0x114c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x11540002 0x0 0x11600002 0x0 0x11650003 0x0 0xfffffffffffffee0 0x116a0007 0x6f7 0x138 0x0 0x11720007 0x0 0x118 0x0 0x11790007 0x0 0xf8 0x0 0x11800007 0x0 0xb0 0x0 0x118a0002 0x0 0x11940005 0x0 0x0 0x0 0x0 0x0 0x0 0x11a90005 0x0 0x0 0x0 0x0 0x0 0x0 0x11b10002 0x0 0x11bd0002 0x0 0x11c20003 0x0 0xfffffffffffffee0 0x11c50003 0x6f7 0xffffffffffffda48 0x11cd0007 0x1 0x58 0xf 0x11d60005 0x6 0x0 0x1e037f4ebd0 0x7 0x1e037f4eb20 0x2 0x11db0007 0x0 0x3e8 0x10 0x11e40007 0x0 0x3c8 0x10 0x11ec0007 0x10 0x100 0x0 0x11f20005 0x0 0x0 0x0 0x0 0x0 0x0 0x12080007 0x0 0xa8 0x0 0x12250005 0x0 0x0 0x0 0x0 0x0 0x0 0x12330005 0x0 0x0 0x0 0x0 0x0 0x0 0x123a0003 0x0 0xffffffffffffff70 0x12400005 0x0 0x0 0x1e037f4c9b0 0x10 0x0 0x0 0x12500007 0x10 0x270 0x44 0x12560005 0x0 0x0 0x1e037f4c9b0 0x44 0x0 0x0 0x12600005 0x0 0x0 0x1e037f4c9b0 0x44 0x0 0x0 0x126c0005 0x0 0x0 0x1e037f4c9b0 0x44 0x0 0x0 0x12790005 0x0 0x0 0x1e037f4c9b0 0x44 0x0 0x0 0x12840005 0x0 0x0 0x1e037f4c9b0 0x44 0x0 0x0 0x12910007 0x44 0xe8 0x0 0x129c0007 0x0 0xc8 0x0 0x12a60007 0x0 0x90 0x0 0x12b20007 0x0 0x70 0x0 0x12bf0005 0x0 0x0 0x0 0x0 0x0 0x0 0x12c40003 0x0 0x30 0x12ca0003 0x0 0xffffffffffffff50 0x12e30005 0x1e 0x0 0x1e037f4ebd0 0x21 0x1e037f4eb20 0x5 0x12e60003 0x44 0xfffffffffffffda8 0x12eb0007 0x10 0x160 0x0 0x12fe0007 0x0 0x140 0x0 0x130b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x13140007 0x0 0x40 0x0 0x131b0007 0x0 0xb0 0x0 0x13220002 0x0 0x132c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x134d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x13550002 0x0 0x135c0003 0x0 0xfffffffffffffed8 0x13610007 0x10 0x160 0x0 0x13740007 0x0 0x140 0x0 0x13810005 0x0 0x0 0x0 0x0 0x0 0x0 0x138a0007 0x0 0x40 0x0 0x13910007 0x0 0xb0 0x0 0x13980002 0x0 0x13a20005 0x0 0x0 0x0 0x0 0x0 0x0 0x13c30005 0x0 0x0 0x0 0x0 0x0 0x0 0x13cb0002 0x0 0x13d20003 0x0 0xfffffffffffffed8 0x13d70007 0x10 0x70 0x0 0x13ea0005 0x0 0x0 0x0 0x0 0x0 0x0 0x13f10003 0x0 0xffffffffffffffa8 0x13f90005 0x7 0x0 0x1e037f4ebd0 0x7 0x1e037f4eb20 0x2 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 74 3 org/lombokweb/asm/ClassReader 10 org/lombokweb/asm/ClassReader 17 org/lombokweb/asm/ClassReader 483 org/lombokweb/asm/ClassReader 643 org/lombokweb/asm/ClassReader 654 org/lombokweb/asm/ClassReader 663 org/lombokweb/asm/ClassReader 672 org/lombokweb/asm/ClassReader 681 org/lombokweb/asm/ClassReader 688 org/lombokweb/asm/ClassReader 695 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 697 lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 705 org/lombokweb/asm/ClassReader 716 org/lombokweb/asm/ClassReader 723 org/lombokweb/asm/ClassReader 745 org/lombokweb/asm/ClassReader 756 org/lombokweb/asm/ClassReader 765 org/lombokweb/asm/ClassReader 809 org/lombokweb/asm/ClassReader 820 org/lombokweb/asm/ClassReader 827 org/lombokweb/asm/ClassReader 947 org/lombokweb/asm/ClassReader 1001 org/lombokweb/asm/ClassReader 1060 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1062 org/lombokweb/asm/MethodWriter 1540 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1542 org/lombokweb/asm/MethodWriter 1550 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1552 org/lombokweb/asm/MethodWriter 1560 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1562 org/lombokweb/asm/MethodWriter 1570 org/lombokweb/asm/ClassReader 1577 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1579 org/lombokweb/asm/MethodWriter 1828 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1830 lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 1838 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1840 lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 1848 org/lombokweb/asm/ClassReader 1855 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1857 lombok/patcher/scripts/ReplaceMethodCallScript$ReplaceMethodCall 1865 org/lombokweb/asm/ClassReader 1872 org/lombokweb/asm/MethodWriter 1874 lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 1882 org/lombokweb/asm/ClassReader 1889 org/lombokweb/asm/ClassReader 1896 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1898 org/lombokweb/asm/MethodWriter 1906 org/lombokweb/asm/ClassReader 1913 org/lombokweb/asm/ClassReader 1920 org/lombokweb/asm/ClassReader 1927 org/lombokweb/asm/ClassReader 1934 org/lombokweb/asm/ClassReader 1945 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1947 lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 1962 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1964 org/lombokweb/asm/MethodWriter 2080 org/lombokweb/asm/ClassReader 2087 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2089 org/lombokweb/asm/MethodWriter 2097 lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 2099 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2211 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2213 org/lombokweb/asm/MethodWriter 2258 org/lombokweb/asm/ClassReader 2269 org/lombokweb/asm/ClassReader 2276 org/lombokweb/asm/ClassReader 2283 org/lombokweb/asm/ClassReader 2290 org/lombokweb/asm/ClassReader 2297 org/lombokweb/asm/ClassReader 2333 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2335 org/lombokweb/asm/MethodWriter 2445 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2447 org/lombokweb/asm/MethodWriter methods 0
ciMethodData lombok/patcher/MethodTarget matches (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z 2 5120 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0x50005 0x1201 0x0 0x0 0x0 0x0 0x0 0x80007 0x20 0x20 0x11e1 0xf0005 0x20 0x0 0x0 0x0 0x0 0x0 0x120007 0x20 0x20 0x0 0x190002 0x20 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData lombok/patcher/PatchScript$MethodPatcher visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 2 7030 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 149 0x80002 0x11b8 0x110005 0x0 0x0 0x1e035945ed0 0x11b8 0x0 0x0 0x180003 0x11b8 0x1e0 0x1d0005 0x0 0x0 0x1e03750ea90 0x5ac 0x0 0x0 0x220004 0x0 0x0 0x1e037509dd0 0x5ac 0x0 0x0 0x290005 0x0 0x0 0x1e037509dd0 0x5ac 0x0 0x0 0x2d0005 0x5ac 0x0 0x0 0x0 0x0 0x0 0x300007 0x5aa 0xe8 0x2 0x350005 0x0 0x0 0x1e037509dd0 0x2 0x0 0x0 0x390005 0x2 0x0 0x0 0x0 0x0 0x0 0x3c0007 0x0 0x58 0x2 0x410005 0x0 0x0 0x1e03750ea90 0x2 0x0 0x0 0x480005 0x0 0x0 0x1e03750ea90 0x1764 0x0 0x0 0x4d0007 0x5ab 0xfffffffffffffe00 0x11b9 0x540005 0x0 0x0 0x1e035945ed0 0x11b9 0x0 0x0 0x5b0003 0x11b9 0x128 0x600005 0x0 0x0 0x1e03750ea90 0x11cb 0x0 0x0 0x650004 0x0 0x0 0x1e0372fccc0 0x11cb 0x0 0x0 0x720005 0x0 0x0 0x1e0372fccc0 0x11cb 0x0 0x0 0x770007 0x11bf 0x68 0xc 0x880002 0xc 0x8b0005 0x2 0x0 0x1e0372fcd70 0x5 0x1e0372fce20 0x5 0x930005 0x0 0x0 0x1e03750ea90 0x2378 0x0 0x0 0x980007 0x11cb 0xfffffffffffffeb8 0x11ad 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 14 5 java/util/ArrayList 15 java/util/ArrayList$Itr 22 lombok/patcher/Hook 29 lombok/patcher/Hook 47 lombok/patcher/Hook 65 java/util/ArrayList$Itr 72 java/util/ArrayList$Itr 83 java/util/ArrayList 93 java/util/ArrayList$Itr 100 lombok/patcher/MethodTarget 107 lombok/patcher/MethodTarget 120 lombok/patcher/scripts/ExitFromMethodEarlyScript$1 122 lombok/patcher/scripts/WrapReturnValuesScript$1 127 java/util/ArrayList$Itr methods 0
ciMethodData lombok/patcher/MethodLogistics <init> (ILjava/lang/String;)V 1 17 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 118 0x10002 0xb 0x90007 0xa 0x38 0x1 0xd0003 0x1 0x18 0x150002 0xb 0x1a0005 0x0 0x0 0x1e035945ed0 0xb 0x0 0x0 0x230005 0x0 0x0 0x1e03750ea90 0xb 0x0 0x0 0x280004 0x0 0x0 0x1e035942bc0 0xb 0x0 0x0 0x300002 0xb 0x390002 0xb 0x490002 0xb 0x520002 0xb 0x5b0002 0xb 0x600003 0xb 0x180 0x650005 0x0 0x0 0x1e03750ea90 0xc 0x0 0x0 0x6a0004 0x0 0x0 0x1e035942bc0 0xc 0x0 0x0 0x710002 0xc 0x7a0002 0xc 0x7d0005 0x0 0x0 0x1e035945ed0 0xc 0x0 0x0 0x870002 0xc 0x8a0005 0x0 0x0 0x1e035945ed0 0xc 0x0 0x0 0x940002 0xc 0x970002 0xc 0x9a0005 0x0 0x0 0x1e035945ed0 0xc 0x0 0x0 0xa90005 0x0 0x0 0x1e03750ea90 0x17 0x0 0x0 0xae0007 0xc 0xfffffffffffffe60 0xb 0xb40002 0xb 0xbd0002 0xb 0xc60002 0xb 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 9 14 java/util/ArrayList 21 java/util/ArrayList$Itr 28 java/lang/String 48 java/util/ArrayList$Itr 55 java/lang/String 66 java/util/ArrayList 75 java/util/ArrayList 86 java/util/ArrayList 93 java/util/ArrayList$Itr methods 0
ciMethodData lombok/patcher/Hook getMethodDescriptor ()Ljava/lang/String; 1 253 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 224 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 85 0x40002 0x16 0xb0005 0x16 0x0 0x0 0x0 0x0 0x0 0x130005 0x0 0x0 0x1e03736ca40 0x16 0x0 0x0 0x190003 0x16 0xd0 0x1d0005 0x0 0x0 0x1e037db1660 0x24 0x0 0x0 0x220004 0x0 0x0 0x1e035942bc0 0x24 0x0 0x0 0x280002 0x24 0x2b0005 0x24 0x0 0x0 0x0 0x0 0x0 0x300005 0x0 0x0 0x1e037db1660 0x3a 0x0 0x0 0x350007 0x24 0xffffffffffffff10 0x16 0x3b0005 0x16 0x0 0x0 0x0 0x0 0x0 0x440002 0x16 0x470005 0x16 0x0 0x0 0x0 0x0 0x0 0x4c0005 0x16 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 4 12 java/util/Collections$UnmodifiableRandomAccessList 22 java/util/Collections$UnmodifiableCollection$1 29 java/lang/String 45 java/util/Collections$UnmodifiableCollection$1 methods 0
ciMethodData lombok/patcher/MethodTarget classMatches (Ljava/lang/String;)Z 1 40 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x50002 0x1b 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0xffffffffffffffff oops 0 methods 0
ciMethodData lombok/patcher/MethodTarget descriptorMatch (Ljava/lang/String;)Z 1 38 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 131 0x40007 0x1a 0x20 0x1 0xa0002 0x1a 0xd0005 0x0 0x0 0x1e035945ed0 0x1a 0x0 0x0 0x140005 0x0 0x0 0x1e03750ea90 0x1a 0x0 0x0 0x190004 0x0 0x0 0x1e035942bc0 0x1a 0x0 0x0 0x200002 0x1a 0x230007 0x16 0x20 0x4 0x2c0005 0x0 0x0 0x1e03736ca40 0x16 0x0 0x0 0x320003 0x16 0x128 0x360005 0x0 0x0 0x1e03750ea90 0x17 0x0 0x0 0x3b0004 0x0 0x0 0x1e035942bc0 0x17 0x0 0x0 0x3f0005 0x0 0x0 0x1e037db1660 0x17 0x0 0x0 0x440004 0x0 0x0 0x1e035942bc0 0x17 0x0 0x0 0x470002 0x17 0x4a0007 0x11 0x20 0x6 0x500005 0x0 0x0 0x1e03750ea90 0x27 0x0 0x0 0x550007 0xe 0x78 0x19 0x590005 0x0 0x0 0x1e037db1660 0x19 0x0 0x0 0x5e0007 0x17 0xfffffffffffffe60 0x2 0x620005 0x0 0x0 0x1e03750ea90 0x10 0x0 0x0 0x670007 0x2 0x78 0xe 0x6b0005 0x0 0x0 0x1e037db1660 0xe 0x0 0x0 0x700007 0x2 0x20 0xc 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 12 9 java/util/ArrayList 16 java/util/ArrayList$Itr 23 java/lang/String 36 java/util/Collections$UnmodifiableRandomAccessList 46 java/util/ArrayList$Itr 53 java/lang/String 60 java/util/Collections$UnmodifiableCollection$1 67 java/lang/String 80 java/util/ArrayList$Itr 91 java/util/Collections$UnmodifiableCollection$1 102 java/util/ArrayList$Itr 113 java/util/Collections$UnmodifiableCollection$1 methods 0
compile org/lombokweb/asm/ClassReader readMethod (Lorg/lombokweb/asm/ClassVisitor;Lorg/lombokweb/asm/Context;I)I -1 4 inline 153 0 -1 org/lombokweb/asm/ClassReader readMethod (Lorg/lombokweb/asm/ClassVisitor;Lorg/lombokweb/asm/Context;I)I 1 13 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 1 27 org/lombokweb/asm/ClassReader readUTF8 (I[C)Ljava/lang/String; 2 2 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 2 19 org/lombokweb/asm/ClassReader readUtf (I[C)Ljava/lang/String; 3 34 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 3 38 org/lombokweb/asm/ClassReader readUtf (II[C)Ljava/lang/String; 4 157 java/lang/String <init> ([CII)V 5 7 java/lang/String rangeCheck ([CII)Ljava/lang/Void; 6 4 java/lang/String checkBoundsOffCount (III)V 5 10 java/lang/String <init> ([CIILjava/lang/Void;)V 6 1 java/lang/Object <init> ()V 6 36 java/lang/StringUTF16 compress ([CII)[B 1 41 org/lombokweb/asm/ClassReader readUTF8 (I[C)Ljava/lang/String; 2 2 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 2 19 org/lombokweb/asm/ClassReader readUtf (I[C)Ljava/lang/String; 3 34 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 3 38 org/lombokweb/asm/ClassReader readUtf (II[C)Ljava/lang/String; 4 157 java/lang/String <init> ([CII)V 5 7 java/lang/String rangeCheck ([CII)Ljava/lang/Void; 6 4 java/lang/String checkBoundsOffCount (III)V 5 10 java/lang/String <init> ([CIILjava/lang/Void;)V 6 1 java/lang/Object <init> ()V 6 36 java/lang/StringUTF16 compress ([CII)[B 1 95 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 1 116 org/lombokweb/asm/ClassReader readUTF8 (I[C)Ljava/lang/String; 2 2 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 2 19 org/lombokweb/asm/ClassReader readUtf (I[C)Ljava/lang/String; 3 34 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 3 38 org/lombokweb/asm/ClassReader readUtf (II[C)Ljava/lang/String; 4 157 java/lang/String <init> ([CII)V 5 7 java/lang/String rangeCheck ([CII)Ljava/lang/Void; 6 4 java/lang/String checkBoundsOffCount (III)V 5 10 java/lang/String <init> ([CIILjava/lang/Void;)V 6 1 java/lang/Object <init> ()V 6 36 java/lang/StringUTF16 compress ([CII)[B 1 126 org/lombokweb/asm/ClassReader readInt (I)I 1 139 java/lang/String equals (Ljava/lang/Object;)Z 1 166 java/lang/String equals (Ljava/lang/Object;)Z 1 179 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 1 242 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 1 519 lombok/patcher/PatchScript$MethodPatcher visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 2 8 org/lombokweb/asm/ClassVisitor visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 3 18 org/lombokweb/asm/ClassWriter visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 4 19 org/lombokweb/asm/MethodWriter <init> (Lorg/lombokweb/asm/SymbolTable;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;I)V 5 3 org/lombokweb/asm/MethodVisitor <init> (I)V 6 3 org/lombokweb/asm/MethodVisitor <init> (ILorg/lombokweb/asm/MethodVisitor;)V 7 1 java/lang/Object <init> ()V 5 11 org/lombokweb/asm/ByteVector <init> ()V 6 1 java/lang/Object <init> ()V 5 26 java/lang/String equals (Ljava/lang/Object;)Z 5 46 org/lombokweb/asm/SymbolTable addConstantUtf8 (Ljava/lang/String;)I 6 2 org/lombokweb/asm/SymbolTable hash (ILjava/lang/String;)I 7 4 java/lang/String hashCode ()I 8 17 java/lang/String isLatin1 ()Z 8 27 java/lang/StringLatin1 hashCode ([B)I 6 8 org/lombokweb/asm/SymbolTable get (I)Lorg/lombokweb/asm/SymbolTable$Entry; 6 37 java/lang/String equals (Ljava/lang/Object;)Z 6 88 org/lombokweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;I)V 7 7 org/lombokweb/asm/Symbol <init> (IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V 8 1 java/lang/Object <init> ()V 5 61 org/lombokweb/asm/SymbolTable addConstantUtf8 (Ljava/lang/String;)I 6 2 org/lombokweb/asm/SymbolTable hash (ILjava/lang/String;)I 7 4 java/lang/String hashCode ()I 8 17 java/lang/String isLatin1 ()Z 8 27 java/lang/StringLatin1 hashCode ([B)I 6 8 org/lombokweb/asm/SymbolTable get (I)Lorg/lombokweb/asm/SymbolTable$Entry; 6 37 java/lang/String equals (Ljava/lang/Object;)Z 6 88 org/lombokweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;I)V 7 7 org/lombokweb/asm/Symbol <init> (IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V 8 1 java/lang/Object <init> ()V 5 144 org/lombokweb/asm/SymbolTable addConstantClass (Ljava/lang/String;)Lorg/lombokweb/asm/Symbol; 5 183 org/lombokweb/asm/Type getArgumentsAndReturnSizes (Ljava/lang/String;)I 6 6 java/lang/String charAt (I)C 7 1 java/lang/String isLatin1 ()Z 7 12 java/lang/StringLatin1 charAt ([BI)C 6 39 java/lang/String charAt (I)C 7 1 java/lang/String isLatin1 ()Z 7 12 java/lang/StringLatin1 charAt ([BI)C 6 58 java/lang/String charAt (I)C 7 1 java/lang/String isLatin1 ()Z 7 12 java/lang/StringLatin1 charAt ([BI)C 6 70 java/lang/String indexOf (II)I 7 1 java/lang/String isLatin1 ()Z 7 13 java/lang/StringLatin1 indexOf ([BII)I 8 1 java/lang/StringLatin1 canEncode (I)Z 6 89 java/lang/String charAt (I)C 7 1 java/lang/String isLatin1 ()Z 7 12 java/lang/StringLatin1 charAt ([BI)C 6 100 java/lang/String charAt (I)C 7 1 java/lang/String isLatin1 ()Z 7 12 java/lang/StringLatin1 charAt ([BI)C 5 217 org/lombokweb/asm/Label <init> ()V 6 1 java/lang/Object <init> ()V 2 17 java/util/ArrayList iterator ()Ljava/util/Iterator; 3 5 java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 4 6 java/lang/Object <init> ()V 2 72 java/util/ArrayList$Itr hasNext ()Z 2 29 java/util/ArrayList$Itr next ()Ljava/lang/Object; 3 1 java/util/ArrayList$Itr checkForComodification ()V 2 41 lombok/patcher/Hook getMethodName ()Ljava/lang/String; 2 45 java/lang/String equals (Ljava/lang/Object;)Z 2 84 java/util/ArrayList iterator ()Ljava/util/Iterator; 3 5 java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 4 6 java/lang/Object <init> ()V 2 147 java/util/ArrayList$Itr hasNext ()Z 2 96 java/util/ArrayList$Itr next ()Ljava/lang/Object; 3 1 java/util/ArrayList$Itr checkForComodification ()V 2 114 lombok/patcher/MethodTarget matches (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z 3 5 java/lang/String equals (Ljava/lang/Object;)Z 2 136 lombok/patcher/MethodLogistics <init> (ILjava/lang/String;)V 3 1 java/lang/Object <init> ()V 3 48 lombok/patcher/MethodLogistics sizeOf (Ljava/lang/String;)I 4 2 java/lang/String charAt (I)C 5 1 java/lang/String isLatin1 ()Z 5 12 java/lang/StringLatin1 charAt ([BI)C 3 57 lombok/patcher/MethodLogistics returnOpcodeFor (Ljava/lang/String;)I 4 2 java/lang/String charAt (I)C 5 1 java/lang/String isLatin1 ()Z 5 12 java/lang/StringLatin1 charAt ([BI)C 3 73 java/util/ArrayList <init> ()V 4 1 java/util/AbstractList <init> ()V 5 1 java/util/AbstractCollection <init> ()V 6 1 java/lang/Object <init> ()V 3 82 java/util/ArrayList <init> ()V 4 1 java/util/AbstractList <init> ()V 5 1 java/util/AbstractCollection <init> ()V 6 1 java/lang/Object <init> ()V 3 91 java/util/ArrayList <init> ()V 4 1 java/util/AbstractList <init> ()V 5 1 java/util/AbstractCollection <init> ()V 6 1 java/lang/Object <init> ()V 3 113 lombok/patcher/MethodLogistics sizeOf (Ljava/lang/String;)I 4 2 java/lang/String charAt (I)C 5 1 java/lang/String isLatin1 ()Z 5 12 java/lang/StringLatin1 charAt ([BI)C 3 122 java/lang/Integer valueOf (I)Ljava/lang/Integer; 3 135 java/lang/Integer valueOf (I)Ljava/lang/Integer; 4 28 java/lang/Integer <init> (I)V 5 1 java/lang/Number <init> ()V 6 1 java/lang/Object <init> ()V 3 148 lombok/patcher/MethodLogistics loadOpcodeFor (Ljava/lang/String;)I 4 2 java/lang/String charAt (I)C 5 1 java/lang/String isLatin1 ()Z 5 12 java/lang/StringLatin1 charAt ([BI)C 3 151 java/lang/Integer valueOf (I)Ljava/lang/Integer; 1 572 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 1 579 org/lombokweb/asm/MethodWriter canCopyMethodAttributes (Lorg/lombokweb/asm/ClassReader;ZZIII)Z 2 5 org/lombokweb/asm/SymbolTable getSource ()Lorg/lombokweb/asm/ClassReader; 2 55 org/lombokweb/asm/SymbolTable getMajorVersion ()I 2 106 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 2 137 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 1 592 org/lombokweb/asm/MethodWriter setMethodAttributesSource (II)V
