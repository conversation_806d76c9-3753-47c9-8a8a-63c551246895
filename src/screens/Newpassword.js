import {
  ScrollView,
  StyleSheet,
  Text,
  View,
  KeyboardAvoidingView,
  TouchableOpacity,
  Alert,
  Platform,
  ActivityIndicator,
} from 'react-native';
import React, { useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaView } from 'react-native-safe-area-context';
import CustomImage from '../components/CustomImage';
import CustomTextinput from '../components/CustomTextinput';
import { buttoncolor, textcolor } from '../styles/style';
import CustomButton from '../components/CustomButton';
import { useTranslation } from 'react-i18next';
import { config } from '../../config';

const Newpassword = ({ navigation }) => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  const handleResetPassword = async () => {
    if (!password || !confirmPassword) {
      Alert.alert(t('error'), t('passwordRequired'));
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert(t('error'), t('passwordsDoNotMatch'));
      return;
    }

    setLoading(true);

    try {
      const userData = await AsyncStorage.getItem('User');
      const userId = userData ? JSON.parse(userData)._id : null;

      if (!userId) {
        Alert.alert('Error', 'User ID not found');
        setLoading(false);
        return;
      }

      const response = await fetch(`${config.baseUrl}/auth/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password, userId }),
      });

      const data = await response.json();

      if (response.ok) {
        Alert.alert('Success', data.message);
        navigation.navigate('SignIn');
      } else {
        Alert.alert('Error', data.message);
      }
    } catch (error) {
      Alert.alert('Error', error.message);
    }
    finally {
      setLoading(false); 
    }
  };

  
  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior="padding"
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}>
        <ScrollView contentContainerStyle={{ flexGrow: 1, marginTop: 20 }}>
          <CustomImage header={t('newpassword')} />

          <View style={{ marginTop: 30 }}>
            <CustomTextinput
              label={t('password')}
              placeholder={t('enterPassword')}
              secureTextEntry={true}
              onChangeText={setPassword} 
            />
            <CustomTextinput
              label={t('confirmPass')}
              placeholder={t('enterconfirmPass')}
              secureTextEntry={true}
              onChangeText={setConfirmPassword} 
            />
          </View>

          <View style={{ marginTop: 20 }}>
          {loading ? (
              <ActivityIndicator size="large" color={textcolor.color2}/>
            ) : (
              <CustomButton
                title={t('send')}
                onPress={handleResetPassword} 
              />
            )}
            <View style={styles.bottom}>
              <Text style={styles.text}>{t('areNew')} </Text>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate('Signup');
                }}>
                <Text style={styles.text2}>{t('createAccount')}</Text>
              </TouchableOpacity>
            </View>
          </View>
          <TouchableOpacity
            style={{ marginTop: -15 }}
            onPress={() => {
              navigation.navigate('SignIn');
            }}>
            <Text style={styles.text3}>{t('backtoSignin')}</Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Newpassword;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  bottom: {
    flexDirection: 'row',
    alignSelf: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  text: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color1,
  },
  text2: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color2,
  },
  text3: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color2,
    alignSelf: 'center',
  },
});