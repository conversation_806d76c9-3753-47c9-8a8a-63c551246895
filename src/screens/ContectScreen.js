import React, { useState } from 'react';
import { ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View, Modal, Button } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import CustomMenu from '../components/CustomMenu';
import CustomProfileDetails from '../components/CustomProfileDetails';
import CustomImage from '../components/CustomImage';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import { textcolor } from '../styles/style';

import BackButton from '../components/BackButton';


const ContactCard = ({ name, position, location,onDelete }) => (
  <View style={styles.main}>
    <View style={styles.Imgbox} />
    <View style={styles.detail}>
      
      <Text style={styles.text2}>{name}</Text>
      <Text style={styles.text2}>{position}</Text>
      <Text style={styles.text2}>{location}</Text>
    </View>
    <View style={styles.iconContainer}>
      <TouchableOpacity style={styles.Icon}>
        <Feather name="phone" size={25} color={'#31A8F7'} />
      </TouchableOpacity>
      <TouchableOpacity style={styles.Icon}>
        <Feather name="message-circle" size={25} color={'#31A8F7'} />
      </TouchableOpacity>
      <TouchableOpacity style={styles.Icon}>
        <FontAwesome name="envelope-o" size={25} color={'#31A8F7'} />
      </TouchableOpacity>
      <View  style={styles.deleteButton}>
  <TouchableOpacity onPress={onDelete}>
      <Feather name="trash-2" size={18} color={'red'} />
    </TouchableOpacity>
  </View>
    </View>
  
  </View>
);

export default function ContactScreen() {
  const [contacts, setContacts] = useState([
    { name: 'Owner TexiReport', position: 'Owner', location: 'Sweden, Malmo' },
    { name: 'Owner TexiReport', position: 'Owner', location: 'Sweden, Malmo' },
  ]);

   const [modalVisible, setModalVisible] = useState(false);                        // use fro modal
  const [newContact, setNewContact] = useState({ name: '', position: '', location: '' });       // new data

  const handleAddContact = () => {                            // add new data
    setContacts([...contacts, newContact]);
    setNewContact({ name: '', position: '', location: '' });
    setModalVisible(false);
  };

  const handleDelete = (index) => {                                 // use for delete button
    const newContacts = contacts.filter((_, i) => i !== index);
    setContacts(newContacts);
  };
  return (
    <SafeAreaView style={styles.Container}>
      <ScrollView contentContainerStyle={styles.scrollViewContent}>
      <View style={styles.navBar}>
            <View style={styles.logo}>
             <BackButton/>
            
            </View>
            <CustomProfileDetails />
          </View>
          <View>
            <Text style={styles.title1}>Important Contect</Text>
          </View>
        {contacts.map((contact, index) => (
          <ContactCard
            key={index}
            name={contact.name}
            position={contact.position}
            location={contact.location}
            onDelete={() => handleDelete(index)}  //  use Pass handle Delete function
          />
        ))}
        <TouchableOpacity style={styles.more}
         onPress={() => setModalVisible(true)}     // click open modal
         >
          <Feather name='plus-circle' size={25} />
          <Text style={styles.text}>Add Important Contact</Text>
        </TouchableOpacity>
      </ScrollView>
    

      {/* Modal for Adding Contact */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalBackground}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Add New Contact</Text>
            <TextInput
              style={styles.input}
              placeholder="Name"
              value={newContact.name}                                         // new data
             onChangeText={(text) => setNewContact({ ...newContact, name: text })}  // add new data
            />
            <TextInput
              style={styles.input}
              placeholder="Position"
              value={newContact.position}
              onChangeText={(text) => setNewContact({ ...newContact, position: text })}
            />
            <TextInput
              style={styles.input}
              placeholder="Location"
              value={newContact.location}
             onChangeText={(text) => setNewContact({ ...newContact, location: text })}
            />
            <View style={styles.buttonContainer}>
              <Button title="Save" onPress={handleAddContact}        // save data
              />
              <Button title="Cancel" onPress={() => setModalVisible(false)} color="red" />
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  Container: {
    flex: 1,
    backgroundColor: 'white',
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  main: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 4,
    borderTopWidth: 1,
    borderTopColor: 'grey',
    paddingVertical: 5,
    marginTop: '5%',
  },
  Imgbox: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'black',
    alignSelf: 'center',
  },
  detail: {
    alignSelf: 'center',
    marginLeft: 10,
  },
  text: {
    fontSize: 12,
    fontWeight: '700',
    color: 'black',
  },
  text2: {
    fontSize: 12,
    fontWeight: '400',
    color: 'black',
  },
  // logo: {
  //   marginTop: '-30%',
  // },
  iconContainer: {
    flexDirection: 'row',
    alignSelf: 'center',
  },
  Icon: {
    width: 50,
    height: 50,
    borderWidth: 2,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
    
  },
  more: {
    alignItems: 'center',
    borderTopColor: 'grey',
    borderTopWidth: 1,
    paddingVertical: 10,
    marginTop: 20,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 60, // Adjust this padding to fit the CustomBottomTab height
  },
  bottomTabContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
  },
  modalBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color:'black'
  },
  input: {
    borderBottomWidth: 1,
    borderBottomColor: 'grey',
    marginBottom: 15,
    padding: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  deleteButton: {
   position: 'absolute',
    top: '-20%',
    right: "-1%",
    zIndex: 1, 
  },
  title1: {
    fontSize: 20,
    fontWeight: '700',
    color: textcolor.color2,
    alignSelf:'center'
  },
});





// import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
// import React from 'react';
// import { SafeAreaView } from 'react-native-safe-area-context';
// import CustomMenu from '../components/CustomMenu';
// import CustomProfileDetails from '../components/CustomProfileDetails';
// import CustomImage from '../components/CustomImage';
// import FontAwesome from 'react-native-vector-icons/FontAwesome';
// import Feather from 'react-native-vector-icons/Feather';

// const ContactCard = ({ name, position, location }) => (
//   <View style={styles.main}>
//     <View style={styles.Imgbox} />
//     <View style={styles.detail}>
//       <Text style={styles.text}>Name</Text>
//       <Text style={styles.text2}>{name}</Text>
//       <Text style={styles.text2}>{position}</Text>
//       <Text style={styles.text2}>{location}</Text>
//     </View>
//     <View style={styles.iconContainer}>
//       <View style={styles.Icon}>
//         <Feather name="phone" size={25} color={'#31A8F7'} />
//       </View>
//       <View style={styles.Icon}>
//         <Feather name="message-circle" size={25} color={'#31A8F7'} />
//       </View>
//       <View style={styles.Icon}>
//         <FontAwesome name="envelope-o" size={25} color={'#31A8F7'} />
//       </View>
//     </View>
//   </View>
// );

// export default function ContactScreen() {
//   return (
//     <SafeAreaView style={styles.Container}>
//       <ScrollView contentContainerStyle={styles.scrollViewContent}>
//         <View style={styles.navBar}>
//           <CustomMenu />
//           <CustomProfileDetails />
//         </View>
//         <View style={styles.logo}>
//           <CustomImage header={'Important Contacts'} />
//         </View>
//         <ContactCard
//           name="Owner TexiReport"
//           position="Owner"
//           location="Sweden, Malmo"
//         />
//         <ContactCard
//           name="Owner TexiReport"
//           position="Owner"
//           location="Sweden, Malmo"
//         />
//         <TouchableOpacity style={styles.more}>
//           <Feather name='plus-circle' size={25} />
//           <Text style={styles.text}>Add Important Contact</Text>
//         </TouchableOpacity>
//       </ScrollView>
     
//     </SafeAreaView>
//   );
// }

// const styles = StyleSheet.create({
//   Container: {
//     flex: 1,
//     backgroundColor: 'white',
//   },
//   navBar: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',

//   },
//   main: {
//     width: '100%',
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     marginHorizontal: '3%',
//     borderTopWidth: 1,
//     borderTopColor: 'grey',
//     paddingVertical: 10,
//     marginTop: '5%',
//   },
//   Imgbox: {
//     width: 70,
//     height: 70,
//     borderRadius: 35,
//     backgroundColor: 'black',
//     alignSelf: 'center',
//   },
//   detail: {
//     alignSelf: 'center',
//     marginLeft: 10,
//   },
//   text: {
//     fontSize: 12,
//     fontWeight: '700',
//     color: 'black',
//   },
//   text2: {
//     fontSize: 12,
//     fontWeight: '400',
//     color: 'black',
//   },
//   logo: {
//     marginTop: '-30%',
    
//   },
//   iconContainer: {
//     flexDirection: 'row',
//     alignSelf: 'center',
//   },
//   Icon: {
//     width: 50,
//     height: 50,
//     borderWidth: 2,
//     borderRadius: 25,
//     justifyContent: 'center',
//     alignItems: 'center',
//     marginHorizontal: 5,
//   },
//   more: {
//     alignItems: 'center',
//     borderTopColor: 'grey',
//     borderTopWidth: 1,
//     paddingVertical: 10,
//     marginTop: 20,
//   },
//   scrollViewContent: {
//     flexGrow: 1,
//     paddingBottom: 60, // Adjust this padding to fit the CustomBottomTab height
//   },
//   bottomTabContainer: {
//     position: 'absolute',
//     bottom: 0,
//     width: '100%',
//   },
// });




// import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
// import React from 'react'
// import { SafeAreaView } from 'react-native-safe-area-context'
// import CustomMenu from '../components/CustomMenu'
// import CustomProfileDetails from '../components/CustomProfileDetails'
// import CustomImage from '../components/CustomImage'
// import FontAwesome from 'react-native-vector-icons/FontAwesome'
// import Feather from 'react-native-vector-icons/Feather'
// export default function ContectScreen() {
//   return (
//   <SafeAreaView>
//     <SafeAreaView style={styles.Container}>
//     <View style={styles.navBar}>
//             <CustomMenu />
//              <CustomProfileDetails/>
//           </View>
//           <View style={styles.logo}>
//             <CustomImage header={'Important Contects'}/>
//           </View>

//           <View style={styles.main}>
//             <View style={styles.Imgbox}>
//             </View>
//             <View style={styles.detail}>
//                 <Text style={styles.text}>Name</Text>
//                 <Text style={styles.text2}>Owner TexiReport</Text>
//                 <Text style={styles.text2}>Sweden, Malmo</Text>
//             </View>

//             <View style={{flexDirection:'row',alignSelf:'center'}}>
//                 <View style={styles.Icon}>
//                     <Feather name="phone" size={25} color={'#31A8F7'} />
//                 </View>
//                 <View style={styles.Icon}>
//                     <Feather name="message-circle" size={25} color={'#31A8F7'} />
//                 </View>
//                 <View style={styles.Icon}>
//                     <FontAwesome name="envelope-o" size={25} color={'#31A8F7'} />
//                 </View>
//             </View>
//           </View>
            
//           <View style={styles.main}>
//             <View style={styles.Imgbox}>
//             </View>
//             <View style={styles.detail}>
//                 <Text style={styles.text}>Name</Text>
//                 <Text style={styles.text2}>Owner TexiReport</Text>
//                 <Text style={styles.text2}>Sweden, Malmo</Text>
//             </View>

//             <View style={{flexDirection:'row',alignSelf:'center'}}>
//                 <View style={styles.Icon}>
//                     <Feather name="phone" size={25} color={'#31A8F7'} />
//                 </View>
//                 <View style={styles.Icon}>
//                     <Feather name="message-circle" size={25} color={'#31A8F7'} />
//                 </View>
//                 <View style={styles.Icon}>
//                     <FontAwesome name="envelope-o" size={25} color={'#31A8F7'} />
//                 </View>
//             </View>
//           </View>
              
         
//           <TouchableOpacity style={styles.more}>
//                <Feather name='plus-circle' size={25}/>
//                <Text style={styles.text}>Add Important Contect</Text>
//           </TouchableOpacity>
//     </SafeAreaView>
//   </SafeAreaView>
//   )
// }

// const styles = StyleSheet.create({
//     Container: {
//         // flex: 1,
//         backgroundColor:'white'
//       },
//       navBar: {
//         flexDirection: 'row',
//         justifyContent: 'space-between',
//       },
//       main:{
//         width:'100%',
//         flexDirection:"row",
//         justifyContent:'space-between',
//         marginHorizontal:'5%',
//         borderTopWidth:1,
//         borderTopColor:'grey',
//         paddingVertical:10,
//         marginTop:"5%"
//       },
//       Imgbox:{
//        width:70,
//        height:70,
//        borderRadius:50,
//        backgroundColor:'black',
//        alignSelf:'center'
//       },
//       detail:{
//        alignSelf:'center'
//       },
//       text:{
//        fontSize:12,
//        fontWeight:'700',
//        color:'black'
//       },
//       text2:{
//         fontSize:12,
//        fontWeight:'400',
//        color:'black'
//       },
//       logo: {
//         marginTop: "-30%",
//       },
//       Icon:{
//         width:50,
//         height:50,
//         borderWidth:2,
//         borderRadius:50,
//         justifyContent:'center',
//         alignItems:'center',
//         marginLeft:'4%'
//       },
//       more:{
//         alignItems:'center',
//         borderTopColor:'grey',
//         borderTopWidth:1,
//         paddingVertical:10
//       }

// })
