import {
  KeyboardAvoidingView,
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import React, {useState} from 'react';
import CustomImage from '../components/CustomImage';
import {textcolor} from '../styles/style';
import CustomButton from '../components/CustomButton';
import OTPinput from '../components/OTPinput';
import {useTranslation} from 'react-i18next';
import AuthService from '../services/features/auth/auth.service';
import AsyncStorage from '@react-native-async-storage/async-storage';

const Verification = ({navigation, route}) => {
  //

  const [otp, setOtp] = useState('');
  const [loading, setLoading] = useState(false);
  const {t} = useTranslation();
  const {componentType} = route.params; //
  // console.log('componentType', componentType); //
  const handleOtpSubmit = async () => {
    setLoading(true);
    if (!otp) {
      Alert.alert('otp', 'Email is required !!');
      return;
    }

    try {
      // Retrieve the stored user object from AsyncStorage
      const user = await AsyncStorage.getItem('User'); 

      // Parse the user data and create the payload
      const parsedUser = user ? JSON.parse(user) : null;
      const userId = parsedUser ? parsedUser._id : null;

      console.log(userId);

      if (!userId) {
        throw new Error('No user ID found');
      }

      // Send the OTP verification request
      const payload = {otp, userId};
      const response = await AuthService.otp(payload);
      // if(response.suc)
      // console.log('Response on frontend :', response);
      Alert.alert(t('success'), t('otpVerified'));
      if (componentType === 'forgetPassword') {
        navigation.navigate('Newpassword');
      } else {
        navigation.navigate('SignIn'); ////
      }
      // navigation.navigate('SignIn'); // Navigate to SignIn on success
    } catch (error) {
      console.log('Error:', error);
      Alert.alert(t('error'), t('otpFailed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior="padding"
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}>
        <ScrollView contentContainerStyle={{flexGrow: 1, marginTop: 20}}>
          <CustomImage header={t('verification')} />
          <Text style={styles.title}>{t('enterVerification')}</Text>
          <OTPinput value={otp} setValue={setOtp} />
          <View style={styles.bottom}>
            <Text style={styles.text}>{t('ifyounotRecieve')} </Text>
            <TouchableOpacity>
              <Text style={styles.text2}>{t('resend')}</Text>
            </TouchableOpacity>
          </View>
          <View style={{marginTop: 20}}>
            {loading ? (
              <ActivityIndicator size="large" color={textcolor.color2} />
            ) : (
              <CustomButton title={t('send')} onPress={handleOtpSubmit} />
            )}
            <View style={styles.bottom}>
              <Text style={styles.text}>{t('areNew')} </Text>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate('Signup');
                }}>
                <Text style={styles.text2}>{t('createAccount')}</Text>
              </TouchableOpacity>
            </View>
          </View>
          <TouchableOpacity
            style={{marginTop: -15}}
            onPress={() => {
              navigation.navigate('SignIn');
            }}>
            <Text style={styles.text3}>{t('backtoSignin')}</Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Verification;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: textcolor.color1,
    alignSelf: 'center',
    marginVertical: 30,
  },
  bottom: {
    flexDirection: 'row',
    alignSelf: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  text: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color1,
  },
  text2: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color2,
  },
  text3: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color2,
    alignSelf: 'center',
  },
});
