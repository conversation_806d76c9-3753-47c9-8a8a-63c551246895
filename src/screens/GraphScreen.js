// import React, { useState, useRef } from 'react';
import React, {useCallback, useRef, useState} from 'react';
import {
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  View,
  Alert,
  Text,
  ActivityIndicator,
  TouchableOpacity,
  PermissionsAndroid,
  Platform,
  Dimensions,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CustomgraphView from '../components/CustomgraphView';
import {buttoncolor, textcolor} from '../styles/style';
import Custominput from '../components/Custominput';
import {useTranslation} from 'react-i18next';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faShareNodes} from '@fortawesome/free-solid-svg-icons';
import RNFS from 'react-native-fs';
import RNHTMLtoPDF from 'react-native-html-to-pdf';
import Share from 'react-native-share';
import {captureRef} from 'react-native-view-shot'; // Import view-shot for capturing the graph
import CustomImage from '../components/CustomImage';
import CustomMenu from '../components/CustomMenu';
import CustomProfileDetails from '../components/CustomProfileDetails';
import {Button} from 'react-native-elements';
import CustomButton from '../components/CustomButton';
import {config} from '../../config';
import ViewShot from 'react-native-view-shot';

const {height, width} = Dimensions.get('window');
const ViewGraph = () => {
  const [userId, setUserId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [totalBeforeDetection, setTotalBeforeDetection] = useState(0);
  const [totalAfterDetection, setTotalAfterDetection] = useState(0);
  const [totalTip, setTotalTip] = useState(0);
  const [totalKastrup, setTotalKastrup] = useState(0);
  const [totalStation, setTotalStation] = useState(0);
  const [totalSturup, setTotalSturup] = useState(0);
  const [reportSettings, setReportSettings] = useState({});
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear()); // REMOVE THIS

  const graphRef = useRef(); // Ref for the graph view
  const {t} = useTranslation();

  useFocusEffect(
    useCallback(() => {
      const fetchUserId = async () => {
        try {
          const user = await AsyncStorage.getItem('User');
          const parsedUser = user ? JSON.parse(user) : null;
          const id = parsedUser ? parsedUser._id : null;

          if (!id) {
            Alert.alert('Error', 'User ID not found');
            return;
          }

          setUserId(id);
        } catch (error) {
          Alert.alert('Error', 'Failed to fetch user ID');
          console.error('Error fetching user ID:', error);
        } finally {
          setLoading(false);
        }
      };
      const fetchReportSettings = async () => {
        try {
          const storedSettings = await AsyncStorage.getItem('reportSettings');
          if (storedSettings) {
            setReportSettings(JSON.parse(storedSettings));
            // console.log(reportSettings);
          } else if (userId) {
            const response = await fetch(
              `${config.baseUrl}/auth/report-setting/${userId}`,
            );
            const data = await response.json();
            await AsyncStorage.setItem('reportSettings', JSON.stringify(data));
            setReportSettings(data);
            // console.log(reportSettings);
            // console.log('Report settings Data : ', data);
          }
        } catch (error) {
          console.error('Error fetching report settings:', error);
        }
      };

      const fetchYearlyData = async () => {
        try {
          // console.log(selectedYear);
          const yearlyData = await fetch(
            `${config.baseUrl}/reports/${userId}/year/${selectedYear}`,
          );
          const data = await yearlyData.json();
          // console.log('Yearly data:', data);
          setTotalBeforeDetection(data.beforeDetaction);
          setTotalAfterDetection(data.afterDetaction);
          setTotalTip(data.tip);
          setTotalKastrup(data.kastrupAirport);
          setTotalStation(data.stationCH);
          setTotalSturup(data.sturupAirport);
          return data;
        } catch (error) {
          console.error('Error fetching yearly data:', error);
        }
      };

      fetchUserId();
      fetchReportSettings();
      fetchYearlyData();
    }, [userId, selectedYear]),
  );

  // Function to generate a unique file name
  const generateUniqueFileName = async (baseName, extension, directory) => {
    const timestamp = new Date().getTime();
    const uniqueFileName = `${baseName}_${timestamp}.${extension}`;
    return `${directory}/${uniqueFileName}`;
  };
  const generatePDF = async () => {
    try {
      // Request permission to write to storage (Android)
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          {
            title: 'File Access Permission',
            message: 'This app needs access to your files to share reports.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
      }

      // Capture the graph as an image using the ref
      const capturedImageUri = await captureRef(graphRef, {
        format: 'png', // Format the image as PNG
        quality: 0.8, // Adjust quality of the image
      });
      // console.log('Captured image URI:', capturedImageUri);

      // Define the directory path for saving the image
      const directoryPath = RNFS.DocumentDirectoryPath;
      const timestamp = new Date().getTime(); // Generate unique timestamp
      const newImagePath = `${directoryPath}/graphImage_${timestamp}.png`;

      // Move the captured image to the correct location
      await RNFS.moveFile(capturedImageUri, newImagePath);
      // console.log('Image moved to:', newImagePath);

      // Generate unique file name for the PDF
      const baseFileName = 'report';
      const pdfFilePath = await generateUniqueFileName(
        baseFileName,
        'pdf',
        RNFS.DocumentDirectoryPath,
      ); // Get full path for the file

      // Ensure fileName only contains the name, not the path
      const fileName = pdfFilePath.split('/').pop(); // Extract the filename from the full path
      // console.log('Generated file name for PDF:', fileName);

      // Simple HTML Content for PDF with the updated image path
      const htmlContent = `
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; margin: 10px; padding: 0; }
        .header { text-align: center; font-size: 18px; }
        h2 {text-align: center;}
        .content { width: 100%; max-width: 800px; margin: auto; }
        img { width: 100%; max-height: 65vh; object-fit: contain; }
        .section { margin: 5px 0; display: flex; flex-direction: column; align-items: center; }
        .input-container { display: flex; justify-content: space-between;  width: 100%; max-width: 90%;  margin-top: 10px; textAlign: center; }
        .input-item { width: 80%; margin: 2px 8px;  }
        .input-item label { font-weight: bold; }
        .input-item span { font-weight: bold; }
    </style>
</head>
<body>
    <div class="content">
        <div class="header">
            <h1>${t('graphAndYearlyData')}</h1>
        </div>
        <div class="section">
            <h2>${t('graphData')}</h2>
            <img src="file://${newImagePath}" />
        </div>
        <div class="section">
            <h2>${t('yearlyData')}</h2>
            <div class="input-container">
                <div class="input-item">
                    <label>${t('TotalBefore')}:</label>
                    <span>${
                      totalBeforeDetection ? totalBeforeDetection : '0'
                    }</span>
                </div>
                <div class="input-item">
                    <label>${t('TotalAfter')}:</label>
                    <span>${
                      totalAfterDetection ? totalAfterDetection : '0'
                    }</span>
                </div>
            </div>
            <div class="input-container">
                ${
                  reportSettings.tip?.isActive
                    ? `<div class="input-item">
                    <label>${t('TotalTip')}:</label>
                    <span>${totalTip ? totalTip : '0'}</span>
                </div>`
                    : ''
                }
                ${
                  reportSettings.kastrupAirport?.isActive
                    ? `<div class="input-item">
                    
                      <label>${t('TotalKastrup')}:</label>
                    <span>${totalKastrup ? totalKastrup : '0'}</span>
                </div>`
                    : ''
                }
            </div>
            <div class="input-container">
                ${
                  reportSettings.stationCH?.isActive
                    ? `<div class="input-item">
                    <label>${t('TotalStation')}:</label>
                    <span>${totalStation ? totalStation : '0'}</span>
                </div>`
                    : ''
                }
                ${
                  reportSettings.sturupAirport?.isActive
                    ? `<div class="input-item">
                    <label>${t('TotalSturup')}:</label>
                    <span>${totalSturup ? totalSturup : '0'}</span>
                </div>`
                    : ''
                }
            </div>
        </div>
    </div>
</body>
</html>
`;

      const options = {
        html: htmlContent,
        fileName: fileName,
        directory: 'Documents',
        // Ensure A4 dimensions to prevent page overflow
        width: 595, // A4 width in points
        height: 842, // A4 height in points
        base64: false,
      };

      const document = await RNHTMLtoPDF.convert(options);
      // console.log('Generated PDF file:', document.filePath);

      // Move the PDF to the destination path if necessary
      const destinationPath = await generateUniqueFileName(
        baseFileName,
        'pdf',
        directoryPath,
      );
      await RNFS.moveFile(document.filePath, destinationPath);

      // Share the generated PDF
      const shareOptions = {
        title: 'Share Report PDF',
        url: `file://${destinationPath}`,
        type: 'application/pdf',
        message: 'Here is your report!',
      };
      await Share.open(shareOptions);
    } catch (error) {
      console.log('Error generating or sharing PDF:', error);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <KeyboardAvoidingView behavior="padding" style={styles.container}>
        <View style={styles.navBar}>
          <View style={styles.logo}>
            <View style={styles.logoImageContainer}>
              <CustomImage />
            </View>
            <CustomMenu />
          </View>
          <CustomProfileDetails />
        </View>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.graphContainer}>
            {loading ? (
              <Text>Loading user ID...</Text>
            ) : userId ? (
              <ViewShot ref={graphRef} options={{format: 'png', quality: 0.9}}>
                <CustomgraphView
                  selectedYear={selectedYear}
                  setSelectedYear={setSelectedYear}
                  _id={userId}
                />
              </ViewShot>
            ) : (
              <ActivityIndicator size="large" color={textcolor.color2} />
            )}
          </View>
          <View style={styles.inputContainer}>
            <View style={styles.leftContainer}>
              <Custominput
                title={t('TotalBefore')}
                value={
                  totalBeforeDetection ? String(totalBeforeDetection) : '0'
                }
                bordered={true}
                editable={false}
              />
              <Custominput
                title={t('TotalAfter')}
                value={totalAfterDetection ? String(totalAfterDetection) : '0'}
                bordered={true}
                editable={false}
              />
              {reportSettings.tip?.isActive && (
                <Custominput
                  title={t('TotalTip')}
                  value={totalTip ? String(totalTip) : '0'}
                  bordered={true}
                  editable={false}
                />
              )}
            </View>
            <View style={styles.rightContainer}>
              {reportSettings.kastrupAirport?.isActive && (
                <Custominput
                  title={t('TotalKastrup')}
                  value={totalKastrup ? String(totalKastrup) : '0'}
                  bordered={true}
                  editable={false}
                />
              )}
              {reportSettings.stationCH?.isActive && (
                <Custominput
                  title={t('TotalStation')}
                  value={totalStation ? String(totalStation) : '0'}
                  bordered={true}
                  editable={false}
                />
              )}
              {reportSettings.sturupAirport?.isActive && (
                <Custominput
                  title={t('TotalSturup')}
                  value={totalSturup ? String(totalSturup) : '0'}
                  bordered={true}
                  editable={false}
                />
              )}
            </View>
          </View>
          <TouchableOpacity style={styles.dotWithText} onPress={generatePDF}>
            <Text style={styles.shareText}>{t('Share')} </Text>
            <FontAwesomeIcon icon={faShareNodes} color="#000000" size={20} />
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: 'white',
  },
  container: {
    flex: 1,
    justifyContent: 'center', // Center content vertically
    alignItems: 'center', // Center content horizontally
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center', // Center content vertically
    alignItems: 'center', // Center content horizontally
    width: '100%', // Take full width
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%', // Take full width
    paddingHorizontal: 4, // Add some padding
  },
  logo: {
    justifyContent: 'space-around',
  },
  logoImageContainer: {
    marginTop: 10,
    marginBottom: '-65%',
  },
  graphContainer: {
    flex: 2,
    width: '100%', // Take full width
    alignItems: 'center', // Center content horizontally
    marginTop: 16, // Add some margin
  },
  inputContainer: {
    flexDirection: 'row',
    // justifyContent: 'space-between', // Space out left and right containers
    flex: 1,
    width: '100%', // Take 90% of the width
    marginTop: 16, // Add some margin
  },
  rightContainer: {
    // width: '50%', // Take 48% of the width
  },
  leftContainer: {
    // width: '50%', // Take 48% of the width
  },
  dotWithText: {
    paddingVertical: 5,
    marginTop: 16, // Add some margin
    flexDirection: 'row',
    alignSelf: 'center',
    alignItems: 'center',
    // backgroundColor: '#F1C50D',
    borderRadius: 18,
    paddingHorizontal: 10,
    // borderWidth: 1,
    // borderColor: '#000',
  },
  shareText: {
    color: '#000',
    fontWeight: 'bold',
    fontSize: 14,
  },
});

export default ViewGraph;
