import {
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  Text,
  View,
  Platform,
  Image,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import BackButton from '../components/BackButton';
import CustomProfileDetails from '../components/CustomProfileDetails';

import CustomButton from '../components/CustomButton';
import {textcolor} from '../styles/style';
import {useNavigation} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ActivityIndicator, Alert} from 'react-native';
import reportSettingService from '../services/features/reportSetting/reportSetting.service';
import {config} from '../../config';
import CustomSwitchWithLabel from '../components/CustomSwitchWithLabel';
import CustomSwitchWithInputAndLabel from '../components/CustomSwitchWithInputAndLabel';

const RapportSetting = () => {
  const navigation = useNavigation();
  const {t} = useTranslation();

  const [loading, setLoading] = useState(true);
  const [reportSettings, setReportSettings] = useState({
    total: {isActive: false},
    cash: {isActive: false},
    card: {isActive: false},
    prePaid: {isActive: false},
    FTJ: {isActive: false},
    SKOL: {isActive: false},
    tip: {isActive: false},
    kastrupAirport: {isActive: false, value: ''},
    stationCH: {isActive: false, value: ''},
    sturupAirport: {isActive: false, value: ''},
    extraBill: {isActive: false},
    tax: {isActive: false, value: ''},
    holiday: {isActive: false, value: ''},
    driverPercentage: {isActive: false, value: ''},
    ownerPercentage: {isActive: false, value: ''},
  });

  const [userId, setUserId] = useState(null);

  // // Fetch user ID from AsyncStorage
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const user = await AsyncStorage.getItem('User');
        const parsedUser = user ? JSON.parse(user) : null;
        setUserId(parsedUser?._id || null);
        // console.log(userId);
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };
    fetchUserData();
  }, []);

  useEffect(() => {
    const fetchReportSettings = async () => {
      setLoading(true);
      try {
        // Check for existing report settings in AsyncStorage
        const storedSettings = await AsyncStorage.getItem('reportSettings');
        // console.log(storedSettings);
        if (storedSettings) {
          // If found, parse and set the report settings from AsyncStorage
          const parsedSettings = JSON.parse(storedSettings);
          setReportSettings(prevSettings => ({
            ...prevSettings,
            ...parsedSettings,
          }));
        } else {
          // If not found, make the API call
          // console.log(userId)
          const response = await fetch(
            `${config.baseUrl}/auth/report-setting/${userId}`,
          );
          const data = await response.json();
          // console.log("data :", data);
          //  await AsyncStorage.setItem('reportSettings', JSON.stringify(payload))
          await AsyncStorage.setItem('reportSettings', JSON.stringify(data));
          // console.log(data);
          // Update state with the fetched report settings
          setReportSettings(prevSettings => ({
            ...prevSettings,
            ...data,
          }));
        }
      } catch (error) {
        console.error(
          'Error fetching report settings from AsyncStorage or API:',
          error,
        );
        Alert.alert(
          'Error',
          'Failed to load report settings from server or storage.',
        );
      } finally {
        setLoading(false);
      }
    };

    fetchReportSettings();
  }, [userId]); // Add userId as a dependency to refetch when userId changes

  // Handle saving report settings
  const handleReportSetting = async () => {
    setLoading(true);
    try {
      const payload = {...reportSettings}; // Use the current state directly

      // Convert string values to numbers for specific fields
      const fieldsToConvert = [
        'kastrupAirport',
        'stationCH',
        'sturupAirport',
        'tax',
        'holiday',
        'driverPercentage',
        'ownerPercentage',
      ];
      fieldsToConvert.forEach(field => {
        payload[field].value = Number(payload[field].value) || 0; // Default to 0 if NaN
      });

      // Save settings to AsyncStorage
      await AsyncStorage.setItem('reportSettings', JSON.stringify(payload));

      // Call the API with the current userId and payload
      if (userId) {
        await reportSettingService.reportSetting(userId, payload);
      } else {
        console.error('User ID is not available');
        Alert.alert('Error', 'User ID is missing. Cannot save settings.');
      }

      // console.log('userid :', userId);
      // console.log("Payload : ", payload);
      navigation.navigate('Bottomtab');
    } catch (error) {
      console.error('Error saving report settings:', error);
      Alert.alert('Error', 'Failed to save report settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}>
        <View style={styles.navBar}>
          <View style={styles.logoContainer}>
            <View>
              <Image
                source={require('../assets/Image/logo.png')}
                style={styles.logo}></Image>
            </View>
            <BackButton />
          </View>
          <CustomProfileDetails />
        </View>
        <Text style={styles.text}>{t('Reportsetting')}</Text>
        <ScrollView
          contentContainerStyle={styles.container}
          keyboardShouldPersistTaps="handled">
          <View style={styles.formContainer}>
            <CustomSwitchWithLabel
              label={t('Total')}
              onValueChange={value =>
                setReportSettings(prev => ({...prev, total: {isActive: value}}))
              }
              initialEnabled={reportSettings?.total?.isActive}
              value={reportSettings?.total?.isActive}
            />
            <CustomSwitchWithLabel
              label={t('Cash')}
              onValueChange={value =>
                setReportSettings(prev => ({...prev, cash: {isActive: value}}))
              }
              initialEnabled={reportSettings?.cash?.isActive}
              value={reportSettings?.cash?.isActive}
            />
            <CustomSwitchWithLabel
              label={t('Card')}
              onValueChange={value =>
                setReportSettings(prev => ({...prev, card: {isActive: value}}))
              }
              initialEnabled={reportSettings?.card?.isActive}
              value={reportSettings?.card?.isActive}
            />
            <CustomSwitchWithLabel
              label={t('PrePaid')}
              onValueChange={value =>
                setReportSettings(prev => ({
                  ...prev,
                  prePaid: {isActive: value},
                }))
              }
              initialEnabled={reportSettings?.prePaid?.isActive}
              value={reportSettings?.prePaid?.isActive}
            />
            <CustomSwitchWithLabel
              label="FTJ"
              onValueChange={value =>
                setReportSettings(prev => ({...prev, FTJ: {isActive: value}}))
              }
              initialEnabled={reportSettings?.FTJ?.isActive}
              value={reportSettings?.FTJ?.isActive}
            />
            <CustomSwitchWithLabel
              label="SKOL"
              onValueChange={value =>
                setReportSettings(prev => ({...prev, SKOL: {isActive: value}}))
              }
              initialEnabled={reportSettings?.SKOL?.isActive}
              value={reportSettings?.SKOL?.isActive}
            />
            <CustomSwitchWithLabel
              label={t('Tip')}
              onValueChange={value =>
                setReportSettings(prev => ({...prev, tip: {isActive: value}}))
              }
              initialEnabled={reportSettings?.tip?.isActive}
              value={reportSettings?.tip?.isActive}
            />
            <CustomSwitchWithLabel
              label={t('Extra')}
              onValueChange={value =>
                setReportSettings(prev => ({
                  ...prev,
                  extraBill: {isActive: value},
                }))
              }
              initialEnabled={reportSettings?.extraBill?.isActive}
              value={reportSettings?.extraBill?.isActive}
            />
            <CustomSwitchWithInputAndLabel
              label={t('Kastrup')}
              value={reportSettings?.kastrupAirport?.isActive}
              inputValue={reportSettings?.kastrupAirport?.value?.toString()}
              // initialEnabled={reportSettings.kastrupAirport.value.toString()}
              onValueChange={(isActive, value) =>
                setReportSettings(prev => ({
                  ...prev,
                  kastrupAirport: {isActive, value},
                }))
              }
              keyboardType="number-pad"
            />
            <CustomSwitchWithInputAndLabel
              label={t('Station')}
              value={reportSettings?.stationCH?.isActive}
              inputValue={reportSettings?.stationCH?.value?.toString()}
              // initialEnabled={reportSettings.stationCH.value.toString()}
              onValueChange={(isActive, value) =>
                setReportSettings(prev => ({
                  ...prev,
                  stationCH: {isActive, value},
                }))
              }
              keyboardType="number-pad"
            />
            <CustomSwitchWithInputAndLabel
              label={t('Sturup')}
              value={reportSettings?.sturupAirport?.isActive}
              inputValue={reportSettings?.sturupAirport?.value?.toString()}
              // initialEnabled={reportSettings.sturupAirport.value.toString()}
              onValueChange={(isActive, value) =>
                setReportSettings(prev => ({
                  ...prev,
                  sturupAirport: {isActive, value},
                }))
              }
              keyboardType="number-pad"
            />
            <CustomSwitchWithInputAndLabel
              label={t('Tax')}
              value={reportSettings?.tax?.isActive}
              inputValue={reportSettings?.tax?.value?.toString()}
              // initialEnabled={reportSettings.tax.value.toString()}
              onValueChange={(isActive, value) =>
                setReportSettings(prev => ({
                  ...prev,
                  tax: {isActive, value},
                }))
              }
              keyboardType="number-pad"
            />
            <CustomSwitchWithInputAndLabel
              label={t('Holiday')}
              value={reportSettings?.holiday?.isActive}
              inputValue={reportSettings?.holiday?.value?.toString()}
              // initialEnabled={reportSettings.holiday.value.toString()}
              onValueChange={(isActive, value) =>
                setReportSettings(prev => ({
                  ...prev,
                  holiday: {isActive, value},
                }))
              }
              keyboardType="number-pad"
            />
            <CustomSwitchWithInputAndLabel
              label={t('DriverPercentage')}
              value={reportSettings?.driverPercentage?.isActive}
              inputValue={reportSettings?.driverPercentage?.value?.toString()}
              // initialEnabled={reportSettings.percentage.value.toString()}
              onValueChange={(isActive, value) =>
                setReportSettings(prev => ({
                  ...prev,
                  driverPercentage: {isActive, value},
                }))
              }
              keyboardType="number-pad"
            />
            <CustomSwitchWithInputAndLabel
              label={t('OwnerPercentage')}
              value={reportSettings?.ownerPercentage?.isActive}
              inputValue={reportSettings?.ownerPercentage?.value?.toString()}
              // initialEnabled={reportSettings.percentage.value.toString()}
              onValueChange={(isActive, value) =>
                setReportSettings(prev => ({
                  ...prev,
                  ownerPercentage: {isActive, value},
                }))
              }
              keyboardType="number-pad"
            />
          </View>
          <View style={styles.saveButtonContainer}>
            {loading ? (
              <ActivityIndicator size="large" color={textcolor.color2} />
            ) : (
              <CustomButton title={t('Save')} onPress={handleReportSetting} />
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  logoContainer: {
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    gap: 20,
    marginLeft: '4%',
  },
  logo: {
    width: 110,
    height: 55,
    // marginBottom: '2%',
  },
  container: {
    paddingBottom: 20,
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  text: {
    fontSize: 22,
    fontWeight: '700',
    color: textcolor.color2,
    alignSelf: 'center',
    marginTop: -17,
  },
  formContainer: {
    marginVertical: 20,
    paddingHorizontal: 25,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  saveButtonContainer: {
    marginVertical: 15,
    paddingHorizontal: 30,
  },
});

export default RapportSetting;
