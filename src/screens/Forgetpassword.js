import {
  ScrollView,
  StyleSheet,
  Text,
  View,
  KeyboardAvoidingView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import React, {useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import CustomImage from '../components/CustomImage';
import CustomTextinput from '../components/CustomTextinput';
import {buttoncolor, textcolor} from '../styles/style';
import CustomButton from '../components/CustomButton';
import {useTranslation} from 'react-i18next';
import AuthService from '../services/features/auth/auth.service';
import AsyncStorage from '@react-native-async-storage/async-storage';

const Forgetpassword = ({navigation}) => {
  // const [selectedRadio, setselectedRadio] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const {t} = useTranslation();
  const handleForgetPassword = async () => {
    if (!email.trim()) {
      Alert.alert('Error', 'Email is required !!');
      return;
    }
    setLoading(true);
    try {
      const payload = {email};
      const response = await AuthService.forgetPassword(payload);
      const user = JSON.stringify(response.user);
      await AsyncStorage.setItem('User', user);
      Alert.alert('OTP sent to you email !!');
      navigation.navigate('Verification', {componentType: 'forgetPassword'});
    } catch (error) {
      Alert.alert('Email not Found', 'Enter a valid Email !!');
    } finally {
      setLoading(false);
    }
  };
  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior="padding"
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}>
        <ScrollView contentContainerStyle={{flexGrow: 1, marginTop: 20}}>
          <CustomImage header={t('forgotPass')} />
          <View style={{marginTop: 30}}>
            <CustomTextinput
              label={t('email')}
              placeholder={t('enteremail')}
              value={email}
              onChangeText={setEmail}
            />
            {/* <TouchableOpacity onPress={() => setselectedRadio(0)}>
              <View style={styles.boxcontainer}>
                <View style={styles.box}>
                  {selectedRadio === 0 ? (
                    <View style={styles.innerbox}></View>
                  ) : null}
                </View>
                <Text style={styles.checktext}>{t('checkEmail')}</Text>
              </View>
            </TouchableOpacity> */}
          </View>

          <View style={{marginTop: 20}}>
            {loading ? (
              <ActivityIndicator size="large" color={textcolor.color2} />
            ) : (
              <CustomButton title={t('send')} onPress={handleForgetPassword} />
            )}

            <View style={styles.bottom}>
              <Text style={styles.text}>{t('areNew')} </Text>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate('Signup');
                }}>
                <Text style={styles.text2}>{t('createAccount')}</Text>
              </TouchableOpacity>
            </View>
          </View>
          <TouchableOpacity
            style={{marginTop: -15}}
            onPress={() => {
              navigation.navigate('SignIn');
            }}>
            <Text style={styles.text3}>{t('backtoSignin')}</Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Forgetpassword;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  boxcontainer: {
    flexDirection: 'row',
    marginHorizontal: 22,
    marginVertical: 20,
  },
  box: {
    height: 22,
    width: 22,
    borderRadius: 20,
    backgroundColor: buttoncolor.color,
  },
  innerbox: {
    backgroundColor: buttoncolor.bgcolor,
    height: 20,
    width: 20,
    borderRadius: 10,
  },
  checktext: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color1,
    alignSelf: 'flex-end',
    marginLeft: 7,
  },
  bottom: {
    flexDirection: 'row',
    alignSelf: 'center',
    marginTop: 30,
    marginBottom: 30,
  },

  text: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color1,
  },
  text2: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color2,
  },
  text3: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color2,
    alignSelf: 'center',
  },
});
