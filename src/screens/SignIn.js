import React, { useState, useEffect } from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  View,
  KeyboardAvoidingView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import CustomImage from '../components/CustomImage';
import CustomTextinput from '../components/CustomTextinput';
import { textcolor } from '../styles/style';
import CustomButton from '../components/CustomButton';
import { useTranslation } from 'react-i18next';
import AuthService from '../services/features/auth/auth.service';
import AsyncStorage from '@react-native-async-storage/async-storage';
import LanguageSelector from '../Language/LanguageSelector';

const { width, height } = Dimensions.get('window');
const SignIn = ({ navigation }) => {
  const { t, i18n } = useTranslation();

  // State for email and password
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  // Function to load language preference from AsyncStorage
  const loadLanguagePreference = async () => {
    try {
      const language = await AsyncStorage.getItem('language');
      if (language) {
        i18n.changeLanguage(language); // Change the language if found in AsyncStorage
      } else {
        // Default to 'sv' if no language is found
        i18n.changeLanguage('sv');
      }
    } catch (error) {
      console.error('Error loading language preference:', error);
    }
  };

  // UseEffect to load language preference on initial render
  useEffect(() => {
    loadLanguagePreference();
  }, []);

  const handleLogin = async () => {
    setLoading(true); 
    if (!email && !password) {
      Alert.alert(
        'Credentials Required',
        'Please enter both your email and password to continue.'
      );
      setLoading(false);
      return;
    } else if (!email) {
      Alert.alert(
        'Email Required',
        'Please enter your email address.'
      );
      setLoading(false);
      return;
    } else if (!password) {
      Alert.alert(
        'Password Required',
        'Please enter your password.'
      );
      setLoading(false);
      return;
    }
    try {
      const payload = { email: email.trim(), password };
      const response = await AuthService.login(payload); // Call the AuthService API

      if (!response.data.success) {
        // Send OTP if the user isn't verified
        await AsyncStorage.setItem(
          'User',
          JSON.stringify({ _id: response.data.userId }),
        );
        Alert.alert(
          'Verification Needed',
          `OTP has been sent to your email: ${payload.email}`,
          [
            {
              text: 'OK',
              onPress: () =>
                navigation.navigate('Verification', { ComponentType: 'Signin' }),
            },
          ],
        );
      } else {
        // Successful login
        if (response.data.success) {
          await AsyncStorage.setItem(
            'User',
            JSON.stringify(response?.data?.user),
          );
          await AsyncStorage.setItem('token', response.data.token);

          // Get user's preferred language (assuming it's part of the user data)
          const userLanguage = response?.data?.user?.language || 'sv'; // Default to 'sv' if no language is set

          // Save the selected language to AsyncStorage
          await AsyncStorage.setItem('language', userLanguage);

          // Change language in i18next
          i18n.changeLanguage(userLanguage);

          // Navigate to the home screen (or SplashScreen)
          navigation.navigate('Splashscreen');
          setEmail('');
          setPassword('');
        }
      }
    } catch (error) {
      //console.error('Login Error:', error);
      Alert.alert(
        'Login Failed',
        error?.message ||'We couldn’t find your account. Please contact support or create a new account.'
     );

    } finally {
      setLoading(false); // Stop loading indicator
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior="padding"
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
        style={{ flex: 1 }}>
        <ScrollView
          contentContainerStyle={{ flexGrow: 1, }}>
          <View style={{ width: '40%', marginTop: 20, alignSelf: 'flex-end' }}>
            <LanguageSelector />
          </View>
          <View style={{ marginTop: height * 0.14 }}>
            <CustomImage header={t('letsgetsStarted')} />
          </View>

          <View style={{ marginTop: 15 }}>
            <View style={{ marginBottom: 14 }}>
              <CustomTextinput
                label={t('email')}
                placeholder={t('enterEmail')}
                value={email}
                onChangeText={setEmail}
              />
            </View>
            <CustomTextinput
              label={t('password')}
              placeholder={t('enterPassword')}
              secureTextEntry={true}
              value={password}
              onChangeText={setPassword}
            />
            <TouchableOpacity
              style={{ marginVertical: 15 }}
              onPress={() => {
                navigation.navigate('Forgetpassword');
              }}>
              <Text style={styles.forgettext}>{t('forgotPass')}</Text>
            </TouchableOpacity>

            <View style={{ marginTop: 20 }}>
              {loading ? (
                <ActivityIndicator size="large" color={textcolor.color2} />
              ) : (
                <CustomButton title={t('signIn')} onPress={handleLogin} />
              )}
              <View style={styles.bottom}>
                <Text style={styles.text}>{t('areNew')}</Text>
                <TouchableOpacity
                  onPress={() => {
                    navigation.navigate(t('Signup'));
                  }}>
                  <Text style={styles.text2}>{t('createAccount')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default SignIn;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  forgettext: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color1,
    alignSelf: 'flex-end',
    marginHorizontal: 30,
  },
  bottom: {
    flexDirection: 'row',
    alignSelf: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  text: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color1,
  },
  text2: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color2,
  },
});
