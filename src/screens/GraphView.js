import React, {useState} from 'react';
import {
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  View,
  Alert,
  Text,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useFocusEffect} from '@react-navigation/native';
import CustomProfileDetails from '../components/CustomProfileDetails';
import CustomImage from '../components/CustomImage';

import CustomMenu from '../components/CustomMenu';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CustomGraph from '../components/CustomGraph';
import BackButton from '../components/BackButton';
import {textcolor} from '../styles/style';

const GraphView = () => {
  const [userId, setUserId] = useState(null);
  const [loading, setLoading] = useState(true);

  useFocusEffect(
    React.useCallback(() => {
      const fetchUserId = async () => {
        try {
          const user = await AsyncStorage.getItem('User');
          const parsedUser = user ? JSON.parse(user) : null;
          const id = parsedUser ? parsedUser._id : null;

          if (!id) {
            Alert.alert('Error', 'User ID not found');
            return;
          }

          setUserId(id);
        } catch (error) {
          Alert.alert('Error', 'Failed to fetch user ID');
          console.error('Error fetching user ID:', error);
        } finally {
          setLoading(false);
        }
      };

      fetchUserId();
    }, []),
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <KeyboardAvoidingView behavior="padding" style={styles.container}>
        <View style={styles.navBar}>
          <View style={styles.logo}>
            <View style={styles.logoImageContainer}>
              <CustomImage />
            </View>
            <CustomMenu />
            <View style={{marginLeft: 10, marginTop: 10}}>
              <BackButton />
            </View>
          </View>
          <View>
            <CustomProfileDetails />
          </View>
        </View>

        <ScrollView
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}>
          {loading ? (
            <Text>Loading user ID...</Text>
          ) : userId ? (
            <CustomGraph days={30} _id={userId} />
          ) : (
            <ActivityIndicator size="large" color={textcolor.color2} />
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: 'white',
  },
  container: {
    flex: 1,
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  logoImageContainer: {
    marginTop: 10,
    marginBottom: '-65%',
  },
  scrollViewContent: {},
});

export default GraphView;
