import {
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  Text,
  View,
  Platform,
  SafeAreaView,
  PermissionsAndroid,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import style, {buttoncolor, textcolor} from '../styles/style';
import CustomProfileDetails from '../components/CustomProfileDetails';
import CustomImage from '../components/CustomImage';
import Custominput from '../components/Custominput';
import CustomButton from '../components/CustomButton';
import CustomMenu from '../components/CustomMenu';
import CustomTable from '../components/CustomTable';
import CustomNote from '../components/CustomNote';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useFocusEffect} from '@react-navigation/native';
import {config} from '../../config';
import RNFS from 'react-native-fs';
import RNHTMLtoPDF from 'react-native-html-to-pdf';
import Share from 'react-native-share';
import {useAppContext} from '../context/ContextProvider';
import {TouchableOpacity} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
  faTrashCan,
  faChartLine,
  // faShareFromSquare,
  faShareNodes,
} from '@fortawesome/free-solid-svg-icons';

import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import {GestureDetector, Gesture} from 'react-native-gesture-handler';

const ViewReportScreen = ({navigation}) => {
  const {reportData: reportDataFromContext} = useAppContext();
  const {t} = useTranslation();
  const [loading, setLoading] = useState(true); // State to handle loading
  const [userId, setUserId] = useState(null);
  const [driverName, setDriverName] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [taxiNumber, setTaxiNumber] = useState('');
  const [companyLogo, setCompanyLogo] = useState(null);
  const [monthYear, setMonthYear] = useState(null); // New state to store MM-YYYY format
  const [tableRefreshKey, setTableRefreshKey] = useState(0); // Key for table refresh
  const [totals, setTotals] = useState({
    cash: 0,
    prePaid: 0,
    card: 0,
    tip: 0,
    kastrupAirport: 0,
    stationCH: 0,
    sturupAirport: 0,
    extraBill: 0,
    beforeDetaction: 0,
    afterDetaction: 0,
  });
  const [driverPercentageValue, setDriverPercentageValue] = useState(0);
  const [driverCalculatedPercentage, setDriverCalculatedPercentage] =
    useState(0);
  const [ownerPercentageValue, setOwnerPercentageValue] = useState(0);
  const [ownerCalculatedPercentage, setOwnerCalculatedPercentage] = useState(0);
  const [holidayPayValue, setHolidayPayValue] = useState(0);
  const [calculatedHolidayPay, setCalculatedHolidayPay] = useState(0);
  const [kastrupAirportValue, setKastrupAirportValue] = useState(0);
  const [stationCHValue, setStationCHValue] = useState(0);
  const [sturupAirportValue, setSturupAirportValue] = useState(0);
  const [finalCash, setFinalCash] = useState(0);
  const [beforeTaxTotal, setBeforeTaxTotal] = useState(0);
  const [tax, setTax] = useState(0);
  const [afterTaxTotal, setAfterTaxTotal] = useState(0);
  const [driverIncome, setDriverIncome] = useState(0);
  const [reportData, setReportData] = useState([]);
  const {height} = Dimensions.get('window');
  const [reportSettings, setReportSettings] = useState({
    total: {isActive: false},
    cash: {isActive: false},
    card: {isActive: false},
    prePaid: {isActive: false},
    FTJ: {isActive: false},
    SKOL: {isActive: false},
    tip: {isActive: false},
    kastrupAirport: {isActive: false},
    stationCH: {isActive: false},
    sturupAirport: {isActive: false},
    extraBill: {isActive: false},
    holiday: {isActive: false},
    driverPercentage: {isActive: false},
    ownerPercentage: {isActive: false},
    tax: {isActive: false},
  });

  const scale = useSharedValue(1); // Default scale is 1
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);

  // Pinch gesture handler
  const pinchGesture = Gesture.Pinch()
    .onUpdate(event => {
      scale.value = event.scale; // Update scale as user pinches
    })
    .onEnd(() => {
      // Restrict zoom level (1x - 3x)
      scale.value = withTiming(Math.max(1, Math.min(scale.value, 3)));
    });

  // Pan gesture handler
  const panGesture = Gesture.Pan()
    .onUpdate(event => {
      if (scale.value > 1) {
        // Allow panning only when zoomed in
        translateX.value = event.translationX;
        translateY.value = event.translationY;
      }
    })
    .onEnd(() => {
      // Add boundaries to prevent moving out of the view
      const maxTranslateX = (scale.value - 1) * 100; // Adjust boundary values
      const maxTranslateY = (scale.value - 1) * 100;

      translateX.value = withTiming(
        Math.max(-maxTranslateX, Math.min(translateX.value, maxTranslateX)),
      );
      translateY.value = withTiming(
        Math.max(-maxTranslateY, Math.min(translateY.value, maxTranslateY)),
      );
    });

  // Double-tap gesture handler
  const doubleTapGesture = Gesture.Tap()
    .numberOfTaps(2)
    .onEnd(() => {
      scale.value = withTiming(1); // Reset zoom to default on double tap
    });

  // Combine gestures
  const gestures = Gesture.Simultaneous(
    Gesture.Race(pinchGesture, doubleTapGesture, panGesture),
  );

  // Animated style for scaling
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      {translateX: translateX.value},
      {translateY: translateY.value},
      {scale: scale.value},
    ],
  }));

  useEffect(() => {
    // Set the current month and year as the default date
    const currentDate = new Date();
    const formattedDate = `${
      currentDate.getMonth() + 1
    }-${currentDate.getFullYear()}`;
    setMonthYear(formattedDate);
  }, []);

  const handleFilteredReports = useCallback(reports => {
    setReportData(reports); // Store filtered reports
    console.log('reports in view reporty', reports);
  });

  const handleTotalsCalculated = useCallback(
    calculatedTotals => {
      setReportData(reportDataFromContext);
      // Check if the new totals are different from the current ones
      if (JSON.stringify(calculatedTotals) !== JSON.stringify(totals)) {
        setTotals(calculatedTotals);

        // Check if notes exist and contain at least one valid note
        if (
          calculatedTotals?.notes &&
          Array.isArray(calculatedTotals.notes) &&
          calculatedTotals.notes.length > 0 &&
          calculatedTotals.notes[0]?.date
        ) {
          const dateParts = calculatedTotals.notes[0].date.split('/');
          const noteDate = new Date(
            dateParts[2],
            dateParts[1] - 1,
            dateParts[0],
          ); // Parse the date correctly

          if (!isNaN(noteDate)) {
            const formattedMonthYear = `${String(
              noteDate.getMonth() + 1,
            ).padStart(2, '0')}-${noteDate.getFullYear()}`;
            setMonthYear(formattedMonthYear);
          } else {
            console.log('Invalid date format in notes');
          }
        } else {
          // Reset to current month and year if no valid notes
          const currentDate = new Date();
          const formattedDate = `${String(currentDate.getMonth() + 1).padStart(
            2,
            '0',
          )}-${currentDate.getFullYear()}`;
          setMonthYear(formattedDate);
        }
      }
    },
    [totals],
  );

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const user = await AsyncStorage.getItem('User');
        const parsedUser = JSON.parse(user) || {};
        setUserId(parsedUser._id);
        // console.log(parsedUser);
        setDriverName(parsedUser.name);
        setCompanyName(parsedUser.taxiCompany.companyName);
        setTaxiNumber(parsedUser.taxiNumber);
        setCompanyLogo(parsedUser.taxiCompany.companyLogo);
        // console.log(companyLogo);
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };
    fetchUserData();
  }, []);

  const fetchReportSettings = async () => {
    if (!userId) return;
    setLoading(true);

    try {
      const response = await fetch(
        `${config.baseUrl}/auth/report-setting/${userId}`,
      );
      const result = await response.json();
      // console.log(result);

      setDriverPercentageValue(result.driverPercentage.value);
      setOwnerPercentageValue(result.ownerPercentage.value);
      setHolidayPayValue(result.holiday.value);
      setKastrupAirportValue(result.kastrupAirport.value);
      setStationCHValue(result.stationCH.value);
      setSturupAirportValue(result.sturupAirport.value);

      // Update report settings as before
      setReportSettings({
        total: {isActive: result.total?.isActive || false},
        cash: {isActive: result.cash?.isActive || false},
        card: {isActive: result.card?.isActive || false},
        prePaid: {isActive: result.prePaid?.isActive || false},
        FTJ: {isActive: result.FTJ?.isActive || false},
        SKOL: {isActive: result.SKOL?.isActive || false},
        tip: {isActive: result.tip?.isActive || false},
        kastrupAirport: {isActive: result.kastrupAirport?.isActive || false},
        stationCH: {isActive: result.stationCH?.isActive || false},
        sturupAirport: {isActive: result.sturupAirport?.isActive || false},
        extraBill: {isActive: result.extraBill?.isActive || false},
        holiday: {isActive: result.holiday?.isActive || false},
        driverPercentage: {
          isActive: result.driverPercentage?.isActive || false,
        },
        ownerPercentage: {isActive: result.ownerPercentage?.isActive || false},
        tax: {isActive: result.tax?.isActive || false},
      });
    } catch (error) {
      console.error('Error fetching report data:', error);
    } finally {
      setLoading(false);
    }
  };

  // fetchReportSettings();

  useFocusEffect(
    useCallback(() => {
      // Check if userId exists before calling the fetch function
      if (userId) {
        fetchReportSettings();
      }
      // Clean-up function (optional, if needed)
      return () => {};
    }, [userId]), // Add userId as a dependency
  );

  useEffect(() => {
    // Update calculated percentage whenever totals or driverPercentageValue changes
    if (totals.afterDetaction && driverPercentageValue) {
      setDriverCalculatedPercentage(
        Number(
          ((totals.afterDetaction * driverPercentageValue) / 100).toFixed(1),
        ),
      );
    } else {
      setDriverCalculatedPercentage(0); // Reset if no afterDetaction
    }
  }, [totals, driverPercentageValue]); // Update dependency to include totals

  useEffect(() => {
    if (totals.afterDetaction && ownerPercentageValue) {
      setOwnerCalculatedPercentage(
        Number(
          ((totals.afterDetaction * ownerPercentageValue) / 100).toFixed(1),
        ),
      );
    } else {
      setOwnerCalculatedPercentage(0);
    }
  }, [totals, ownerPercentageValue]);

  useEffect(() => {
    if (driverCalculatedPercentage && holidayPayValue) {
      setCalculatedHolidayPay(
        Number(
          ((driverCalculatedPercentage * holidayPayValue) / 100).toFixed(1),
        ),
      );
    } else {
      setCalculatedHolidayPay(0);
    }
  }, [totals, holidayPayValue, driverCalculatedPercentage]);

  useEffect(() => {
    if (totals.cash !== undefined && totals.cash !== null) {
      setFinalCash(
        Number((totals.cash - (totals.extraBill + totals.tip)).toFixed(1)),
      );
    } else {
      setFinalCash(0);
    }
  }, [totals]);

  useEffect(() => {
    if (
      !reportSettings.percentage?.isActive &&
      !reportSettings.holiday?.isActive
    ) {
      setBeforeTaxTotal(Number((totals.afterDetaction || 0).toFixed(1)));
    } else if (!reportSettings.holiday?.isActive) {
      setBeforeTaxTotal(Number((driverCalculatedPercentage || 0).toFixed(1)));
    } else if (
      (calculatedHolidayPay ?? null) !== null || //Fix fallback system by verifying presence.
      (driverCalculatedPercentage ?? null) !== null
    ) {
      setBeforeTaxTotal(
        Number(
          (
            (calculatedHolidayPay || 0) + (driverCalculatedPercentage || 0)
          ).toFixed(1),
        ),
      );
    } else {
      setBeforeTaxTotal(0);
    }
  }, [
    totals,
    reportSettings,
    calculatedHolidayPay,
    driverCalculatedPercentage,
  ]);
  // includes all state vars above.

  useEffect(() => {
    if (tax && beforeTaxTotal) {
      setAfterTaxTotal(Number((beforeTaxTotal - tax).toFixed(1)));
    } else {
      setAfterTaxTotal(0);
    }
  }, [totals, tax, beforeTaxTotal]);

  useEffect(() => {
    if (
      afterTaxTotal !== undefined &&
      afterTaxTotal !== null &&
      finalCash !== undefined &&
      finalCash !== null
    ) {
      setDriverIncome(Number((afterTaxTotal - finalCash).toFixed(1)));
    } else {
      setDriverIncome(0);
    }
  }, [totals, afterTaxTotal, finalCash]);
  // const requestStoragePermission = async () => {
  //   try {
  //     const granted = await PermissionsAndroid.requestMultiple([
  //       PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
  //       PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
  //     ]);
  //     return (
  //       granted['android.permission.WRITE_EXTERNAL_STORAGE'] ===
  //         PermissionsAndroid.RESULTS.GRANTED &&
  //       granted['android.permission.READ_EXTERNAL_STORAGE'] ===
  //         PermissionsAndroid.RESULTS.GRANTED
  //     );
  //   } catch (err) {
  //     console.error('Failed to request permission', err);
  //     return false;
  //   }
  // };
  const generateUniqueFileName = async (baseName, extension, directory) => {
    let counter = 1;
    let fileName = `${baseName}.${extension}`;
    let destinationPath = `${directory}/${fileName}`;

    while (await RNFS.exists(destinationPath)) {
      fileName = `${baseName}[${counter}].${extension}`;
      destinationPath = `${directory}/${fileName}`;
      counter++;
    }

    return fileName;
  };

  const getDayOfWeek = dateString => {
    const [day, month, year] = dateString.split('/').map(Number);
    const date = new Date(year, month - 1, day);
    const dayIndex = date.getDay();
    const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thur', 'Fri', 'Sat'];
    return weekDays[dayIndex];
  };
  const formatDate = dateString => {
    return dateString.split('/')[0];
  };
  const generatePDF = async () => {
    // console.log('Generating PDF with reports:', reportData);

    try {
      if (Platform.OS === 'android') {
        try {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
            {
              title: 'File Access Permission',
              message: 'This app needs access to your files to share reports.',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            },
          );
          // if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          //   console.log('You can access files');
          // } else {
          //   console.log('File access permission denied');
          // }
        } catch (err) {
          console.warn(err);
        }
      }

      const directoryPath = RNFS.DocumentDirectoryPath; // Folder where the PDF will be saved
      const baseFileName = 'reports'; // Base name for the PDF file
      const htmlContent = ` 
      <html>
      <head>
          <style>
              body { font-family: Arial, sans-serif; font-size: 8px; }
              h1 { text-align: center; font-size: 18px; }
              .header-line {
              text-align: center; font-size: 18px; margin-bottom: 20px; }
              .header-item { display: inline-block; margin: 0 50px; }
              table { width: 100%; border-collapse: collapse; }
              th, td { border: 1px solid #000; padding: 3px; text-align: center; font-size: 13px; }
              th {  }
              .section-title { font-size: 18px; font-weight: bold; margin-top: 10px; text-align: center; }
              td { word-wrap: break-word; } /* Ensures notes wrap nicely */

          </style>
      </head>
      <body>
          <h1>${t('Report')}</h1>
          <div class="header-line">
            <span class="header-item">${driverName}</span>
            <span class="header-item">${companyName}</span>
            <span class="header-item">${taxiNumber}</span>
            <span class="header-item">
              <img src="${companyLogo}" alt="Company Logo" style="height: 40px; width: auto;" />
            </span>
         </div>
          <!-- Report Table -->
          <h2 class="section-title">Report Month:${monthYear} </h2>
        <table>
              <tr>
                  <th>${t('Date')}</th>
                  <th>${t('day')}</th>
                  ${
                    reportSettings.total?.isActive
                      ? `<th>${t('Total')}</th>`
                      : ''
                  }
                  ${
                    reportSettings.cash?.isActive ? `<th>${t('Cash')}</th>` : ''
                  }
                  ${
                    reportSettings.prePaid?.isActive
                      ? `<th>${t('Pre-Paid')}</th>`
                      : ''
                  }
                  ${
                    reportSettings.card?.isActive ? `<th>${t('Card')}</th>` : ''
                  }
                  ${reportSettings.tip?.isActive ? `<th>${t('Tip')}</th>` : ''}
                  ${
                    reportSettings.kastrupAirport?.isActive
                      ? `<th>${t('K/A')}</th>`
                      : ''
                  }
                  ${
                    reportSettings.stationCH?.isActive
                      ? `<th>${t('C/H')}</th>`
                      : ''
                  }
                  ${
                    reportSettings.sturupAirport?.isActive
                      ? `<th>${t('St/A')}</th>`
                      : ''
                  }
                  ${
                    reportSettings.extraBill?.isActive
                      ? `
                        <th>${t('Extra')}</th>
                  `
                      : ''
                  }
                  <th>${t('bdet')}</th>
                  <th>${t('Adet')}</th>
                  ${
                    reportSettings.ownerPercentage?.isActive
                      ? `<th>${t('Owner')}</th>`
                      : ''
                  }
                  ${
                    reportSettings.driverPercentage?.isActive
                      ? `<th>${t('Dper')}</th>`
                      : ''
                  }
              </tr>
              ${reportData
                .map(
                  row => `
                  <tr>
    <td>${String(formatDate(row.date)) || 'N/A'}</td>
    <td>${String(getDayOfWeek(row.date)) || 'N/A'}</td>
    ${
      reportSettings.total?.isActive ? `<td>${String(row.total || 0)}</td>` : ''
    }
    ${reportSettings.cash?.isActive ? `<td>${String(row.cash || 0)}</td>` : ''}
    ${
      reportSettings.prePaid?.isActive
        ? `<td>${String(row.prePaid || 0)}</td>`
        : ''
    }
    ${reportSettings.card?.isActive ? `<td>${String(row.card || 0)}</td>` : ''}
    ${reportSettings.tip?.isActive ? `<td>${String(row.tip || 0)}</td>` : ''}
    ${
      reportSettings.kastrupAirport?.isActive
        ? `<td>${String(row.kastrupAirport || 0)}</td>`
        : ''
    }
    ${
      reportSettings.stationCH?.isActive
        ? `<td>${String(row.stationCH || 0)}</td>`
        : ''
    }
    ${
      reportSettings.sturupAirport?.isActive
        ? `<td>${String(row.sturupAirport || 0)}</td>`
        : ''
    }
    ${
      reportSettings.extraBill?.isActive
        ? `<td>${String(row.extraBill || 0)}</td>`
        : ''
    }
    <td>${String(row.beforeDetaction || 0)}</td>
    <td>${String(row.afterDetaction || 0)}</td>
    ${
      reportSettings.driverPercentage?.isActive
        ? `<td>${String(row.driverPercentage || 0)}</td>`
        : ''
    }
    ${
      reportSettings.ownerPercentage?.isActive
        ? `<td>${String(row.ownerPercentage || 0)}</td>`
        : ''
    }
</tr>
              `,
                )
                .join('')}
          </table>
  
          <!-- Totals Section -->
          <h2 class="section-title">Totals</h2>
          <table>
              <tr>
                  <th>${t('TotalBefore')}</th>
                  <td>${String(totals.beforeDetaction)}</td>
              </tr>
              <tr>
                  <th>${t('TotalAfter')}</th>
                  <td>${String(totals.afterDetaction)}</td>
              </tr>
              ${
                reportSettings.tip?.isActive
                  ? `
                  <tr>
                      <th>${t('TotalTip')}</th>
                      <td>${String(totals.tip)}</td>
                  </tr>
              `
                  : ''
              }
              ${
                reportSettings.extraBill?.isActive
                  ? `
                  <tr>
                      <th>${t('TotalExtra')}</th>
                      <td>${String(totals.extraBill)}</td>
                  </tr>
              `
                  : ''
              }
              ${
                reportSettings.cash?.isActive
                  ? `
                  <tr>
                      <th>${t('TotalCash')}</th>
                      <td>${String(totals.cash)}</td>
                  </tr>
              `
                  : ''
              }
              ${
                reportSettings.prePaid?.isActive
                  ? `
                  <tr>
                      <th>${t('TotalPrepaid')}</th>
                      <td>${String(totals.prePaid)}</td>
                  </tr>
              `
                  : ''
              }
              ${
                reportSettings.SKOL?.isActive
                  ? `
                  <tr>
                      <th>${t('TotalSkol')}</th>
                      <td>${String(totals.SKOL)}</td>
                  </tr>
              `
                  : ''
              }
              ${
                reportSettings.FTJ?.isActive
                  ? `
                  <tr>
                      <th>${t('TotalFTJ')}</th>
                      <td>${String(totals.FTJ)}</td>
                  </tr>
              `
                  : ''
              }
              ${
                reportSettings.card?.isActive
                  ? `
                  <tr>
                      <th>${t('TotalCard')}</th>
                      <td>${String(totals.card)}</td>
                  </tr>
              `
                  : ''
              }
              ${
                reportSettings.kastrupAirport?.isActive
                  ? `
                  <tr>
                      <th>${t('TotalKastrup')}</th>
                      <td>${String(
                        totals.kastrupAirport * kastrupAirportValue,
                      )}</td>
                  </tr>
              `
                  : ''
              }
              ${
                reportSettings.stationCH?.isActive
                  ? `
                  <tr>
                      <th>${t('TotalStation')}</th>
                      <td>${String(totals.stationCH * stationCHValue)}</td>
                  </tr>
              `
                  : ''
              }
              ${
                reportSettings.sturupAirport?.isActive
                  ? `
                  <tr>
                      <th>${t('TotalSturup')}</th>
                      <td>${String(
                        totals.sturupAirport * sturupAirportValue,
                      )}</td>
                  </tr>
              `
                  : ''
              }
              ${
                reportSettings.driverPercentage?.isActive
                  ? `
                  <tr>
                      <th>${t('DriverPercentage')}</th>
                      <td>${String(driverCalculatedPercentage)}</td>
                  </tr>
              `
                  : ''
              }
              ${
                reportSettings.holiday?.isActive
                  ? `
                  <tr>
                      <th>${t('Holiday')}</th>
                      <td>${String(calculatedHolidayPay)}</td>
                  </tr>
              `
                  : ''
              }
              ${
                reportSettings.tax?.isActive
                  ? `
                  <tr>
              <th>${t('BeforeTax')}</th>
              <td>${String(beforeTaxTotal)}</td>
              </tr>
              `
                  : ''
              }
              ${
                reportSettings.tax?.isActive
                  ? `
                  <tr>
                      <th>${t('Tax')}</th>
                      <td>${String(tax)}</td>
                  </tr>
              `
                  : ''
              }
              ${
                reportSettings.tax?.isActive
                  ? `
                <tr>
                  <th>${t('AfterTotal')}</th>
                  <td>${String(afterTaxTotal)}</td>
                </tr>
                `
                  : ''
              }
              ${
                reportSettings.tax?.isActive
                  ? `
                <tr>
              <th>${t('DriverIncome')}</th>
              <td>${String(driverIncome)}</td>
              </tr>
                `
                  : ''
              }
          </table>
  
          <!-- Notes Section -->
          <h2 class="section-title">${t('Notes')}</h2>
<table>
  <tr>
    <th>${t('Date')}</th>
    <th>${t('Note')}</th>
  </tr>
  ${totals.notes
    .filter(
      noteEntry =>
        typeof noteEntry.note === 'string' && noteEntry.note.trim() !== '',
    ) // Ensure note is a non-empty string
    .map(
      noteEntry => `
      <tr>
        <td>${noteEntry.date}</td>
        <td>${noteEntry.note}</td>
      </tr>
    `,
    )
    .join('')}
</table>

      </body>
      </html>
      `;
      // Generate unique file name in the Documents folder for temporary saving
      const tempFileName = await generateUniqueFileName(
        baseFileName,
        'pdf',
        RNFS.DocumentDirectoryPath,
      );

      // Generate the PDF from HTML content
      const document = await RNHTMLtoPDF.convert({
        html: htmlContent,
        fileName: tempFileName,
        directory: 'Documents',
      });

      // Define the destination path for the PDF file
      const uniqueFileName = await generateUniqueFileName(
        baseFileName,
        'pdf',
        directoryPath,
      );
      const destinationPath = `${directoryPath}/${uniqueFileName}`;

      // Move the generated file to the target directory
      await RNFS.moveFile(document.filePath, destinationPath);

      // Alert.alert('PDF Generated', `File saved at: ${destinationPath}`);

      // Share the PDF using react-native-share
      const options = {
        title: 'Share Report PDF',
        url: `file://${destinationPath}`, // Required for Android
        type: 'application/pdf',
        message: 'Here is your report!',
      };

      try {
        await Share.open(options); // Open the share dialog
      } catch (shareError) {
        if (shareError.message !== 'User did not share') {
          console.error('Error sharing the report:', shareError);
        }
      }
    } catch (error) {
      console.error('Error generating or sharing the PDF:', error);
    }
  };
  const deleteMonthlyReports = async () => {
    try {
      // Fetch to check if reports exist for the selected monthYear
      const checkResponse = await fetch(
        `${config.baseUrl}/reports/${userId}/monthly?monthYear=${monthYear}`,
        {
          method: 'GET', // Check the reports first
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      const data = await checkResponse.json();

      if (!checkResponse.ok || !data || data.length === 0) {
        Alert.alert('No Reports', 'No reports exist for the selected month.');
        return;
      }
      // Validate response
      // if () {
      //   throw new Error(`Failed to fetch reports for ${monthYear}.`);
      // }

      // Check if reports exist

      // Proceed with deletion
      Alert.alert(
        'Confirm Delete',
        `Are you sure you want to delete reports for ${monthYear}?`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Delete',
            onPress: async () => {
              try {
                const deleteResponse = await fetch(
                  `${config.baseUrl}/reports/${userId}/monthly?monthYear=${monthYear}`,
                  {
                    method: 'DELETE',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                  },
                );

                // Check if deletion was successful
                if (!deleteResponse.ok) {
                  throw new Error(
                    `Failed to delete reports. Status: ${deleteResponse.status}`,
                  );
                }

                const deleteResult = await deleteResponse.json();
                Alert.alert('Success', 'Reports deleted successfully.');
                refreshTable();
                setTax('');
                // Optionally, refresh data or reset state
                setReportData([]); // Clear the report data for the current month
              } catch (deleteError) {
                console.error('Error deleting reports:', deleteError);
                Alert.alert(
                  'Error',
                  `Could not delete reports: ${deleteError.message}`,
                );
              }
            },
          },
        ],
      );
    } catch (error) {
      console.error('Error checking reports:', error);
      Alert.alert('Error', `Failed to check reports: ${error.message}`);
    }
  };
  const refreshTable = () => {
    setTableRefreshKey(prevKey => prevKey + 1); // Increment to trigger re-render
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}>
        <View style={styles.navBar}>
          <View style={styles.logo}>
            <View style={{marginTop: 10, marginBottom: '-65%'}}>
              <CustomImage />
            </View>
            <CustomMenu />
          </View>
          <CustomProfileDetails />
        </View>
        <View style={styles.header}>
          <Text style={styles.text}>{t('Report')}</Text>
        </View>

        <ScrollView>
          {/* <GestureDetector gesture={gestures}>
            <Animated.View style={[styles.container, animatedStyle]}> */}
          <CustomTable
            days={30}
            onTotalsCalculated={handleTotalsCalculated}
            // reports={handleFilteredReports}
            key={tableRefreshKey} // Add this line
          />
          {/* </Animated.View>
          </GestureDetector> */}
          <View
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalScrollViewContainer}>
            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={buttoncolor.bgcolor} />
                <Text style={styles.loadingText}>{t('Loading')}...</Text>
              </View>
            ) : (
              <View style={styles.totalValuesInputs}>
                <View style={styles.rightInputs}>
                  <Custominput
                    title={t('TotalBefore')}
                    value={String(totals.beforeDetaction)}
                    editable={false}
                  />
                  <Custominput
                    title={t('TotalAfter')}
                    value={String(totals.afterDetaction)}
                    editable={false}
                  />
                  {reportSettings.cash?.isActive && (
                    <Custominput
                      title={t('TotalCash')}
                      value={String(totals.cash)}
                      editable={false}
                    />
                  )}
                  {reportSettings.tip?.isActive && (
                    <Custominput
                      title={t('TotalTip')}
                      value={String(totals.tip)}
                      editable={false}
                    />
                  )}
                  {reportSettings.extraBill?.isActive && (
                    <Custominput
                      title={t('TotalExtra')}
                      value={String(totals.extraBill)}
                      editable={false}
                    />
                  )}
                  {reportSettings.cash?.isActive && (
                    <Custominput
                      title={t('FinalCash')}
                      value={String(finalCash)}
                      editable={false}
                    />
                  )}
                  {reportSettings.prePaid?.isActive && (
                    <Custominput
                      title={t('TotalPrepaid')}
                      value={String(totals.prePaid)}
                      editable={false}
                      bgColor={true}
                    />
                  )}
                  {reportSettings.SKOL?.isActive && (
                    <Custominput
                      title={t('TotalSkol')}
                      value={String(totals.SKOL)}
                      editable={false}
                    />
                  )}
                  {reportSettings.FTJ?.isActive && (
                    <Custominput
                      title={t('Total FTJ')}
                      value={String(totals.FTJ)}
                      editable={false}
                    />
                  )}
                  {reportSettings.card?.isActive && (
                    <Custominput
                      title={t('TotalCard')}
                      value={String(totals.card)}
                      editable={false}
                      bgColor={true}
                    />
                  )}
                  {reportSettings.driverPercentage?.isActive && (
                    <Custominput
                      title={t('DriverPercentage')}
                      value={String(driverCalculatedPercentage)}
                      editable={false}
                    />
                  )}
                  {reportSettings.holiday?.isActive && (
                    <Custominput
                      title={t('Holiday')}
                      value={String(calculatedHolidayPay)}
                      editable={false}
                    />
                  )}
                  {reportSettings.tax?.isActive && (
                    <Custominput
                      title={t('BeforeTaxTotal')}
                      value={String(beforeTaxTotal)}
                      editable={false}
                    />
                  )}
                  {reportSettings.ownerPercentage?.isActive && (
                    <Custominput
                      title={t('OwnerIncome')}
                      value={String(ownerCalculatedPercentage)}
                      editable={false}
                    />
                  )}
                  {reportSettings.tax?.isActive && (
                    <Custominput
                      title={t('Tax')}
                      placeholder={'Enter'}
                      value={tax}
                      onChange={setTax}
                    />
                  )}
                  {reportSettings.tax?.isActive && (
                    <Custominput
                      title={t('Aftertax')}
                      editable={false}
                      value={String(afterTaxTotal)}
                    />
                  )}
                  {reportSettings.tax?.isActive && (
                    <Custominput
                      title={t('DriverIncome')}
                      value={String(driverIncome)}
                      editable={false}
                    />
                  )}
                </View>
                <View style={styles.rightInputs1}>
                  {reportSettings.kastrupAirport?.isActive && (
                    <Custominput
                      title={t('TotalKastrup')}
                      value={String(
                        totals.kastrupAirport * kastrupAirportValue,
                      )}
                      editable={false}
                    />
                  )}
                  {reportSettings.stationCH?.isActive && (
                    <Custominput
                      title={t('TotalStation')}
                      value={String(totals.stationCH * stationCHValue)}
                      editable={false}
                    />
                  )}
                  {reportSettings.sturupAirport?.isActive && (
                    <Custominput
                      title={t('TotalSturup')}
                      value={String(totals.sturupAirport * sturupAirportValue)}
                      editable={false}
                    />
                  )}
                  <CustomNote title={t('Note')} notes={totals.notes} />
                </View>
              </View>
            )}
          </View>
          <View style={{marginVertical: 20, paddingHorizontal: 20}}>
            <View style={styles.threeDots}>
              <TouchableOpacity
                style={styles.dotWithText}
                title={t('DeleteReport')}
                onPress={deleteMonthlyReports}>
                {/* <View style={styles.dotYellow} />
                <Text style={styles.dotText}>{t('DeleteReport')}</Text> */}
                <FontAwesomeIcon icon={faTrashCan} color="#000" size={27} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.dotWithText}
                title={t('ViewGrap')}
                onPress={() => navigation.navigate('Graphview')}>
                {/* <View style={styles.dotBlue} />
                <Text style={styles.dotText}>{t('Graph')}</Text> */}
                <FontAwesomeIcon icon={faChartLine} color="#31A8F7" size={27} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.dotWithText}
                title={t('Share')}
                onPress={generatePDF}>
                {/* <View style={styles.dotBlack} />
                <Text style={styles.dotText}>{t('Share')}</Text> */}
                <FontAwesomeIcon
                  icon={faShareNodes}
                  color="#FCCC31"
                  size={27}
                />
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  header: {
    marginVertical: 20,
    alignItems: 'center',
    marginTop: -40,
  },
  text: {
    fontSize: 28,
    fontWeight: '700',
    color: textcolor.color2,
  },
  horizontalScrollViewContainer: {
    flexDirection: 'row',
    // paddingHorizontal: 20,
  },
  totalValuesInputs: {
    flexDirection: 'row',
    marginTop: 10,
    marginHorizontal: 2,
  },
  rightInputs: {
    flexWrap: 'wrap',
    // marginRight: 5,
  },
  rightInputs1: {
    flexWrap: 'wrap',
    maxWidth: '40%',
    // marginLeft:5
  },
  // horizontalLine: {
  //   margin: 10,
  //   width: '100%',
  //   height: 1.5,
  //   backgroundColor: 'black',
  // },
  // buttonsGroup: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-evenly',
  //   marginVertical: 10,
  // },
  // Button1: {
  //   backgroundColor: buttoncolor.bgcolor,
  //   marginHorizontal: 4,
  //   width: 'auto',
  //   fontSize: 12,
  // },
  // Button2: {
  //   backgroundColor: '#F1C50D',
  //   marginHorizontal: 4,
  //   width: 'auto',
  //   fontSize: 12,
  // },
  // Button3: {
  //   backgroundColor: '#DC143C',
  //   marginHorizontal: 4,
  //   width: 'auto',
  //   fontSize: 12,
  // },
  loadingText: {
    textAlign: 'center',
  },
  threeDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 10,
    // borderWidth: 1,
    // borderColor: '#000',
    // alignItems: 'flex-start',
  },
  dotWithText: {
    flexDirection: 'column',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  // dotYellow: {
  //   width: 10,
  //   height: 10,
  //   borderRadius: 5,
  //   backgroundColor: '#F1C50D',
  //   marginBottom: 5,
  // },
  // dotBlue: {
  //   // width: 10,
  //   // height: 10,
  //   borderRadius: 5,
  //   backgroundColor: buttoncolor.bgcolor,
  //   marginBottom: 5,
  // },
  // dotBlack: {
  //   // width: 10,
  //   // height: 10,
  //   borderRadius: 5,
  //   backgroundColor: '#000000',
  //   marginBottom: 5,
  // },
  // dotText: {
  //   fontSize: 14,
  //   fontWeight: '500',
  //   color: textcolor.color1,
  // },
});

export default ViewReportScreen;
