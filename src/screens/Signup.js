import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Dimensions,
  Alert,
  ActivityIndicator,
} from 'react-native';
import React, {useState} from 'react';
import CustomImage from '../components/CustomImage';
import CustomButton from '../components/CustomButton';
import CustomTextinput from '../components/CustomTextinput';
import {textcolor} from '../styles/style';
import CustomDropdown from '../components/CustomDropdown';
import {useTranslation} from 'react-i18next';
import LanguageSelector from '../Language/LanguageSelector';
import {SafeAreaView} from 'react-native-safe-area-context';
import AuthService from '../services/features/auth/auth.service.ts';
import AsyncStorage from '@react-native-async-storage/async-storage';
const {width, height} = Dimensions.get('window');

const Signup = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const taxiCompany = [
    {
      name: 'Taxi 232323',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465225/232323_uqmxtn.jpg',
    },
    {
      name: 'Svedtax Skane 13 80 00',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465221/Skane138000_qyvr0q.jpg',
    },
    {
      name: 'Taxi Malmo',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465221/Malmo_lmxfvf.jpg',
    },
    {
      name: 'Taxi Vellinge',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465222/V_csscif.jpg',
    },
    {
      name: 'Taxi Grand Skane',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465221/grandSkane_e3lqtz.jpg',
    },
    {
      name: 'Taxi LUND 121212',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465221/Lund121212_eevrhh.jpg',
    },
    {
      name: 'Taxi Kurir',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465221/kurir_e2qy1t.jpg',
    },
    {
      name: 'Taxi 97',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465220/97_plwq29.jpg',
    },
    {
      name: 'Taxi Skane',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465221/skane_dgb0gl.jpg',
    },
  ];
  // console.log(taxiCompany);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    mobileNumber: '',
    taxiCompany: null,
    taxiNumber: '',
  });
  // console.log(formData);
  const selectedLanguage = i18n.language; // Get the current selected language
  const handleInputChange = (name, value) => {
    setFormData({...formData, [name]: value});
  };
  const [loading, setLoading] = useState(false); // Loading state

  const handleRegister = async () => {
    const {
      name,
      email,
      password,
      confirmPassword,
      mobileNumber,
      taxiCompany,
      taxiNumber,
    } = formData;
    // console.log('data :', formData);
    // Validation
    if (
      !name ||
      !email ||
      !password ||
      !confirmPassword ||
      !mobileNumber ||
      !taxiCompany || // Adding taxiCompany to the validation check
      !taxiNumber
    ) {
      Alert.alert(t('Missing Fields'), t('All fields required'));
      return; // Return early to prevent execution
    }

    if (password !== confirmPassword) {
      Alert.alert(t('passwordMismatch'), t('passwordsDoNotMatch'));
      return; // Return early to prevent execution
    }
    const payload = {
      name,
      email,
      password,
      mobileNumber,
      taxiCompany: taxiCompany
        ? {
            companyName: taxiCompany.name,
            companyLogo: taxiCompany.logo,
          }
        : null, // Handle null case for taxiCompany
      taxiNumber,
    };
    setLoading(true); // Start loading
    try {
      // Register user
      const response = await AuthService.register(payload);
      // console.log('response ', response);
      Alert.alert(t('registrationSuccess'), t('accountCreated'));
      const user = JSON.stringify(response.user);
      try {
        await AsyncStorage.setItem('User', user);
        // await AsyncStorage.setItem('language', selectedLanguage);  // Save language preference locally
      } catch (e) {
        console.log(e);
      }
      navigation.navigate('registersuccessful', {componentType: 'Signup'}); // Navigate to verification screen
    } catch (error) {
      console.error('Error registering user:', JSON.stringify(error, null, 2)); // Log error object properly
      Alert.alert(t('registrationFailed'), error.message || t('tryAgainLater'));
    } finally {
      setLoading(false); // Stop loading
    }
  };
  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
        style={styles.keyboardAvoidingView}>
        <ScrollView contentContainerStyle={styles.scrollViewContent}>
          <View>
            <View style={{width: '40%', alignSelf: 'flex-end'}}>
              <LanguageSelector />
            </View>
            <View style={{marginTop: '1%'}}>
              <CustomImage />
            </View>
            <View>
              <Text style={[styles.header, {fontSize: height * 0.03}]}>
                {t('createAccount')}
              </Text>
            </View>
          </View>
          <View style={{marginTop: '-5%'}}>
            <CustomTextinput
              label={t('fullName')}
              placeholder={t('enterFullName')}
              value={formData.name}
              onChangeText={value => handleInputChange('name', value)}
              // onChangeText={value => console.log(value)}
            />
            <CustomTextinput
              label={t('email')}
              placeholder={t('enterEmail')}
              value={formData.email}
              onChangeText={value => handleInputChange('email', value)}
            />
            <CustomTextinput
              label={t('password')}
              placeholder={t('enterPassword')}
              secureTextEntry={true}
              value={formData.password}
              onChangeText={value => handleInputChange('password', value)}
            />
            <CustomTextinput
              label={t('confirmPass')}
              placeholder={t('enterconfirmPass')}
              secureTextEntry={true}
              value={formData.confirmPassword}
              onChangeText={value =>
                handleInputChange('confirmPassword', value)
              }
            />
            <CustomDropdown
              label={t('selectTaxiCompany')}
              options={taxiCompany}
              selectedOption={formData.taxiCompany}
              onSelect={option => handleInputChange('taxiCompany', option)}
            />
            <CustomTextinput
              label={t('mobileno')}
              placeholder={t('entermobileno')}
              keyboardType={'number-pad'}
              value={formData.mobileNumber}
              onChangeText={value => handleInputChange('mobileNumber', value)}
            />
            <CustomTextinput
              label={t('taxiNumber')}
              placeholder={t('enterTaxiNumber')}
              keyboardType={'number-pad'}
              value={formData.taxiNumber}
              onChangeText={value => handleInputChange('taxiNumber', value)}
            />
          </View>
          <View style={{marginTop: height * 0.03}}>
            {loading ? (
              <ActivityIndicator
                size="large"
                color={textcolor.color2}
              /> /* Show ActivityIndicator */
            ) : (
              <CustomButton title={t('signup')} onPress={handleRegister} />
            )}
            <View style={styles.bottom}>
              <Text style={styles.text}>{t('alreadyHaveAccount')} </Text>
              <TouchableOpacity onPress={() => navigation.navigate('SignIn')}>
                <Text style={styles.text2}>{t('loginHere')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Signup;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  bottom: {
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    marginTop: height * 0.01,
  },
  text: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color1,
  },
  text2: {
    fontSize: 14,
    fontWeight: '700',
    color: textcolor.color2,
  },
  header: {
    fontSize: height * 0.03,
    fontWeight: '700',
    color: textcolor.color2,
    alignSelf: 'center',
    marginTop: '-11%',
  },
});
