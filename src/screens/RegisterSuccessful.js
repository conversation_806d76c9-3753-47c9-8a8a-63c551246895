import {
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    View,
    Text,
    Dimensions,
    TouchableOpacity
} from 'react-native';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';
import CustomImage from '../components/CustomImage';
import { textcolor } from '../styles/style';
import CustomButton from '../components/CustomButton';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faChevronLeft } from '@fortawesome/free-solid-svg-icons';
import { faChevronRight } from '@fortawesome/free-solid-svg-icons';
const { width, height } = Dimensions.get('window');

const RegisterSuccessful = ({ navigation }) => {
    const { t, i18n } = useTranslation();

    const handlelogin = async () => {
        navigation.navigate('SignIn')
    };

    return (
        <SafeAreaView style={styles.container}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
                style={styles.keyboardAvoidingView}
            >
                <ScrollView contentContainerStyle={styles.scrollViewContent}>
                    <View style={{ marginTop: height * 0.05 }}>
                        <CustomImage />
                    </View>
                    <View style={styles.maincontainer}>
                        <Text style={styles.title}>{t('successfullyregistered')}</Text>
                        <Text style={styles.text}>
                            {t('description')}
                        </Text>

                        <View style={styles.iconcontainer}>
                        <CustomButton title={t('Next')} onPress={()=>navigation.navigate('SignIn')}/>
                        </View>
                    </View>
                    {/* <View style={{ margin: 12, alignSelf: 'flex-end', marginRight: width * 0.09 }}>
                        <CustomButton title={'     Next    '} onPress={handlelogin} />
                    </View> */}
                </ScrollView>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
};

export default RegisterSuccessful;

const styles = StyleSheet.create({
    container: {
        flex: 1,

    },
    keyboardAvoidingView: {
        flex: 1,
    },
    scrollViewContent: {
        flexGrow: 1,

    },
    maincontainer: {
        width: width * 0.8,
        height: height * 0.6,
        backgroundColor: '#FCCC31',
        padding: 20,
        alignSelf: 'center'
    },
    title: {
        fontSize: 21,
        fontWeight: '700',
        alignSelf: 'center',
        marginVertical: 20,
        paddingBottom: 15,
        textAlign: 'center',
        color: textcolor.color1
    },
    text: {
        fontSize: 18,
        fontWeight: '400',
        textAlign: 'center',
        color: textcolor.color1
    },
    iconcontainer:{ 
        flex: 2,
        justifyContent:'flex-end',
         alignItems: 'flex-end' 
        }
});