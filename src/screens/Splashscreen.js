import { Image, StyleSheet } from 'react-native';
import React, { useEffect, useState } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTranslation } from 'react-i18next';

const Splashscreen = ({ navigation }) => {
  const [userId, setUserId] = useState(null);
  const { i18n } = useTranslation();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Load the selected language from AsyncStorage
        const savedLanguage = await AsyncStorage.getItem('language');
        if (savedLanguage) {
          i18n.changeLanguage(savedLanguage); // Apply the saved language
        } else {
          // If no language is found, use default
          i18n.changeLanguage('sv'); // Default language
        }

        // Fetch user data from AsyncStorage
        const user = await AsyncStorage.getItem('User');
        const parsedUser = user ? JSON.parse(user) : null;
        setUserId(parsedUser?._id || null);

        // Navigate based on user login state
        if (parsedUser?._id) {
          navigation.replace('Bottomtab'); // User is logged in
        } else {
          navigation.replace('SignIn'); // User needs to sign in
        }
      } catch (error) {
        console.error('Error during initialization:', error);
      }
    };

    // Run the initialization after splash screen shows for 3 seconds
    const timer = setTimeout(initializeApp, 3000);
    return () => clearTimeout(timer); // Cleanup timer
  }, [navigation, i18n]);

  return (
    <SafeAreaView style={styles.container}>
      <Image
        source={require('.././assets/Image/logo.png')}
        style={styles.image}
      />
    </SafeAreaView>
  );
};

export default Splashscreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '70%',
    height: '100%',
    resizeMode: 'contain',
  },
});
