import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  SafeAreaView,
  KeyboardAvoidingView,
  StyleSheet,
  ScrollView,
  Text,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import CustomCalendar from '../components/CustomCalender';
import CustomImage from '../components/CustomImage';
import CustomProfileDetails from '../components/CustomProfileDetails';
import CustomMenu from '../components/CustomMenu';
import CustomModal from '../components/CustomModal';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import LanguageSelector from '../Language/LanguageSelector';
import AdBanner from '../components/AdBnners';

const Home = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');
  const [beforeDedaction, setBeforeDedaction] = useState(0);
  const [afterDedaction, setAfterDedaction] = useState(0);
  const [userId, setUserId] = useState(null);
  const [timer, setTimer] = useState(null);
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);

  const imageSources = [
    require('../assets/Image/slide.png'),
    require('../assets/Image/TaxiBanner.png'),
    require('../assets/Image/techcreator.png'),
  ];
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        // Get the saved language for the specific userId
        const savedLanguage = await AsyncStorage.getItem(`${userId}-language`);
        if (savedLanguage) {
          // Change the language using i18n
          i18n.changeLanguage(savedLanguage);
          setCurrentLanguage(savedLanguage); // Update the state
        }
      } catch (error) {
        console.error('Error loading language from AsyncStorage:', error);
      }
    };

    loadLanguage();
  }, [i18n, userId]); // Re-run when userId or i18n changes

  // Fetch user data when the screen is focused
  useFocusEffect(
    useCallback(() => {
      const fetchUserData = async () => {
        try {
          const user = await AsyncStorage.getItem('User');
          const parsedUser = user ? JSON.parse(user) : null;
          setUserId(parsedUser?._id || null);
        } catch (error) {
          console.error('Error fetching user data:', error);
        }
      };
      fetchUserData();
    }, []),
  );

  const closeModal = () => {
    setModalVisible(false);
  };

  const handleDateSelect = (date, before, after) => {
    setSelectedDate(date);
    setBeforeDedaction(before);
    setAfterDedaction(after);

    if (timer) {
      clearTimeout(timer);
    }
    const newTimer = setTimeout(() => {
      setSelectedDate('');
      setBeforeDedaction(0);
      setAfterDedaction(0);
    }, 5000);

    setTimer(newTimer);
  };
  useEffect(() => {
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [timer]);

  return (
    <SafeAreaView style={styles.safeArea}>
      <KeyboardAvoidingView style={styles.keyboardAvoidingView}>
        <View style={styles.navBar}>
          <View style={styles.logo}>
            <View style={{marginTop: 10, marginBottom: '-65%'}}>
              <CustomImage />
            </View>
            <CustomMenu />
          </View>
          <CustomProfileDetails />
        </View>
        <View style={styles.hiddenLanguageSelector}>
          <LanguageSelector />
        </View>
        <ScrollView style={styles.scrollView}>
          <View style={styles.calendar}>
            <CustomCalendar onDateSelect={handleDateSelect} />
          </View>
          <View>
           
            <View style={styles.horizontalLineUp} />
            
              <View style={styles.totalContainer}>
                <Text style={styles.Text}>{selectedDate}</Text>
                <Text style={styles.Text}>
                {t('Before')}:{' '}
                  <Text style={styles.totalText}>{beforeDedaction}Kr</Text>
                </Text>
                <Text style={styles.Text}>
                {t('After')}:{' '}
                  <Text style={styles.totalText}>{afterDedaction}Kr</Text>
                </Text>
              </View>
            
          </View>
        </ScrollView>

        <View style={styles.adBannerContainer}>
  <AdBanner />
</View>

      </KeyboardAvoidingView>
      <CustomModal
        visible={modalVisible}
        onClose={closeModal}
        navigation={navigation}
        t={t}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: 'white',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'white',
  },
  hiddenLanguageSelector:{
    display: 'none'
  },
  scrollView: {
    flex: 1,
    paddingBottom: 20,
  },
  calendar: {
    backgroundColor: 'white',
    paddingHorizontal: 10,
    marginTop:-30
  },
  horizontalLineUp: {
    marginTop: 10,
    marginBottom: 10,
    width: '100%',
    height: 1.5,
    backgroundColor: 'black',
  },
  totalContainer: {
    padding: 10,
    alignItems: 'center',
  },
  totalText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#18A0FB',
  },
  Text: {
    fontSize: 16,
    color: 'black',
  },
  adBannerContainer: {
  position: 'absolute',
  bottom: 5, 
  left: 0,
  right: 0,
  zIndex: 99,
  alignItems: 'center',
  justifyContent: 'center',
},

});

export default Home;
