import {
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  Text,
  View,
  Platform,
  SafeAreaView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import CustomProfileDetails from '../components/CustomProfileDetails';
import CustomImage from '../components/CustomImage';
import Custominput from '../components/Custominput';
import CustomButton from '../components/CustomButton';
import CustomMenu from '../components/CustomMenu';
import {textcolor} from '../styles/style';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {config} from '../../config';
import {useFocusEffect} from '@react-navigation/native';

const AddRapport = ({navigation}) => {
  const {t} = useTranslation();
  const [total, setTotal] = useState('');
  const [cash, setCash] = useState('');
  const [card, setCard] = useState('');
  const [prepaid, setPrepaid] = useState('');
  const [FTJ, setFTJ] = useState('');
  const [SKOL, setSKOL] = useState('');
  const [tips, setTips] = useState('');
  const [kastrup, setkastrup] = useState('');
  const [station, setstation] = useState('');
  const [sturup, setsturup] = useState('');
  const [extraBill, setExtraBill] = useState('');
  const [beforeValue, setBeforeValue] = useState(0);
  const [afterValue, setAfterValue] = useState(0);
  const [driverPercentage, setDriverPercentage] = useState(0);
  const [ownerPercentage, setOwnerPercentage] = useState(0);
  const [note, setNote] = useState('');
  const [date, setDate] = useState(null);
  const [userId, setUserId] = useState(null);
  const [loading, setLoading] = useState(false);
  const input1 = true;
  // State to hold report settings
  const [reportSettings, setReportSettings] = useState({
    total: {isActive: false},
    cash: {isActive: false},
    card: {isActive: false},
    prePaid: {isActive: false},
    FTJ: {isActive: false},
    SKOL: {isActive: false},
    tip: {isActive: false},
    kastrupAirport: {isActive: false, value: 0},
    stationCH: {isActive: false, value: 0},
    sturupAirport: {isActive: false, value: 0},
    extraBill: {isActive: false},
    ownerPercentage: {isActive: false, value: 0},
    driverPercentage: {isActive: false, value: 0},
  });

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const user = await AsyncStorage.getItem('User');
        const parsedUser = user ? JSON.parse(user) : null;
        setUserId(parsedUser?._id || null);
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };
    fetchUserData();
  }, []);

  const fetchReportSettings = async () => {
    if (!userId) {
      console.log('No user ID available for API request.');
      return;
    }
    try {
      const response = await fetch(
        `${config.baseUrl}/auth/report-setting/${userId}`,
      );
      const result = await response.json();

      setReportSettings({
        total: {isActive: result.total?.isActive || false},
        cash: {isActive: result.cash?.isActive || false},
        card: {isActive: result.card?.isActive || false},
        prePaid: {isActive: result.prePaid?.isActive || false},
        FTJ: {isActive: result.FTJ?.isActive || false},
        SKOL: {isActive: result.SKOL?.isActive || false},
        tip: {isActive: result.tip?.isActive || false},
        kastrupAirport: {
          isActive: result.kastrupAirport?.isActive || false,
          value: result.kastrupAirport?.value || 1,
        },
        stationCH: {
          isActive: result.stationCH?.isActive || false,
          value: result.stationCH?.value || 1,
        },
        sturupAirport: {
          isActive: result.sturupAirport?.isActive || false,
          value: result.sturupAirport?.value || 1,
        },
        extraBill: {isActive: result.extraBill?.isActive || false},
        ownerPercentage: {
          isActive: result.ownerPercentage?.isActive || false,
          value: result.ownerPercentage?.value || 0,
        },
        driverPercentage: {
          isActive: result.driverPercentage?.isActive || false,
          value: result.driverPercentage?.value || 0,
        },
      });
    } catch (error) {
      console.error('Error fetching report settings:', error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (userId) {
        fetchReportSettings();
      }
    }, [userId]),
  );

  const validateReport = () => {
    const values = [
      total,
      cash,
      card,
      prepaid,
      FTJ,
      SKOL,
      tips,
      kastrup,
      station,
      sturup,
      extraBill,
      note,
    ];

    // Check if at least one value is greater than 0
    return values.some(
      value => parseFloat(value) > 0 || (value && value.trim() !== ''),
    );
  };

  const handleTotalChange = value => {
    const totalValue = parseFloat(value) || 0;
    setTotal(value);
    setBeforeValue(totalValue);
    setReportSettings(prev => ({
      ...prev,
      total: {...prev.total, value: totalValue},
    }));
  };

  useEffect(() => {
    const cashValue = parseFloat(cash) || 0;
    const cardValue = parseFloat(card) || 0;
    const prepaidValue = parseFloat(prepaid) || 0;
    const FTJValue = parseFloat(FTJ) || 0;
    const SKOLValue = parseFloat(SKOL) || 0;
    const tipsValue = parseFloat(tips) || 0;

    let totalValue = parseFloat(total) || 0;

    if (total) {
      // If total is provided, use it directly for beforeDedaction
      setBeforeValue(totalValue);
    } else {
      // If total is not provided, calculate from other fields
      totalValue =
        (reportSettings.cash.isActive ? cashValue : 0) +
        (reportSettings.card.isActive ? cardValue : 0) +
        (reportSettings.prePaid.isActive ? prepaidValue : 0) +
        (reportSettings.FTJ.isActive ? FTJValue : 0) +
        (reportSettings.SKOL.isActive ? SKOLValue : 0) +
        (reportSettings.tip.isActive ? tipsValue : 0);

      setBeforeValue(totalValue);
    }

    const kastrupValue = reportSettings.kastrupAirport.isActive
      ? (parseFloat(kastrup) || 0) * reportSettings.kastrupAirport.value
      : 0;
    const sturupValue = reportSettings.sturupAirport.isActive
      ? (parseFloat(sturup) || 0) * reportSettings.sturupAirport.value
      : 0;
    const stationValue = reportSettings.stationCH.isActive
      ? (parseFloat(station) || 0) * reportSettings.stationCH.value
      : 0;
    const extraValue = parseFloat(extraBill) || 0;

    const afterTotal = totalValue - (kastrupValue + sturupValue + stationValue);
    setAfterValue(afterTotal);
    const ownerPercentageValue =
      afterTotal * (reportSettings.ownerPercentage.value / 100);
    const driverPercentageValue =
      afterTotal * (reportSettings.driverPercentage.value / 100);
    setOwnerPercentage(Number(ownerPercentageValue).toFixed(0));
    setDriverPercentage(Number(driverPercentageValue).toFixed(0));
  }, [
    total,
    cash,
    card,
    prepaid,
    FTJ,
    SKOL,
    tips,
    kastrup,
    sturup,
    station,
    extraBill,
    reportSettings,
  ]);
  const formatDateToDDMMYYYY = date => {
    if (!date) return '';
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const submitReport = async () => {
    if (date === null) {
      Alert.alert(t('Error'), t('Please select a date.'));
      return;
    }
    if (!validateReport()) {
      Alert.alert(
        t('Nothing to Submit'),
        t('At least one value must be added to the report.'),
      );
      return;
    }
    setLoading(true);

    const finalBeforeValue =
      !reportSettings.cash.isActive &&
      !reportSettings.card.isActive &&
      !reportSettings.prePaid.isActive &&
      !reportSettings.FTJ.isActive &&
      !reportSettings.SKOL.isActive &&
      !reportSettings.tip.isActive
        ? parseFloat(total) || 0
        : beforeValue;

    const formattedDate = formatDateToDDMMYYYY(date);

    const reportData = {
      total: {value: beforeValue},
      cash: {value: parseFloat(cash) || 0},
      card: {value: parseFloat(card) || 0},
      prePaid: {value: parseFloat(prepaid) || 0},
      FTJ: {value: parseFloat(FTJ) || 0},
      SKOL: {value: parseFloat(SKOL) || 0},
      tip: {value: parseFloat(tips) || 0},
      kastrupAirport: {value: parseFloat(kastrup) || 0},
      sturupAirport: {value: parseFloat(sturup) || 0},
      stationCH: {value: parseFloat(station) || 0},
      extraBill: {value: parseFloat(extraBill) || 0},
      date: formattedDate,
      note: note,
      beforeDetaction: finalBeforeValue,
      afterDetaction: afterValue,
    };

    try {
      const response = await fetch(`${config.baseUrl}/reports/${userId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportData),
      });
      const jsonResponse = await response.json();

      if (response.ok) {
        Alert.alert(t('Success'), t('Report submitted successfully'));
        // Clear all input fields after successful submission
        setTotal('');
        setCash('');
        setCard('');
        setPrepaid('');
        setFTJ('');
        setSKOL('');
        setTips('');
        setkastrup('');
        setstation('');
        setsturup('');
        setExtraBill('');
        setBeforeValue(0);
        setAfterValue(0);
        setNote('');
        setDate(null);
      } else {
        Alert.alert(
          t('Error'),
          jsonResponse.message || t('Something went wrong'),
        );
      }
    } catch (error) {
      Alert.alert(t('Error'), t('Failed to submit report'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}>
        <View style={styles.navBar}>
          <View style={styles.logo}>
            <View style={{marginTop: 10, marginBottom: '-65%'}}>
              <CustomImage />
            </View>
            <CustomMenu />
          </View>
          <CustomProfileDetails />
        </View>
        <Text style={styles.text}>{t('Addreport')}</Text>

        <ScrollView style={styles.scrollView}>
          <View style={styles.header}></View>
          <View style={styles.inputForm}>
            <Custominput
              title1={t('Date')}
              isDateInput={true}
              value={date}
              onChange={setDate}
            />
            {reportSettings.total.isActive && (
              <Custominput
                title1={t('Total')}
                value={total}
                onChange={handleTotalChange}
              />
            )}
            {reportSettings.cash.isActive && (
              <Custominput title1={t('Cash')} value={cash} onChange={setCash} />
            )}
            {reportSettings.card.isActive && (
              <Custominput title1={t('Card')} value={card} onChange={setCard} />
            )}
            {reportSettings.prePaid.isActive && (
              <Custominput
                title1={t('PrePaid')}
                value={prepaid}
                onChange={setPrepaid}
              />
            )}
            {reportSettings.FTJ.isActive && (
              <Custominput title1={t('Ftj')} value={FTJ} onChange={setFTJ} />
            )}
            {reportSettings.SKOL.isActive && (
              <Custominput title1={t('Skol')} value={SKOL} onChange={setSKOL} />
            )}
            {reportSettings.tip.isActive && (
              <Custominput title1={t('Tip')} value={tips} onChange={setTips} />
            )}
            {reportSettings.extraBill.isActive && (
              <Custominput
                title1={t('Extra')}
                value={extraBill}
                onChange={setExtraBill}
              />
            )}
            {reportSettings.kastrupAirport.isActive && (
              <Custominput
                title1={`${t('Kastrup')} (${kastrup} x ${
                  reportSettings.kastrupAirport.value
                })`}
                value={kastrup}
                onChange={setkastrup}
              />
            )}
            {reportSettings.stationCH.isActive && (
              <Custominput
                title1={`${t('Station')} (${station} x ${
                  reportSettings.stationCH.value
                })`}
                value={station}
                onChange={setstation}
              />
            )}
            {reportSettings.sturupAirport.isActive && (
              <Custominput
                title1={`${t('Sturup')} (${sturup} x ${
                  reportSettings.sturupAirport.value
                })`}
                value={sturup}
                onChange={setsturup}
              />
            )}
            <Custominput
              title1={t('Before')}
              value={beforeValue.toString()}
              onChange={setBeforeValue}
              editable={false}
            />
            <Custominput
              title1={t('After')}
              value={afterValue.toString()}
              onChange={setAfterValue}
              editable={false}
            />
            {reportSettings.ownerPercentage.isActive && (
              <Custominput
                title1={t('OwnerPercentage')}
                value={ownerPercentage.toString()}
                onChange={setOwnerPercentage}
                editable={false}
              />
            )}
            {reportSettings.driverPercentage.isActive && (
              <Custominput
                title1={t('DriverPercentage')}
                value={driverPercentage.toString()}
                onChange={setDriverPercentage}
                editable={false}
              />
            )}
            <Custominput
              title1={t('Note')}
              value={note}
              onChange={setNote}
              keyboardType="default"
              Input={input1}
              textAlignVertical="top"
              multiline={true}
            />
          </View>
          <View style={styles.BtnContainer}>
            {loading ? (
              <ActivityIndicator size="large" color={textcolor.color2} />
            ) : (
              <CustomButton
                title={t('Submit')}
                onPress={submitReport}
                style={styles.submitButton}
              />
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  header: {
    marginVertical: 10,
    alignItems: 'center',
  },
  text: {
    fontSize: 28,
    fontWeight: '700',
    color: textcolor.color2,
    textAlign: 'center',
    marginTop: -10,
  },
  inputForm: {
    flexDirection: 'column',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
  },
  BtnContainer: {
    marginTop: 30,
    marginBottom: 15,
  },
});

export default AddRapport;
