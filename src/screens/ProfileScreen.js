import React, {useState} from 'react';
import {
  View,
  Text,
  ImageBackground,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Logo from '../../src/assets/Image/bgImg.png';
import Feather from 'react-native-vector-icons/Feather';
import {textcolor} from '../styles/style';
import Img1 from '../../src/assets/Image/Menu/User.png';
import Img2 from '../../src/assets/Image/Menu/Harddrive.png';
import Img3 from '../../src/assets/Image/Menu/localtaxi.png';
import Img4 from '../../src/assets/Image/Menu/Phone.png';
import Img5 from '../../src/assets/Image/Menu/Mail.png';
import {useTranslation} from 'react-i18next';
import BackButton from '../components/BackButton';
const ProfileScreen = ({navigation}) => {
  const {width, height} = Dimensions.get('window');

  const {t} = useTranslation();
  const [name, setName] = useState(null);
  const [taxiNumber, setTaxiNumber] = useState(null);
  const [companyLogo, setCompanyLogo] = useState(null);
  const [mobileNumber, setMobileNumber] = useState(null);
  const [email, setEmail] = useState(null);
  const [profilePic, setProfilePic] = useState(null);

  const fetchUserData = async () => {
    try {
      const user = await AsyncStorage.getItem('User');
      const parsedUser = user ? JSON.parse(user) : null;
      setName(parsedUser?.name || '');
      setTaxiNumber(parsedUser?.taxiNumber || '');
      setCompanyLogo(parsedUser?.taxiCompany.companyLogo || '');
      setMobileNumber(parsedUser?.mobileNumber || '');
      setProfilePic(parsedUser?.profilePic || null);
      setEmail(parsedUser?.email || '');
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      fetchUserData();
    }, []),
  );

  return (
    <View style={styles.container}>
      <ImageBackground source={Logo} style={styles.background}>
        <View style={styles.navBar}>
          <View style={styles.backBtn}>
            <BackButton />
          </View>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Text
              style={{
                alignSelf: 'center',
                fontSize: 25,
                fontWeight: '700',
                color: 'black',
                marginRight: width * 0.2,
                marginTop: '10%',
              }}>
              {t('Profile')}
            </Text>
            <TouchableOpacity
              onPress={() => {
                navigation.navigate(t('Editprofile'));
              }}
              style={{marginTop: '10%', marginRight: 10}}>
              <Feather name="edit" size={30} color={'black'} />
            </TouchableOpacity>
          </View>
        </View>

        <View>
          <View style={styles.content}>
            <View style={styles.imageContainer}>
              {profilePic && profilePic.startsWith('http') ? (
                <Image source={{uri: profilePic}} style={styles.image} />
              ) : (
                <Image source={Img1} style={styles.image} />
              )}
            </View>
            <View style={styles.detail}>
              <Image source={Img1} style={styles.Icon} />
              <View>
                <Text style={styles.name}>{t('fullName')}</Text>
                <Text style={styles.text}>{name}</Text>
              </View>
            </View>
            <View style={styles.detail}>
              <Image source={Img2} style={styles.Icon} />
              <View>
                <Text style={styles.name}>{t('selectTaxiCompany')}</Text>
                <Image
                  source={
                    companyLogo
                      ? {uri: companyLogo}
                      : require('../assets/Image/Menu/Upload.png')
                  }
                  style={styles.companyLogo}
                />
              </View>
            </View>
            <View style={styles.detail}>
              <Image source={Img3} style={styles.Icon} />
              <View>
                <Text style={styles.name}>{t('taxiNumber')}</Text>
                <Text style={styles.text}>{taxiNumber}</Text>
              </View>
            </View>
            <View style={styles.detail}>
              <Image source={Img4} style={styles.Icon} />
              <View>
                <Text style={styles.name}>{t('mobileno')}</Text>
                <Text style={styles.text}>{mobileNumber}</Text>
              </View>
            </View>
            <View style={styles.detail}>
              <Image source={Img5} style={styles.Icon} />
              <View>
                <Text style={styles.name}>{t('email')}</Text>
                <Text style={styles.text}>{email}</Text>
              </View>
            </View>
          </View>
        </View>
      </ImageBackground>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 7,
  },
  backBtn: {
    marginTop: '10%',
  },
  content: {
    marginBottom: '20%',
  },
  image: {
    width: 70,
    height: 70,
    borderColor: 'black',
    borderWidth: 1,
    borderRadius: 50,
    alignSelf: 'center',
    marginTop: 45,
  },
  detail: {
    flexDirection: 'row',
    marginLeft: '20%',
    marginTop: '7%',
  },
  Icon: {
    marginRight: 10,
    marginTop: 7,
  },
  imagecom: {
    width: '100%',
    height: 30,
  },
  name: {
    fontSize: 14,
    fontWeight: '400',
    color: textcolor.color1,
    marginBottom: 3,
  },
  text: {
    fontSize: 12,
    fontWeight: '400',
    color: textcolor.color1,
  },
  companyLogo: {
    width: '100%',
    height: 30,
    resizeMode: 'contain',
  },
  imageContainer: {marginBottom: 10},
});

export default ProfileScreen;
