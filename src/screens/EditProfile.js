import {
  ImageBackground,
  ScrollView,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BackButton from '../components/BackButton';
import {textcolor} from '../styles/style';
import CustomTextinput from '../components/CustomTextinput';
import CustomDropdown from '../components/CustomDropdown';
import {useTranslation} from 'react-i18next';
import Logo from '../assets/Image/bgImg.png';
import CustomButton from '../components/CustomButton';
import {useNavigation} from '@react-navigation/native';
import {launchImageLibrary, launchCamera} from 'react-native-image-picker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {config} from '../../config';

const EditProfile = () => {
  const {t} = useTranslation();
  const navigation = useNavigation();

  const [userId, setUserId] = useState(null);
  const [name, setName] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [taxiCompany, setTaxiCompany] = useState('');
  const [taxiNumber, setTaxiNumber] = useState('');
  const [profilePic, setProfileImage] = useState(null);
  const [companyLogo, setCompanyLogo] = useState(null);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [isLoading, setIsLoading] = useState(false); // Loading state

  const taxiCompanies = [
    {
      name: 'Taxi 232323',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465225/232323_uqmxtn.jpg',
    },
    {
      name: 'Svedtax Skane 13 80 00',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465221/Skane138000_qyvr0q.jpg',
    },
    {
      name: 'Taxi Malmo',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465221/Malmo_lmxfvf.jpg',
    },
    {
      name: 'Taxi Vellinge',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465222/V_csscif.jpg',
    },
    {
      name: 'Taxi Grand Skane',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465221/grandSkane_e3lqtz.jpg',
    },
    {
      name: 'Taxi LUND 121212',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465221/Lund121212_eevrhh.jpg',
    },
    {
      name: 'Taxi Kurir',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465221/kurir_e2qy1t.jpg',
    },
    {
      name: 'Taxi 97',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465220/97_plwq29.jpg',
    },
    {
      name: 'Taxi Skane',
      logo: 'https://res.cloudinary.com/dvajua78e/image/upload/v1726465221/skane_dgb0gl.jpg',
    },
  ];

  const handleCompanySelect = item => {
    setSelectedCompany(item);
    setTaxiCompany({companyName: item.name, companyLogo: item.logo});
  };

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const user = await AsyncStorage.getItem('User');
        const parsedUser = user ? JSON.parse(user) : null;

        if (parsedUser) {
          setUserId(parsedUser._id || null);
          setName(parsedUser.name || '');
          setMobileNumber(parsedUser.mobileNumber || '');
          setTaxiNumber(parsedUser.taxiNumber || '');
          setProfileImage(parsedUser.profilePic || null);

          // Handle taxiCompany directly
          const taxiCompanyData = parsedUser.taxiCompany || {
            companyName: '',
            companyLogo: '',
          };
          setTaxiCompany(taxiCompanyData);

          // Find and set selectedCompany from taxiCompanies
          const selectedTaxi = taxiCompanies.find(
            company => company.name === taxiCompanyData.companyName,
          );
          setSelectedCompany(selectedTaxi || null);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };

    fetchUserData();
  }, []);

  const handleImagePicker = () => {
    Alert.alert(
      'Select Image Source',
      'Choose an option to pick an image',
      [
        {
          text: 'Camera',
          onPress: () =>
            launchCamera(
              {mediaType: 'photo', quality: 1, saveToPhotos: true},
              handleResponse,
            ),
        },
        {
          text: 'Gallery',
          onPress: () =>
            launchImageLibrary(
              {mediaType: 'photo', quality: 1},
              handleResponse,
            ),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ],
      {cancelable: true},
    );
  };

  const handleResponse = response => {
    if (response.didCancel) {
      console.log('User cancelled image picker');
    } else if (response.error) {
      console.error('ImagePicker Error: ', response.error);
    } else if (response.assets) {
      setProfileImage(response.assets[0].uri);
    }
  };

  const handleSave = async () => {
    if (!userId) return Alert.alert('User ID not found');
    setIsLoading(true);
    try {
      const formData = new FormData();
      formData.append('name', name);
      formData.append('mobileNumber', mobileNumber);

      // Send taxiCompany as stringified JSON
      formData.append(
        'taxiCompany',
        JSON.stringify({
          companyName: selectedCompany?.name || '',
          companyLogo: selectedCompany?.logo || '',
        }),
      );
      formData.append('taxiNumber', taxiNumber);

      if (profilePic) {
        formData.append('profilePic', {
          uri: profilePic,
          type: 'image/jpeg',
          name: 'profile.jpg',
        });
      }

      const response = await fetch(
        `${config.baseUrl}/auth/update-profile/${userId}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          body: formData,
        },
      );

      if (response.ok) {
        const updatedUser = await response.json();
        await AsyncStorage.setItem('User', JSON.stringify(updatedUser.user));
        Alert.alert('Profile updated successfully');
        navigation.navigate('Profilescreen');
      } else {
        const errorData = await response.json();
        Alert.alert(
          'Failed to update profile',
          errorData.message || 'Unknown error',
        );
      }
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <ImageBackground source={Logo} style={styles.background}>
        <View style={styles.header}>
          <BackButton />
          <Text style={styles.profile}>{t('Edit')}</Text>
        </View>

        <ScrollView
          contentContainerStyle={{flexGrow: 1}}
          keyboardShouldPersistTaps="handled">
          <TouchableOpacity
            onPress={handleImagePicker}
            style={styles.imagecontainer}>
            {profilePic && profilePic.startsWith('http') ? (
              <Image source={{uri: profilePic}} style={styles.image} />
            ) : (
              <Icon
                name="add-a-photo"
                size={30}
                color="#ebc634"
                style={styles.icon}
              />
            )}
          </TouchableOpacity>

          <CustomTextinput
            label={t('fullName')}
            placeholder={t('enterFullName')}
            value={name}
            onChangeText={setName}
          />

          <CustomDropdown
            label={t('selectTaxiCompany')}
            options={taxiCompanies}
            selectedOption={selectedCompany}
            onSelect={handleCompanySelect}
          />
          <CustomTextinput
            label={t('mobileno')}
            placeholder={t('entermobileno')}
            keyboardType={'number-pad'}
            value={mobileNumber}
            onChangeText={setMobileNumber}
          />
          <CustomTextinput
            label={t('taxiNumber')}
            placeholder={t('enterTaxiNumber')}
            keyboardType={'number-pad'}
            value={taxiNumber}
            onChangeText={setTaxiNumber}
          />
          {isLoading ? (
            <ActivityIndicator
              style={{marginTop: '8%', marginBottom: '6%'}}
              size="large"
              color={textcolor.color2}
            />
          ) : (
            <View style={{marginTop: '8%', marginBottom: '6%'}}>
              <CustomButton title={t('Save')} onPress={handleSave} />
            </View>
          )}
        </ScrollView>
      </ImageBackground>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 10,
    marginTop: '10%',
  },
  profile: {
    fontSize: 25,
    fontWeight: '700',
    alignSelf: 'center',
    marginRight: '30%',
    color: textcolor.color1,
  },
  imagecontainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignSelf: 'center',
    marginTop: '5%',
    backgroundColor: textcolor.color1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignSelf: 'center',
  },
  icon: {
    position: 'absolute',
    top: '60%',
    right: '10%',
    alignSelf: 'flex-end',
  },
  companyLogo: {
    width: 100,
    height: 100,
    borderRadius: 10,
    alignSelf: 'center',
    marginTop: 10,
  },
});

export default EditProfile;
