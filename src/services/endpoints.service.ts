import {config} from '../../config';

export const baseUrl = config.baseUrl;

export const moduleName = {
  auth: '/auth',
  user: '/user',
};

const authEndPoints = {
  login: baseUrl + moduleName.auth + '/login',
  register: baseUrl + moduleName.auth + '/sign-up',
  otp: baseUrl + moduleName.auth + '/otp',
  forgetPassword: baseUrl + moduleName.auth + '/forget-password',
};
const reportSettingEndPoints = {
  reportSetting: (userId: string) =>  
    baseUrl + moduleName.auth + `/report-setting/${userId}`,
  // addReport: (userId: string) =>
  //   baseUrl + `reports/${userId}`
};
const userEndpoints = {
  deleteUser: (userId: string) =>  baseUrl + moduleName.auth + `/user/${userId}` ,
};


export {authEndPoints, reportSettingEndPoints,userEndpoints};
