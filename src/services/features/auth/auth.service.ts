import AsyncStorage from '@react-native-async-storage/async-storage';
import {authEndPoints, userEndpoints} from '../../endpoints.service';

class AuthService {
  async register(payload: {
    name: string;
    email: string;
    password: string;
    mobileNumber: string;
    taxiCompany: {
      companyName: string;
      companyLogo: string;
    };
    taxiNumber: string;
  }) {
    try {
      const response = await fetch(`${authEndPoints.register}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      // Parse response data
      const data = await response.json();

      if (!response.ok) {
        // Throw error with detailed message
        throw new Error(data.error || data.message || 'Registration Failed');
      }

      return data;
    } catch (error) {
      // Log the entire error object for more context
      console.error('Error in registration:', JSON.stringify(error, null, 2));
      throw error;
    }
  }
  async otp(payload: {otp: string; userId: string}) {
    try {
      const response = await fetch(`${authEndPoints.otp}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
      const data = await response.json();
      // console.log('response:', response);
      if (!response.ok) {
        // Throw error with detailed message
        throw new Error(data.error || data.message || 'User not Verified');
      }
      return data;
    } catch (error) {
      // Log the entire error object for more context
      console.error('Error in Verification:', error);
    }
  }
  async login(payload: {email: string; password: string}) {
    try {
      const response = await fetch(`${authEndPoints.login}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (!response.ok) {
        if (
          data.error === 'User not verified! OTP sent to your email address.'
        ) {
          return {otpRequired: true, message: data.error};
        }
        throw new Error(data.error || 'Login Failed !!');
      }

      return {success: true, message: 'Login Successfully !!', data};
    } catch (error) {
      console.log('Error during login:', error);
      throw error;
    }
  }
  async forgetPassword(payload: {email: string}) {
    try {
      const response = await fetch(`${authEndPoints.forgetPassword}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      // console.log('auth service', error);
      return error;
    }
  }
   async deleteAccount(userId: string) {
    try {
      const token = await AsyncStorage.getItem('token');
      console.log('userId',userId)
      const response = await fetch(`${userEndpoints.deleteUser(userId)}`, {
        
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();
          console.log('sdfff',data);
          
      if (!response.ok) {
        throw new Error(data.message || 'Failed to delete account');
      }

      return data;
    } catch (error) {
      console.error('Error deleting account:', error);
      throw error;
    }
  }

}

export default new AuthService();
