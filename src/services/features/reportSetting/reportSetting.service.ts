import { reportSettingEndPoints } from '../../endpoints.service';

class reportSettingService {
  async reportSetting(
    userId: string,
    payload: {
      total: { isActive: boolean };
      onlyTotal: { isActive: boolean };
      cash: { isActive: boolean };
      prePaid: { isActive: boolean };
      tip: { isActive: boolean };
      kastrupAirport: { isActive: boolean; value: number };
      stationCH: { isActive: boolean; value: number };
      sturupAirport: { isActive: boolean; value: number };
      tax: { isActive: boolean; value: number };
      percentage: { isActive: boolean; value: number };
      holiday: { isActive: boolean; value: number };
    },
  ) {
    try {
      const response = await fetch(`${reportSettingEndPoints.reportSetting(userId)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),  // Ensure the payload is stringified
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }

      const data = await response.json();
      return data;  // Return the response data
    } catch (error) {
      console.error('Error in reportSetting:', error);
      throw error;  // Propagate error
    }
  }
}

export default new reportSettingService();
