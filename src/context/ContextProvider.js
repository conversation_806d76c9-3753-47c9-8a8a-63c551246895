import React, {createContext, useContext, useState} from 'react';
export const AppContext = createContext();

export const AppProvider = ({children}) => {
  const [reportData, setReportData] = useState(['']);

  return (
    <AppContext.Provider value={{reportData, setReportData}}>
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = () => {
  const context = useContext(AppContext);

  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
