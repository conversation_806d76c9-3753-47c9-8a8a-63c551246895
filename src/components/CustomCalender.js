// import {StyleSheet, Text, View, TouchableOpacity} from 'react-native';
// import React, {useState, useEffect, useCallback} from 'react';
// import {useFocusEffect} from '@react-navigation/native';
// import {Calendar} from 'react-native-calendars';
// import {buttoncolor, textcolor} from '../styles/style';
// import {config} from '../../config';
// import AsyncStorage from '@react-native-async-storage/async-storage';
// import { useTranslation } from 'react-i18next';

// const CustomCalendar = ({onDateSelect}) => {
//   const { t } = useTranslation();
//   const [selected, setSelected] = useState('');
//   const [currentDate, setCurrentDate] = useState('');
//   const [incomeData, setIncomeData] = useState({});
//   const [userId, setUserId] = useState(null);
//   const [currentMonth, setCurrentMonth] = useState('');
//   const [currentYear, setCurrentYear] = useState('');

//   const handleDayPress = day => {
//     const income = incomeData[day.dateString];
//     const afterDetaction = income ? income.afterDetaction : 0;
//     const beforeDetaction = income ? income.beforeDetaction : 0;
//     setSelected(day.dateString);
//     onDateSelect(day.dateString, beforeDetaction, afterDetaction);
//   };

//   useEffect(() => {
//     const fetchUserData = async () => {
//       try {
//         const user = await AsyncStorage.getItem('User');
//         const parsedUser = user ? JSON.parse(user) : null;
//         const fetchedUser = parsedUser?._id || null;
//         if (fetchedUser) {
//           setUserId(fetchedUser);
//         } else {
//           console.log('No user ID available for API request 1 :', userId);
//         }
//       } catch (error) {
//         console.error('Error fetching user data:', error);
//       }
//     };

//     fetchUserData();
//   }, [userId]);

//   const fetchReports = async userId => {
//     const apiUrl = `${config.baseUrl}/reports/${userId}`;
//     try {
//       const response = await fetch(apiUrl);
//       const result = await response.json();

//       if (result && result.length > 0) {
//         const dataMap = result.reduce((acc, report) => {
//           const reportDate = report.date.includes('/')
//             ? report.date.split('/').reverse().join('-')
//             : report.date;

//           if (!acc[reportDate]) {
//             acc[reportDate] = {
//               afterDetaction: 0,
//               beforeDetaction: 0,
//               totalReports: [],
//             };
//           }

//           acc[reportDate].afterDetaction += report.afterDetaction;
//           acc[reportDate].beforeDetaction += report.beforeDetaction;
//           acc[reportDate].totalReports.push(report.total);

//           return acc;
//         }, {});

//         setIncomeData(dataMap);
//       }
//     } catch (error) {
//       console.error('Error fetching reports:', error);
//     }
//   };

//   useFocusEffect(
//     useCallback(() => {
//       if (userId) {
//         fetchReports(userId);
//       } else {
//         console.log('No user ID available for API request 2 :');
//       }
//     }, [userId]),
//   );

//   useEffect(() => {
//     const today = new Date();
//     const dateString = today.toISOString().split('T')[0];
//     setCurrentDate(dateString);
//     setCurrentMonth(today.getMonth());
//     setCurrentYear(today.getFullYear())  ////
//   }, []);

//   const renderDay = day => {
//     const reports = incomeData[day.dateString]?.totalReports || [];
//     const dayDate = new Date(day.dateString);
//     const isCurrentMonth = dayDate.getMonth() === currentMonth;

//     return (
//       <TouchableOpacity
//         style={styles.dayContainer}
//         onPress={() => isCurrentMonth && handleDayPress(day)}
//         disabled={!isCurrentMonth}>
//         <Text style={styles.dayText}>{isCurrentMonth ? day.day : ''}</Text>
//         {isCurrentMonth &&
//           reports.map((report, index) => (
//             <Text key={index} style={styles.incomeText}>
//               {report}Kr
//             </Text>
//           ))}
//       </TouchableOpacity>
//     );
//   };

//   return (
//     <View style={styles.container}>
//       <Calendar
//         onDayPress={handleDayPress}
//         onMonthChange={month =>{
//           setCurrentMonth(new Date(month.dateString).getMonth());
//           setCurrentYear(new Date(month.dateString).getFullYear()); 
//         }}
//         markedDates={{
//           [selected]: {
//             selected: true,
//             disableTouchEvent: true,
//           },
//           [currentDate]: {selected: true, selectedColor: '#fcb603'},
//         }}
//         theme={{
//           monthTextColor: textcolor.color1,
//           textDayFontSize: 16,
//           textMonthFontSize: 16,
//           textDayHeaderFontSize: 16,
//           textSectionTitleColor: textcolor.color1,
//           arrowStyle: {
//             marginHorizontal: 0,
//             paddingHorizontal: 0,
//           },
//           'stylesheet.calendar.header': {
//             header: {
//               flexDirection: 'row',
//               justifyContent: 'center',
//               alignItems: 'center',
//               paddingHorizontal: 0,
//             },
//             arrow: {
//               width: 30,
//               height: 30,
//             },
//           },
//         }}
//         dayComponent={({date, state}) =>
//           renderDay({dateString: date.dateString, day: date.day})
//         }
//         monthFormat="yyyy MM"
//         renderHeader={(date) => {
//           const monthIndex = date.getMonth(); // Get the month index (0-11)
//           const translatedMonth = t(`months.${["jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"][monthIndex]}`); // Translate the month
//           return (
//             <View style={styles.monthHeader}>
//               <Text style={styles.monthText}>
//                 {translatedMonth} {currentYear}
//               </Text>
//             </View>
//           );
//         }}
//       />
//     </View>
//   );
// };

// export default CustomCalendar;

// const styles = StyleSheet.create({
//   container: {
//     marginTop: 25,
//     padding: 10,
//   },
//   dayContainer: {
//     alignItems: 'center',
//   },
//    monthHeader: {
//   //    alignSelf:'center',
//   //  // paddingVertical: 10,
//     paddingHorizontal:12,
//    marginBottom:10
//    },
//   monthText: {
//     fontSize: 15,
//     color: '#000000',
//     fontWeight: '400',
//   },
//   dayText: {
//     fontSize: 15,
//     color: '#000000',
//     fontWeight: '400',
//   },
//   incomeText: {
//     fontSize: 10,
//     fontWeight: '700',
//     color: '#18A0FB',
//   },
// });
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import React, { useState, useEffect, useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { Calendar } from 'react-native-calendars';
import { buttoncolor, textcolor } from '../styles/style';
import { config } from '../../config';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTranslation } from 'react-i18next';

const CustomCalendar = ({ onDateSelect }) => {
  const { t } = useTranslation();
  const [selected, setSelected] = useState('');
  const [currentDate, setCurrentDate] = useState('');
  const [incomeData, setIncomeData] = useState({});
  const [userId, setUserId] = useState(null);
  const [currentMonth, setCurrentMonth] = useState('');
  const [currentYear, setCurrentYear] = useState('');

  const handleDayPress = day => {
    const income = incomeData[day.dateString];
    const afterDetaction = income ? income.afterDetaction : 0;
    const beforeDetaction = income ? income.beforeDetaction : 0;
    setSelected(day.dateString);
    onDateSelect(day.dateString, beforeDetaction, afterDetaction);
  };

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const user = await AsyncStorage.getItem('User');
        const parsedUser = user ? JSON.parse(user) : null;
        const fetchedUser = parsedUser?._id || null;
        if (fetchedUser) {
          setUserId(fetchedUser);
        } else {
          console.log('No user ID available for API request 1 :', userId);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };

    fetchUserData();
  }, [userId]);

  const fetchReports = async userId => {
    const apiUrl = `${config.baseUrl}/reports/${userId}`;
    try {
      const response = await fetch(apiUrl);
      const result = await response.json();

      if (result && result.length > 0) {
        const dataMap = result.reduce((acc, report) => {
          const reportDate = report.date.includes('/')
            ? report.date.split('/').reverse().join('-')
            : report.date;

          if (!acc[reportDate]) {
            acc[reportDate] = {
              afterDetaction: 0,
              beforeDetaction: 0,
              totalReports: [],
            };
          }

          acc[reportDate].afterDetaction += report.afterDetaction;
          acc[reportDate].beforeDetaction += report.beforeDetaction;
          acc[reportDate].totalReports.push(report.total);

          return acc;
        }, {});

        setIncomeData(dataMap);
      }
    } catch (error) {
      console.error('Error fetching reports:', error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (userId) {
        fetchReports(userId);
      } else {
        console.log('No user ID available for API request 2 :');
      }
    }, [userId]),
  );

  useEffect(() => {
    const today = new Date();
    const dateString = today.toISOString().split('T')[0];
    setCurrentDate(dateString);
    setCurrentMonth(today.getMonth());
    setCurrentYear(today.getFullYear());
  }, []);

  const renderDay = day => {
    const reports = incomeData[day.dateString]?.totalReports || [];
    const dayDate = new Date(day.dateString);
    const isCurrentMonth = dayDate.getMonth() === currentMonth;

    return (
      <TouchableOpacity
        style={styles.dayContainer}
        onPress={() => isCurrentMonth && handleDayPress(day)}
        disabled={!isCurrentMonth}>
        <Text style={styles.dayText}>{isCurrentMonth ? day.day : ''}</Text>
        {isCurrentMonth &&
          reports.map((report, index) => (
            <Text key={index} style={styles.incomeText}>
              {report}Kr
            </Text>
          ))}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Calendar
        onDayPress={handleDayPress}
        onMonthChange={month => {
          setCurrentMonth(new Date(month.dateString).getMonth());
          setCurrentYear(new Date(month.dateString).getFullYear());
        }}
        markedDates={{
          [selected]: {
            selected: true,
            disableTouchEvent: true,
          },
          [currentDate]: { selected: true, selectedColor: '#fcb603' },
        }}
        theme={{
          monthTextColor: textcolor.color1,
          textDayFontSize: 16,
          textMonthFontSize: 16,
          textSectionTitleColor: textcolor.color1,
          arrowStyle: {
            marginHorizontal: 0,
            paddingHorizontal: 0,
          },
          'stylesheet.calendar.header': {
            header: {
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              paddingHorizontal: 0,
            },
            arrow: {
              width: 30,
              height: 30,
            },
          },
        }}
        dayComponent={({ date, state }) =>
          renderDay({ dateString: date.dateString, day: date.day })
        }
        monthFormat="yyyy MM"
        renderHeader={(date) => {
          const monthIndex = date.getMonth(); // Get the month index (0-11)
          const translatedMonth = t(`months.${["jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"][monthIndex]}`); // Translate the month
          return (
            <View style={styles.monthHeader}>
              <Text style={styles.monthText}>
                {translatedMonth} {currentYear}
              </Text>
            </View>
          );
        }}
      />
    </View>
  );
};

export default CustomCalendar;

const styles = StyleSheet.create({
  container: {
    marginTop: 25,
    padding: 10,
  },
  dayContainer: {
    alignItems: 'center',
  },
  monthHeader: {
    paddingHorizontal: 12,
    marginBottom: 10,
  },
  monthText: {
    fontSize: 15,
    color: '#000000',
    fontWeight: '400',
  },
  weekHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10, // Adjusted margin for spacing between month header and week header
    width: '100%',
  },
  dayHeaderText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#000000',
  },
  dayText: {
    fontSize: 15,
    color: '#000000',
    fontWeight: '400',
  },
  incomeText: {
    fontSize: 10,
    fontWeight: '700',
    color: '#18A0FB',
  },
});
