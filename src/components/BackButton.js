import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faChevronLeft } from '@fortawesome/free-solid-svg-icons';
import { useNavigation } from '@react-navigation/native';

const BackButton = () => {
  const navigation = useNavigation();

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => navigation.goBack()} 
    >
      <FontAwesomeIcon icon={faChevronLeft} size={26} color="#000" />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FCCC31',
    borderRadius: 10,
    padding: 8,
    margin:5,
    flexDirection: 'row',
    height: 40,
    width: 40,
  },
});

export default BackButton;
