import {StyleSheet, Text, TouchableOpacity, Dimensions} from 'react-native';
import React from 'react';
import {buttoncolor, textcolor} from '../styles/style';

const {width, height} = Dimensions.get('window');
const CustomButton = ({title, onPress, style}) => {
  return (
    <TouchableOpacity
      style={[styles.button, style]}
      onPress={() => {
        onPress();
      }}>
      <Text style={[styles.text, style]}>{title}</Text>
    </TouchableOpacity>
  );
};

export default CustomButton;

const styles = StyleSheet.create({
  button: {
    width: '35%',
    paddingHorizontal: 12,
    height: height * 0.05,
    borderRadius: 18,
    backgroundColor: buttoncolor.color,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  text: {
    fontSize: width * 0.04,
    fontWeight: '700',
    color: textcolor.color1,
  },
});
