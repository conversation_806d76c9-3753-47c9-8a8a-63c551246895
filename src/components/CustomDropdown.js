import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  ScrollView,
  Image,
  Modal,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';
import { buttoncolor, textcolor } from '../styles/style';
import { useTranslation } from 'react-i18next';

const { width, height } = Dimensions.get('window');

const CustomDropdown = ({ label, options, selectedOption, onSelect }) => {
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const { t } = useTranslation();

  const toggleDropdownVisibility = () => {
    setIsDropdownVisible(!isDropdownVisible);
  };

  const handleOptionSelect = (option) => {
    onSelect(option); // Send the selected option back to the parent component
    setIsDropdownVisible(false); // Hide dropdown after selection
  };

  return (
    <View style={styles.inputContainer}>
      <Text style={styles.label}>{label}</Text>
      <TouchableOpacity onPress={toggleDropdownVisibility} style={styles.dropdown}>
        {selectedOption ? (
          <View style={styles.selectedContainer}>
            <Image source={{ uri: selectedOption.logo }} style={styles.logo} />
            <Text style={styles.input}>{selectedOption.name}</Text>
          </View>
        ) : (
          <TextInput
            style={styles.input}
            value={t('selectCompany')}
            editable={false}
            placeholder={t('selectCompany')}
          />
        )}
        <Icon name="caretdown" size={18} style={{ color: textcolor.color1 }} />
      </TouchableOpacity>

      {/* Dropdown Modal */}
      <Modal visible={isDropdownVisible} transparent animationType="fade">
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsDropdownVisible(false)}
        >
          <View style={styles.modalContainer}>
            <ScrollView style={styles.scrollView} nestedScrollEnabled>
              {options.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.option}
                  onPress={() => handleOptionSelect(item)}
                >
                  <Image source={{ uri: item.logo }} style={styles.logo} />
                  <Text style={styles.optionText}>{item.name}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    alignSelf: 'center',
    marginTop: height * 0.01,
    width: '90%',
  },
  label: {
    fontSize: width * 0.033,
    fontWeight: '400',
    marginBottom: width * 0.01,
    marginHorizontal: 10,
    color: textcolor.color1,
    fontFamily: 'Roboto-Medium',
  },
  dropdown: {
    width: '100%',
    height: height * 0.053,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 10,
    backgroundColor: buttoncolor.bgcolor,
    paddingHorizontal: 15,
  },
  selectedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  input: {
    flex: 1,
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'Roboto-Medium',
    color: textcolor.color4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Semi-transparent background
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 10,
    maxHeight: height * 0.4, // Prevents dropdown from taking the whole screen
    elevation: 5,
    zIndex: 1000, // Ensures it stays on top in iOS
  },
  scrollView: {
    borderRadius: 10,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  logo: {
    width: 30,
    height: 30,
    resizeMode: 'cover',
    marginRight: 10,
  },
  optionText: {
    fontSize: 14,
    fontWeight: '400',
    fontFamily: 'Roboto-Medium',
    color: textcolor.color1,
  },
});

export default CustomDropdown;
