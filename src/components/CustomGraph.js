import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { config } from '../../config';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faCaretLeft, faCaretRight } from '@fortawesome/free-solid-svg-icons';
import { useFocusEffect } from '@react-navigation/native';
import { textcolor } from '../styles/style';
import { useTranslation } from 'react-i18next';  // Importing translation hook

const { width, height: screenHeight } = Dimensions.get('window');

const CustomGraph = ({ _id }) => {
  const { t } = useTranslation();  // Initialize translation hook
  const [data, setData] = useState([]);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [scale, setScale] = useState(1);
  const [loading, setLoading] = useState(false);

  useFocusEffect(
    useCallback(() => {
      const fetchReports = async () => {
        if (!_id) return;

        setLoading(true); // Show loading indicator when fetching begins
        try {
          const response = await fetch(`${config.baseUrl}/reports/${_id}`);
          const reports = await response.json();

          if (Array.isArray(reports)) {
            const filteredReports = reports.filter(report => {
              const [day, month, year] = report.date.split('/').map(Number);
              return (
                month === currentMonth.getMonth() + 1 &&
                year === currentMonth.getFullYear()
              );
            });

            const processedData = filteredReports
              .map(report => ({
                beforeDetection: report.beforeDetaction,
                total: report.total,
                date: report.date,
              }))
              .sort((a, b) => {
                const [dayA, monthA, yearA] = a.date.split('/').map(Number);
                const [dayB, monthB, yearB] = b.date.split('/').map(Number);

                const dateA = new Date(yearA, monthA - 1, dayA);
                const dateB = new Date(yearB, monthB - 1, dayB);

                return dateA - dateB;
              });

            setData(processedData);
          }
        } catch (error) {
          console.error('Error fetching report data:', error);
        } finally {
          setLoading(false); // Hide loading indicator when fetching completes
        }
      };

      fetchReports();
    }, [_id, currentMonth]),
  );

  const zoomIn = () => setScale(prevScale => Math.min(prevScale + 0.2, 3));
  const zoomOut = () => setScale(prevScale => Math.max(prevScale - 0.2, 1));
  const maxDataValue = useMemo(
    () => Math.max(...data.map(item => item.beforeDetection), 1),
    [data],
  );

  const verticalLabels = [
    10000, 9500, 9000, 8500, 8000, 7500, 7000, 6500, 6000, 5500, 5000, 4500,
    4000, 3500, 3000, 2500, 2000, 1500, 1000, 500, 0,
  ];

  const incrementMonth = () => {
    setCurrentMonth(
      prevMonth =>
        new Date(prevMonth.getFullYear(), prevMonth.getMonth() + 1, 1),
    );
  };

  const decrementMonth = () => {
    setCurrentMonth(
      prevMonth =>
        new Date(prevMonth.getFullYear(), prevMonth.getMonth() - 1, 1),
    );
  };

  const verticalLabelPositions = verticalLabels.map(value => {
    const labelPosition = (value / maxDataValue) * (screenHeight * 0.6);
    return labelPosition;
  });

  const barWidthBase = 25;
  const barMaxHeight = screenHeight * 0.6;

  // Translate the current month name
  const translatedMonth = t(`months.${["jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"][currentMonth.getMonth()]}`);

  return (
    <View style={styles.container}>
      <View style={styles.monthContainer}>
        <TouchableOpacity onPress={decrementMonth}>
          <FontAwesomeIcon icon={faCaretLeft} color={'#42b6f5'} />
        </TouchableOpacity>
        <Text style={styles.month}>
          {translatedMonth} {currentMonth.getFullYear()}
        </Text>
        <TouchableOpacity onPress={incrementMonth}>
          <FontAwesomeIcon icon={faCaretRight} color={'#42b6f5'} />
        </TouchableOpacity>
      </View>
      {loading ? (
        <View style={styles.noDataContainer}>
          <ActivityIndicator size="large" color={textcolor.color2} />
        </View>
      ) : data.length === 0 ? (
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>
            No data available for this month
          </Text>
        </View>
      ) : (
        <>
          <ScrollView
            horizontal
            contentContainerStyle={styles.graphScrollContainer}
            showsHorizontalScrollIndicator={false}>
            <View style={styles.graphContainer}>
              <View style={styles.graphContent}>
                {data.map((report, index) => (
                  <View key={index} style={styles.graphBarContainer}>
                    <Text style={styles.graphText}>
                      {report.beforeDetection} Kr
                    </Text>
                    <TouchableOpacity
                      style={[
                        styles.graphBar,
                        {
                          height:
                            (report.beforeDetection / maxDataValue) *
                            barMaxHeight,
                          width: barWidthBase * scale - 10,
                          // borderBottomWidth: 3,
                        },
                      ]}
                    />
                     <View style={{borderTopWidth:3,borderColor:'#0c7ecf'}}>
                    <Text style={styles.dayLabel}>
                      {report.date.split('/')[0]}
                    </Text>
                    </View>
                  </View>
                ))}
              </View>
            </View>
          </ScrollView>

          {/* Vertical Labels */}
          <View style={styles.verticalLabelsContainer}>
            {verticalLabels.map((value, index) => {
              const labelPosition = verticalLabelPositions[index];
              return (
                <Text
                  key={value}
                  style={[styles.verticalLabel, { bottom: labelPosition }]}>
                  {value}
                </Text>
              );
            })}
          </View>
        </>
      )}
      <View style={styles.zoomButtonsContainer}>
        <TouchableOpacity style={styles.zoomButton} onPress={zoomIn}>
          <Text style={styles.zoomButtonText}>+</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.zoomButton} onPress={zoomOut}>
          <Text style={styles.zoomButtonText}>-</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: 'white',
  },
  monthContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
  },
  month: {
    color: 'black',
    marginHorizontal: 15,
  },
  graphScrollContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  graphContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginLeft: 25,
    //borderBottomWidth: 3,
  },
  graphContent: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginLeft: 12,
  },
  graphBarContainer: {
    alignItems: 'center',
    marginLeft: -13,
    marginTop: 14,
  },
  graphBar: {
    backgroundColor: '#ebc634',
    justifyContent: 'flex-end',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    
  },
  graphText: {
    color: 'black',
    fontSize: 10,
    fontWeight: 'bold',
    transform: [{ rotate: '-90deg' }],
    marginBottom: 14,
  },
  dayLabel: {
    color: '#333',
    fontSize: 10,
    marginTop: 6,
    marginHorizontal: 10,
  },
  verticalLabelsContainer: {
    position: 'absolute',
    left: 0,
    width: 48,
    bottom: 0,
    height: '97%',
    marginVertical: '4.1%',
    alignItems: 'flex-end',
    paddingRight: 1,
    backgroundColor: '#fff',
    borderRightWidth: 3,
    borderColor:'#0c7ecf'
  },
  verticalLabel: {
    color: '#333',
    fontSize: 12,
    textAlign: 'right',
    paddingRight: 3,
    paddingBottom: 13,
    position: 'absolute',
  },
  zoomButtonsContainer: {
    position: 'absolute',
    right: 10,
    top: 7,
    flexDirection: 'row',
  },
  zoomButton: {
    backgroundColor: '#dedcdc',
    borderRadius: 20,
    marginLeft: 4,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomButtonText: {
    color: 'black',
    fontSize: 20,
    fontWeight: 'bold',
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: screenHeight * 0.7,
  },
  noDataText: {
    fontSize: 18,
    color: 'gray',
    textAlign: 'center',
  },
});

export default CustomGraph;


