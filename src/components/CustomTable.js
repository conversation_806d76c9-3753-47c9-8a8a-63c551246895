import AsyncStorage from '@react-native-async-storage/async-storage';
import {useFocusEffect} from '@react-navigation/native';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import {config} from '../../config';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faCaretLeft, faCaretRight} from '@fortawesome/free-solid-svg-icons';
import {faEdit} from '@fortawesome/free-solid-svg-icons';
import {icon} from '@fortawesome/fontawesome-svg-core';
import ReportModal from './ReportModal';
import {useAppContext} from '../context/ContextProvider';
import {useTranslation} from 'react-i18next';
// import Animated, {
//   useSharedValue,
//   useAnimatedStyle,
//   withTiming,
// } from 'react-native-reanimated';
// import {GestureDetector, Gesture} from 'react-native-gesture-handler';

const {width, height} = Dimensions.get('window');
const CustomTable = ({days, onTotalsCalculated}) => {
  const {
    reportData: reportDataFromContext,
    setReportData: setReportDataFromContext,
  } = useAppContext();
  const [userId, setUserId] = useState(null);
  const [reportData, setReportData] = useState([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [iconPressed, setIconPressed] = useState(null);
  const [reportSettings, setReportSettings] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [reportId, setReportId] = useState(null);
  const [loading, setLoading] = useState(false); // Add loading state
  const {t} = useTranslation();

  // const scale = useSharedValue(1); // Default scale is 1

  // // Pinch gesture handler
  // const pinchGesture = Gesture.Pinch()
  //   .onUpdate(event => {
  //     scale.value = event.scale; // Update scale as user pinches
  //   })
  //   .onEnd(() => {
  //     // Restrict zoom level (1x - 3x)
  //     scale.value = withTiming(Math.max(1, Math.min(scale.value, 3)));
  //   });

  // // Double-tap gesture handler
  // const doubleTapGesture = Gesture.Tap()
  //   .numberOfTaps(2)
  //   .onEnd(() => {
  //     scale.value = withTiming(1); // Reset zoom to default on double tap
  //   });

  // // Combine gestures
  // const gestures = Gesture.Simultaneous(pinchGesture, doubleTapGesture);

  // // Animated style for scaling
  // const animatedStyle = useAnimatedStyle(() => ({
  //   transform: [{scale: scale.value}],
  // }));

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const user = await AsyncStorage.getItem('User');
        const parsedUser = user ? JSON.parse(user) : null;
        setUserId(parsedUser?._id || null);
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };
    fetchUserData();
  }, []);

  // Fetch Report Settings from AsyncStorage or API
  const fetchReportSettings = useCallback(async () => {
    try {
      const storedSettings = await AsyncStorage.getItem('reportSettings');
      if (storedSettings) {
        setReportSettings(JSON.parse(storedSettings));
      } else if (userId) {
        const response = await fetch(
          `${config.baseUrl}/auth/report-setting/${userId}`,
        );
        const data = await response.json();
        await AsyncStorage.setItem('reportSettings', JSON.stringify(data));
        setReportSettings(data);
        //  console.log('Report settings Data : ',data);
      }
    } catch (error) {
      console.error('Error fetching report settings:', error);
    }
  }, [userId]);

  useFocusEffect(
    useCallback(() => {
      fetchReportSettings();
    }, [fetchReportSettings]),
  );
  const apiUrl = userId ? `${config.baseUrl}/reports/${userId}` : null;

  const fetchReports = useCallback(async () => {
    setLoading(true); // Set loading to true before fetching
    try {
      if (!apiUrl) {
        console.log('No user ID available for API request.');
        return;
      }
      const response = await fetch(apiUrl);
      const result = await response.json();

      if (response.ok) {
        const sortedReports = result.sort((a, b) => {
          const [dayA, monthA, yearA] = a.date.split('/').map(Number);
          const [dayB, monthB, yearB] = b.date.split('/').map(Number);
          const dateA = new Date(yearA, monthA - 1, dayA);
          const dateB = new Date(yearB, monthB - 1, dayB);
          return dateA - dateB;
        });
        setReportData(sortedReports);
        setErrorMessage('');
      } else {
        setErrorMessage(
          `Error: ${result.message || 'Failed to fetch reports'}`,
        );
      }
    } catch (error) {
      console.error('Error fetching reports:', error);
      setErrorMessage(`Error fetching reports: ${error.message}`);
    } finally {
      setLoading(false); // Set loading to false after fetching
    }
  }, [apiUrl]);

  useFocusEffect(
    useCallback(() => {
      if (userId) {
        fetchReports();
      }
    }, [userId, fetchReports]),
  );

  const filteredReports = useMemo(() => {
    return reportData.filter(report => {
      const [day, month, year] = report.date.split('/').map(Number);
      return (
        month - 1 === currentMonth.getMonth() &&
        year === currentMonth.getFullYear()
      );
    });
  }, [reportData, currentMonth]);

  const [totals, setTotals] = useState({
    total: 0,
    cash: 0,
    prePaid: 0,
    card: 0,
    FTJ: 0,
    SKOL: 0,
    tip: 0,
    kastrupAirport: 0,
    stationCH: 0,
    sturupAirport: 0,
    extraBill: 0,
    beforeDetaction: 0,
    afterDetaction: 0,
    driverPercentage: 0,
    ownerPercentage: 0,
    notes: [],
  });

  useEffect(() => {
    if (filteredReports.length) {
      const newTotals = filteredReports.reduce(
        (acc, curr) => {
          acc.total += curr.total || 0;
          acc.cash += curr.cash || 0;
          acc.prePaid += curr.prePaid || 0;
          acc.card += curr.card || 0;
          acc.FTJ += curr.FTJ || 0;
          acc.SKOL += curr.SKOL || 0;
          acc.tip += curr.tip || 0;
          acc.kastrupAirport += curr.kastrupAirport || 0;
          acc.stationCH += curr.stationCH || 0;
          acc.sturupAirport += curr.sturupAirport || 0;
          acc.extraBill += curr.extraBill || 0;
          acc.beforeDetaction += curr.beforeDetaction || 0;
          acc.afterDetaction += curr.afterDetaction || 0;
          acc.driverPercentage += curr.driverPercentage || 0;
          acc.ownerPercentage += curr.ownerPercentage || 0;
          acc.notes.push({date: curr.date, note: curr.notes || ''});
          return acc;
        },
        {
          total: 0,
          cash: 0,
          prePaid: 0,
          card: 0,
          FTJ: 0,
          SKOL: 0,
          tip: 0,
          kastrupAirport: 0,
          stationCH: 0,
          sturupAirport: 0,
          extraBill: 0,
          beforeDetaction: 0,
          afterDetaction: 0,
          driverPercentage: 0,
          ownerPercentage: 0,
          notes: [],
        },
      );

      setTotals(newTotals);
      setReportDataFromContext(filteredReports);
      if (onTotalsCalculated) {
        onTotalsCalculated(newTotals);
      }
    } else {
      const newTotals = {
        total: 0,
        cash: 0,
        prePaid: 0,
        card: 0,
        FTJ: 0,
        SKOL: 0,
        tip: 0,
        kastrupAirport: 0,
        stationCH: 0,
        sturupAirport: 0,
        extraBill: 0,
        beforeDetaction: 0,
        afterDetaction: 0,
        driverPercentage: 0,
        ownerPercentage: 0,
        notes: [],
      };

      setTotals(newTotals);
      setReportDataFromContext(filteredReports);
      if (onTotalsCalculated) {
        onTotalsCalculated(newTotals);
      }
    }
  }, [filteredReports, onTotalsCalculated, currentMonth]);

  const formatDate = dateString => {
    return dateString.split('/')[0];
  };

  const getDayOfWeek = dateString => {
    const [day, month, year] = dateString.split('/').map(Number);
    const date = new Date(year, month - 1, day);
    const dayIndex = date.getDay();
    const weekDays = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];
    return weekDays[dayIndex];
  };

  const incrementMonth = () => {
    setIconPressed('increment');
    setCurrentMonth(
      prevMonth =>
        new Date(prevMonth.getFullYear(), prevMonth.getMonth() + 1, 1),
    );
    setTotals({
      total: 0,
      cash: 0,
      prePaid: 0,
      card: 0,
      FTJ: 0,
      SKOL: 0,
      tip: 0,
      kastrupAirport: 0,
      stationCH: 0,
      sturupAirport: 0,
      extraBill: 0,
      beforeDetaction: 0,
      afterDetaction: 0,
      driverPercentage: 0,
      ownerPercentage: 0,
    });
    setTimeout(() => setIconPressed(null), 150);
  };

  const decrementMonth = () => {
    setIconPressed('decrement');
    setCurrentMonth(
      prevMonth =>
        new Date(prevMonth.getFullYear(), prevMonth.getMonth() - 1, 1),
    );
    setTotals({
      total: 0,
      cash: 0,
      prePaid: 0,
      card: 0,
      FTJ: 0,
      SKOL: 0,
      tip: 0,
      kastrupAirport: 0,
      stationCH: 0,
      sturupAirport: 0,
      extraBill: 0,
      beforeDetaction: 0,
      afterDetaction: 0,
      driverPercentage: 0,
      ownerPercentage: 0,
    });
    setTimeout(() => setIconPressed(null), 150);
  };
  const handleModalUpdate = () => {
    fetchReports();
  };
  const handleEditClick = _id => {
    setReportId(_id);
    // console.log(reportId);
    setModalVisible(true);
  };
  const translatedMonth = t(
    `months.${
      [
        'jan',
        'feb',
        'mar',
        'apr',
        'may',
        'jun',
        'jul',
        'aug',
        'sep',
        'oct',
        'nov',
        'dec',
      ][currentMonth.getMonth()]
    }`,
  );
  return (
    // <GestureDetector gesture={gestures}>
    //   <Animated.View style={[styles.container, animatedStyle]}>
    <ScrollView>
      <View style={styles.table}>
        <View style={styles.monthContainer}>
          <TouchableOpacity onPress={decrementMonth}>
            <FontAwesomeIcon icon={faCaretLeft} color={'#42b6f5'} />
          </TouchableOpacity>
          {/* <Text style={styles.month}>
            {currentMonth.toLocaleString('default', {
              month: 'long',
              year: 'numeric',
            })}
          </Text> */}
          <Text style={styles.month}>
            {translatedMonth
              .toLocaleString('default', {month: 'long'})
              .toLowerCase()}{' '}
            {currentMonth.getFullYear()}
          </Text>
          <TouchableOpacity onPress={incrementMonth}>
            <FontAwesomeIcon icon={faCaretRight} color={'#42b6f5'} />
          </TouchableOpacity>
        </View>
        <View style={styles.header}>
          <Text style={styles.datecell1}>{t('Date')}</Text>
          <Text style={styles.cell1h}>{t('day')}</Text>
          {reportSettings.total?.isActive && (
            <Text style={styles.cellh}>{t('Total')}</Text>
          )}
          {reportSettings.cash?.isActive && (
            <Text style={styles.cellh}>{t('Cash')}</Text>
          )}
          {reportSettings.prePaid?.isActive && (
            <Text style={styles.cellh}>{t('PrePaid')}</Text>
          )}
          {reportSettings.card?.isActive && (
            <Text style={styles.cellh}>{t('Card')}</Text>
          )}
          {reportSettings.FTJ?.isActive && (
            <Text style={styles.cellh}>FTJ</Text>
          )}
          {reportSettings.SKOL?.isActive && (
            <Text style={styles.cellh}>SKOL</Text>
          )}
          {reportSettings.tip?.isActive && (
            <Text style={styles.cellh}>{t('Tip')}</Text>
          )}
          {reportSettings.kastrupAirport?.isActive && (
            <Text style={[styles.cell1h, styles.blueCellTop]}>K</Text>
          )}
          {reportSettings.stationCH?.isActive && (
            <Text style={[styles.cell1h, styles.blueCellTop]}>C</Text>
          )}
          {reportSettings.sturupAirport?.isActive && (
            <Text style={[styles.cell1h, styles.blueCellTop]}>S</Text>
          )}
          {reportSettings.extraBill?.isActive && (
            <Text style={[styles.cell1h, styles.blueCellTop]}>E-B</Text>
          )}
          <Text style={styles.cellh}>{t('bdet')}</Text>
          <Text style={styles.cellh}>{t('Adet')}</Text>
          {reportSettings.driverPercentage?.isActive && (
            <Text style={styles.cellh}>{t('Dper')}</Text>
          )}
          {reportSettings.ownerPercentage?.isActive && (
            <Text style={styles.cellh}>O %</Text>
          )}
        </View>
        {loading ? (
          // Show ActivityIndicator when loading
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#42b6f5" />
            <Text>Loading reports...</Text>
          </View>
        ) : filteredReports.length > 0 ? (
          filteredReports.map((row, index) => (
            <View key={index} style={styles.row}>
              <TouchableOpacity
                style={[styles.datecell, {flexDirection: 'row'}]}
                onPress={() => handleEditClick(row._id)}>
                <Text style={styles.date}>{formatDate(row.date)}</Text>
                <FontAwesomeIcon
                  icon={faEdit}
                  size={8}
                  color="red"
                  style={styles.icon}
                />
              </TouchableOpacity>
              <Text style={styles.cell1}>{getDayOfWeek(row.date)}</Text>
              {reportSettings.total?.isActive && (
                <Text style={styles.cell}>{row.total}</Text>
              )}
              {reportSettings.cash?.isActive && (
                <Text style={styles.cell}>{row.cash}</Text>
              )}
              {reportSettings.prePaid?.isActive && (
                <Text style={styles.cell}>{row.prePaid}</Text>
              )}
              {reportSettings.card?.isActive && (
                <Text style={styles.cell}>{row.card}</Text>
              )}
              {reportSettings.FTJ?.isActive && (
                <Text style={styles.cell}>{row.FTJ}</Text>
              )}
              {reportSettings.SKOL?.isActive && (
                <Text style={styles.cell}>{row.SKOL}</Text>
              )}
              {reportSettings.tip?.isActive && (
                <Text style={[styles.cell, styles.tipCell]}>{row.tip}</Text>
              )}
              {reportSettings.kastrupAirport?.isActive && (
                <Text style={[styles.cell1, styles.blueCell]}>
                  {row.kastrupAirport}
                </Text>
              )}
              {reportSettings.stationCH?.isActive && (
                <Text style={[styles.cell1, styles.blueCell]}>
                  {row.stationCH}
                </Text>
              )}
              {reportSettings.sturupAirport?.isActive && (
                <Text style={[styles.cell1, styles.blueCell]}>
                  {row.sturupAirport}
                </Text>
              )}
              {reportSettings.extraBill?.isActive && (
                <Text style={[styles.cell, styles.blueCell]}>
                  {row.extraBill}
                </Text>
              )}
              <Text style={styles.cell}>{row.beforeDetaction}</Text>
              <Text style={styles.cell}>{row.afterDetaction}</Text>
              {reportSettings.driverPercentage?.isActive && (
                <Text style={styles.cell}>
                  {Number(row.driverPercentage).toFixed(0)}
                </Text>
              )}
              {reportSettings.ownerPercentage?.isActive && (
                <Text style={styles.cell}>
                  {Number(row.ownerPercentage).toFixed(0)}
                </Text>
              )}
            </View>
          ))
        ) : (
          <View style={styles.noReportsMessage}>
            <Text style={styles.row}>No reports available for this month.</Text>
          </View>
        )}
        <View style={styles.totalsRow}>
          <Text style={styles.total}>Totals</Text>
          {reportSettings.total?.isActive && (
            <Text style={[styles.cell, styles.totals]}>{totals.total}</Text>
          )}
          {reportSettings.cash?.isActive && (
            <Text style={[styles.cell, styles.totals]}>{totals.cash}</Text>
          )}
          {reportSettings.prePaid?.isActive && (
            <Text style={[styles.cell, styles.totals]}>{totals.prePaid}</Text>
          )}
          {reportSettings.card?.isActive && (
            <Text style={[styles.cell, styles.totals]}>{totals.card}</Text>
          )}
          {reportSettings.FTJ?.isActive && (
            <Text style={[styles.cell, styles.totals]}>{totals.FTJ}</Text>
          )}
          {reportSettings.SKOL?.isActive && (
            <Text style={[styles.cell, styles.totals]}>{totals.SKOL}</Text>
          )}
          {reportSettings.tip?.isActive && (
            <Text style={[styles.cell, styles.totals, styles.tipCell]}>
              {totals.tip}
            </Text>
          )}
          {reportSettings.kastrupAirport?.isActive && (
            <Text style={[styles.cell1, styles.totals, styles.blueCell]}>
              {totals.kastrupAirport}
            </Text>
          )}
          {reportSettings.stationCH?.isActive && (
            <Text style={[styles.cell1, styles.totals, styles.blueCell]}>
              {totals.stationCH}
            </Text>
          )}
          {reportSettings.sturupAirport?.isActive && (
            <Text style={[styles.cell1, styles.totals, styles.blueCell]}>
              {totals.sturupAirport}
            </Text>
          )}
          {reportSettings.extraBill?.isActive && (
            <Text style={[styles.cell, styles.totals, styles.blueCell]}>
              {totals.extraBill}
            </Text>
          )}
          <Text style={[styles.cell, styles.totals]}>
            {totals.beforeDetaction}
          </Text>
          <Text style={[styles.cell, styles.totals]}>
            {totals.afterDetaction}
          </Text>
          {reportSettings.driverPercentage?.isActive && (
            <Text style={[styles.cell, styles.totals]}>
              {Number(totals.driverPercentage).toFixed(0)}
            </Text>
          )}
          {reportSettings.ownerPercentage?.isActive && (
            <Text style={[styles.cell, styles.totals]}>
              {Number(totals.ownerPercentage).toFixed(0)}
            </Text>
          )}
        </View>
      </View>
      {reportId && (
        <ReportModal
          visible={modalVisible}
          onClose={() => setModalVisible(false)}
          reportId={reportId}
          onUpdate={handleModalUpdate}
        />
      )}
    </ScrollView>
    //   </Animated.View>
    // </GestureDetector>
  );
};

const styles = StyleSheet.create({
  table: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 5,
    width: '100%',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
  },
  monthContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
  },
  month: {
    color: 'black',
    marginHorizontal: 15,
    fontSize: width < 350 ? 12 : 14,
  },
  swipeIcons: {
    height: 100,
    backgroundColor: 'red',
  },
  header: {
    flexDirection: 'row',
    backgroundColor: '#F1C50D',
    width: '100%',
    justifyContent: 'space-between',
    // justifyContent: 'center',
    alignItems: 'center',
    // height: height * 0.05,
  },
  row: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
    width: '100%',
    justifyContent: 'space-between',
  },
  totalsRow: {
    flexDirection: 'row',
    backgroundColor: '#31A8F7',
    fontWeight: 'bold',
    width: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  totals: {
    fontWeight: 'bold',
    borderColor: '#31A8F7',
  },
  cell: {
    borderWidth: 0.59,
    fontSize: width < 350 ? 6 : 7,
    borderColor: '#ccc',
    flex: 2,
    minWidth: 24,
    textAlign: 'center',
    color: 'black',
  },
  date: {
    fontSize: width < 350 ? 7 : 8,
    borderColor: '#ccc',
    textAlign: 'center',
    color: 'black',
  },
  icon: {
    alignSelf: 'flex-end',
    marginLeft: 2.5,
  },
  cell1: {
    borderWidth: 0.59,
    fontSize: width < 350 ? 7 : 8,
    borderColor: '#ccc',
    flex: 1,
    minWidth: 19,
    textAlign: 'center',
    color: 'black',
  },
  tipCell: {
    backgroundColor: '#74cfff',
    minWidth: 14,
  },
  datecell: {
    borderWidth: 0.59,
    fontSize: width < 350 ? 7 : 8,
    borderColor: '#ccc',
    flex: 1,
    minWidth: 21,
    textAlign: 'center',
    justifyContent: 'center',
    color: 'black',
  },
  total: {
    borderWidth: 0.59,
    fontSize: width < 350 ? 7 : 8,
    borderColor: '#ccc',
    flex: 2,
    minWidth: 40,
    textAlign: 'center',
    fontWeight: 'bold',
    color: 'black',
    paddingVertical: 3,
  },
  blueCell: {
    backgroundColor: '#C9C9CD',
    fontSize: width < 350 ? 6 : 7,
    minWidth: 14,
    flex: 1,
  },
  blueCellTop: {
    backgroundColor: '#F1C50D',
    fontSize: width < 350 ? 6 : 7,
    minWidth: 12,
    flex: 1,
  },
  noReportsMessage: {
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
  },
  errorMessage: {
    color: 'red',
    textAlign: 'center',
    marginTop: 10,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    width: '100%',
    height: height,
  },

  /// header
  datecell1: {
    //borderWidth: 0.59,
    fontSize: width < 350 ? 7 : 8,
    borderColor: '#ccc',
    flex: 1,
    minWidth: 22,
    textAlign: 'center',
    justifyContent: 'center',
    color: 'black',
    // transform: [{rotate: '-90deg'}],
  },
  cellh: {
    //borderWidth: 0.59,
    fontSize: width < 350 ? 6 : 7,
    borderColor: '#ccc',
    flex: 2,
    minWidth: 24,
    textAlign: 'center',
    color: 'black',
    // transform: [{rotate: '-90deg'}],
  },
  cell1h: {
    fontSize: width < 350 ? 7 : 8,
    borderColor: '#ccc',
    flex: 1,
    minWidth: 19,
    textAlign: 'center',
    color: 'black',
    // transform: [{rotate: '-90deg'}],
  },
});

export default CustomTable;
