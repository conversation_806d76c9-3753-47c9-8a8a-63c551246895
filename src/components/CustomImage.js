import {Image, StyleSheet, Text, View,Dimensions} from 'react-native';
import React from 'react';
import {textcolor} from '../styles/style';

const {width,height} = Dimensions.get('window');
const CustomImage = ({header}) => {
  return (
    <View>
      <Image
        source={require('../assets/Image/logo.png')}
        style={styles.image}
      />
      <Text style={styles.text}>{header}</Text>
    </View>
  );
};

export default CustomImage;

const styles = StyleSheet.create({
  image: {
    width: '85%',
    height: height * 0.095,
    resizeMode: 'contain',
    alignSelf: 'center',
   
  },
  text: {
    fontSize: height *0.03,
    fontWeight: '700',
    color: textcolor.color2,
    alignSelf: 'center',
    marginTop: height * 0.03,
  },
});
