import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';
import {buttoncolor, textcolor} from '../styles/style';

const {width, height} = Dimensions.get('window');
const CustomTextinput = ({
  label,
  placeholder,
  secureTextEntry = false,
  keyboardType,
  onChangeText, // Add this prop to pass from parent
  value, // Add this to control the value in parent
}) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  return (
    <TouchableOpacity style={styles.inputContainer}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.inputWrapper}>
        <TextInput
          style={styles.input}
          placeholder={placeholder}
          placeholderTextColor="white"
          secureTextEntry={!isPasswordVisible && secureTextEntry}
          keyboardType={keyboardType}
          onChangeText={onChangeText} // Pass this down to the TextInput
          value={value} // Pass the value down to TextInput
        />
        {secureTextEntry && (
          <TouchableOpacity
            onPress={togglePasswordVisibility}
            style={styles.iconContainer}>
            <Icon
              name={isPasswordVisible ? 'eyeo' : 'eye'}
              size={22}
              style={{color: textcolor.color1}}
            />
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    alignSelf: 'center',
    marginTop: height * 0.01,
  },
  label: {
    fontSize: width * 0.033,
    fontWeight: '600',
    marginBottom: width * 0.01,

    marginHorizontal: 10,
    color: textcolor.color1,
    fontFamily: 'Roboto-Medium',
  },
  inputWrapper: {
    width: '90%',

    height: height * 0.053,

    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 10,
    backgroundColor: buttoncolor.bgcolor,
    justifyContent: 'center',
  },
  input: {
    flex: 1,
    paddingHorizontal: 15,
    fontSize: 12,
    fontWeight: '600',
    fontFamily: 'Roboto-Medium',
    color: textcolor.color4,
  },
  iconContainer: {
    paddingHorizontal: width * 0.04,
  },
});

export default CustomTextinput;
