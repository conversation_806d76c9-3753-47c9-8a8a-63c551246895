import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  PixelRatio,
  Platform,
  Modal,
  KeyboardAvoidingView,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import DateTimePicker from '@react-native-community/datetimepicker';
import {buttoncolor, textcolor} from '../styles/style';

const {width, height} = Dimensions.get('window');
const scale = width / 320;
const normalizeFontSize = size => {
  const newSize = size * scale;
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
};

const Custominput = ({
  title,
  title1,
  isDateInput = false,
  keyboardType,
  value,
  onChange,
  Input,
  textAlignVertical,
  multiline,
  onFocus,
  onBlur,
  placeholder,
  editable = true,
  bordered = false,
  bgColor = false,
}) => {
  const [selectedDate, setSelectedDate] = useState(
    value ? new Date(value) : new Date(), // Ensure it’s always a Date
  );
  const [showPicker, setShowPicker] = useState(false);

  useEffect(() => {
    if (!value) {
      setSelectedDate(null);
    } else {
      setSelectedDate(new Date(value));
    }
  }, [value]);

  const handleDateChange = (event, date) => {
    if (Platform.OS === 'android') {
      setShowPicker(false);
    }
    if (date) {
      setSelectedDate(date);
      onChange && onChange(date);
    }
  };

  // useEffect(() => {
  //   console.log('showPicker state: ', showPicker);
  // }, [showPicker]);

  const formatDateToDDMMYYYY = date => {
    if (!date) return '';
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  return (
    <View style={styles.container}>
      {title && <Text style={styles.title}>{title} :</Text>}
      {title1 && <Text style={styles.title1}>{title1} :</Text>}
      {isDateInput ? (
        <>
          <KeyboardAvoidingView behavior="padding">
            <View style={{position: 'relative', zIndex: 1}}>
              <TouchableOpacity
                onPress={() => {
                  // console.log('Opening Date picker');
                  if (!selectedDate) {
                    setSelectedDate(new Date()); // Ensure today's date is set
                  }
                  setShowPicker(true);
                }}
                style={styles.inputWrapper}
                activeOpacity={0.7}
                onStartShouldSetResponder={() => true} // Ensures it captures touch events
              >
                <TextInput
                  style={styles.Dateinput}
                  value={selectedDate ? formatDateToDDMMYYYY(selectedDate) : ''}
                  editable={false}
                  pointerEvents="none"
                  placeholderTextColor="black"
                  placeholder="DD/MM/YYYY"
                />
              </TouchableOpacity>
            </View>
          </KeyboardAvoidingView>

          {Platform.OS === 'android' && showPicker && (
            <DateTimePicker
              value={selectedDate || new Date()}
              mode="date"
              display="default"
              onChange={handleDateChange}
            />
          )}

          {Platform.OS === 'ios' && showPicker && (
            <Modal visible={showPicker} transparent animationType="slide">
              <View style={styles.modalContainer}>
                <View style={styles.modalContent}>
                  <DateTimePicker
                    value={selectedDate || new Date()}
                    mode="date"
                    display="spinner"
                    onChange={handleDateChange}
                    textColor="black"
                  />
                  <TouchableOpacity onPress={() => setShowPicker(false)}>
                    <Text style={styles.doneButton}>Done</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Modal>
          )}
        </>
      ) : (
        <TextInput
          style={[
            Input ? styles.input1 : styles.input,
            bordered && styles.bordered,
            bgColor && styles.bgColor, // Conditionally apply border
          ]}
          value={value}
          onChangeText={onChange}
          keyboardType={keyboardType ? keyboardType : 'numeric'}
          multiline={multiline}
          placeholder={placeholder}
          textAlignVertical={textAlignVertical}
          onBlur={onBlur}
          onFocus={onFocus}
          editable={editable}
        />
      )}
    </View>
  );
};

export default Custominput;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
    marginHorizontal: 8,
  },
  title: {
    fontSize: normalizeFontSize(8.4),
    fontWeight: '700',
    color: textcolor.color1,
  },
  title1: {
    fontSize: normalizeFontSize(12),
    fontWeight: '600',
    color: textcolor.color1,
  },
  input: {
    width: width * 0.129,
    height: height * 0.027,
    backgroundColor: buttoncolor.color,
    padding: 2,
    fontSize: 11,
    fontWeight: 'bold',
    textAlign: 'center',
    color: 'black',
    borderRadius: 5,
    // borderWidth: 1,
    // borderColor: '#000',
    marginLeft: 5,
  },
  input1: {
    width: '40%',
    maxWidth: '40%',
    height: 60,
    backgroundColor: buttoncolor.color,
    padding: 8,
    fontSize: 11,
    color: 'black',
    flexWrap: 'wrap',
    // borderWidth: 1,
    // borderColor: '#000',
    borderRadius: 5,
  },
  Dateinput: {
    width: width * 0.2,
    textAlign: 'center',
    height: 28,
    fontSize: 10,
    padding: 4,
    color: 'black',
    backgroundColor: buttoncolor.color,
    // borderWidth: 1,
    // borderColor: '#000',
    borderRadius: 5,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
  },
  doneButton: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    color: textcolor.color1,
    marginTop: 10,
  },
  bordered: {
    borderWidth: 1,
    borderColor: 'black',
  },
  bgColor: {
    backgroundColor: '#FCCC31',
  },
});
