import React, {useState, useEffect, useCallback} from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Custominput from './Custominput';
import {useTranslation} from 'react-i18next';
import CustomButton from './CustomButton';
import {config} from '../../config';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ReportModal = ({visible, onClose, reportId, onUpdate}) => {
  const {t} = useTranslation();

  // Individual states for each input field
  const [extraBill, setExtraBill] = useState('');
  const [ftj, setFtj] = useState('');
  const [skol, setSkol] = useState('');
  const [afterDetaction, setAfterDetaction] = useState('');
  const [beforeDetaction, setBeforeDetaction] = useState('');
  const [card, setCard] = useState('');
  const [cash, setCash] = useState('');
  const [date, setDate] = useState('');
  const [kastrupAirport, setKastrupAirport] = useState('');
  const [prePaid, setPrePaid] = useState('');
  const [stationCH, setStationCH] = useState('');
  const [sturupAirport, setSturupAirport] = useState('');
  const [tip, setTip] = useState('');
  const [total, setTotal] = useState('');
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const input1 = true;

  const [userId, setUserId] = useState(null);
  const [isFocused, setIsFocused] = useState(false);

  const [reportSettings, setReportSettings] = useState({
    total: {isActive: false},
    cash: {isActive: false},
    card: {isActive: false},
    prePaid: {isActive: false},
    FTJ: {isActive: false},
    SKOL: {isActive: false},
    tip: {isActive: false},
    kastrupAirport: {isActive: false, value: 1},
    stationCH: {isActive: false, value: 1},
    sturupAirport: {isActive: false, value: 1},
    extraBill: {isActive: false},
  });
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const user = await AsyncStorage.getItem('User');
        const parsedUser = user ? JSON.parse(user) : null;
        setUserId(parsedUser?._id || null);
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };
    fetchUserData();
  }, []);

  const fetchReportSettings = async () => {
    if (!userId) {
      console.log('No user ID available for API request.');
      return;
    }
    try {
      const response = await fetch(
        `${config.baseUrl}/auth/report-setting/${userId}`,
      );
      const result = await response.json();

      setReportSettings({
        total: {isActive: result.total?.isActive || false},
        cash: {isActive: result.cash?.isActive || false},
        card: {isActive: result.card?.isActive || false},
        prePaid: {isActive: result.prePaid?.isActive || false},
        FTJ: {isActive: result.FTJ?.isActive || false},
        SKOL: {isActive: result.SKOL?.isActive || false},
        tip: {isActive: result.tip?.isActive || false},
        kastrupAirport: {
          isActive: result.kastrupAirport?.isActive || false,
          value: result.kastrupAirport?.value || 1,
        },
        stationCH: {
          isActive: result.stationCH?.isActive || false,
          value: result.stationCH?.value || 1,
        },
        sturupAirport: {
          isActive: result.sturupAirport?.isActive || false,
          value: result.sturupAirport?.value || 1,
        },
        extraBill: {isActive: result.extraBill?.isActive || false},
      });
    } catch (error) {
      console.error('Error fetching report settings:', error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (userId) {
        fetchReportSettings();
      }
    }, [userId]),
  );
  useEffect(() => {
    const fetchReportData = async () => {
      if (!reportId) {
        console.warn('No reportId provided');
        return;
      }
      // console.log('Report Id in update modal : ', reportId); 
      try {
        setLoading(true);
        // console.log(reportId);
        const url = `${config.baseUrl}/reports/${reportId}/single`;
        // console.log(url);
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const {report: data} = await response.json();

        // Set notes with the date and note text
        setNotes(data.note);

        // Parse and reformat the date (DD/MM/YYYY format)
        const apiDate = data.date; // Expected to be DD/MM/YYYY
        const [day, month, year] = apiDate.split('/');
        const jsDate = new Date(`${year}-${month}-${day}`); // Create a valid Date object
        setDate(jsDate); // Pass the Date object to CustomInput

        // Populate other fields
        setExtraBill(data.extraBill?.value || '0');
        setFtj(data.FTJ?.value || '0');
        setSkol(data.SKOL?.value || '0');
        setAfterDetaction(data.afterDetaction || '0');
        setBeforeDetaction(data.beforeDetaction || '0');
        setCard(data.card?.value || '0');
        setCash(data.cash?.value || '0');
        setKastrupAirport(data.kastrupAirport?.value || '0');
        setPrePaid(data.prePaid?.value || '0');
        setStationCH(data.stationCH?.value || '0');
        setSturupAirport(data.sturupAirport?.value || '0');
        setTip(data.tip?.value || '0');
        setTotal(data.total?.value || '0');
      } catch (error) {
        console.error('Error fetching report data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchReportData();
  }, [reportId]);

  const handleUpdate = async () => {
    const updatedReport = {
      total: {value: parseFloat(total) || 0},
      cash: {value: parseFloat(cash) || 0},
      card: {value: parseFloat(card) || 0},
      prePaid: {value: parseFloat(prePaid) || 0},
      FTJ: {value: parseFloat(ftj) || 0},
      SKOL: {value: parseFloat(skol) || 0},
      tip: {value: parseFloat(tip) || 0},
      kastrupAirport: {value: parseFloat(kastrupAirport) || 0},
      sturupAirport: {value: parseFloat(sturupAirport) || 0},
      stationCH: {value: parseFloat(stationCH) || 0},
      extraBill: {value: parseFloat(extraBill) || 0},
      note: notes,
      beforeDetaction: beforeDetaction,
      afterDetaction: afterDetaction,
    };

    setLoading(true);

    try {
      const response = await fetch(`${config.baseUrl}/reports/${reportId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedReport),
      });
      const result = await response.json();
      if (!response.ok) {
        Alert.alert(
          t('Error'),
          result.message || t('Failed to update report.'),
        );
      } else {
        Alert.alert(t('Success'), t('Report updated successfully'));
        // onUpdate(); // callback to update the parent component if necessary
        onUpdate();
        onClose(); // Close modal after success
      }
    } catch (error) {
      Alert.alert(t('Error'), t('Failed to update report.'));
    } finally {
      setLoading(false);
    }
  };
  const handleDelete = async () => {
    try {
      // Show a confirmation alert
      Alert.alert(
        'Confirm Delete',
        'Are you sure you want to delete this report?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Delete',
            onPress: async () => {
              // Call the DELETE API
              const response = await fetch(
                `${config.baseUrl}/reports/${reportId}`,
                {
                  method: 'DELETE', // Specify HTTP DELETE method
                  headers: {
                    'Content-Type': 'application/json', // Optional, based on your API requirements
                  },
                },
              );

              // Check the response status
              if (!response.ok) {
                throw new Error(
                  `Failed to delete report. Status: ${response.status}`,
                );
              }

              // Parse the response (if any)
              const result = await response.json();

              // Show success alert
              Alert.alert('Success', 'Report deleted successfully!');
              // Trigger parent update
              if (onUpdate) {
                onUpdate(); // Refresh the reports in the parent component
              }
              onClose();
            },
          },
        ],
      );
    } catch (error) {
      console.error('Error deleting report:', error);
      // Show error alert
      Alert.alert('Error', `Failed to delete the report: ${error.message}`);
    }
  };

  const handleFocus = setInputValue => {
    return () => {
      setInputValue(prevValue => (prevValue === '0' ? '' : prevValue));
    };
  };

  const handleBlur = setInputValue => {
    return () => {
      setInputValue(prevValue => (prevValue === '' ? '0' : prevValue));
    };
  };
  return (
    <Modal
      visible={visible}
      onRequestClose={onClose}
      animationType="slide"
      transparent={true}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <TouchableOpacity style={styles.topIcon} onPress={onClose}>
            <AntDesign name="close" size={20} color="#ffffff" />
          </TouchableOpacity>
          <Text style={styles.modalTitle}>Edit Report</Text>
          {loading ? (
            <ActivityIndicator size="large" color="#0000ff" />
          ) : (
            <ScrollView showsVerticalScrollIndicator={false}>
              <Custominput
                title1={t('Date')}
                isDateInput={true}
                value={date}
                onChange={setDate}
              />
              {reportSettings.total?.isActive && (
                <Custominput
                  title1={t('Total')}
                  value={total.toString()}
                  onChange={setTotal}
                  onFocus={handleFocus(setTotal)}
                  onBlur={handleBlur(setTotal)}
                />
              )}
              {reportSettings.cash?.isActive && (
                <Custominput
                  title1={t('Cash')}
                  value={cash.toString()}
                  onChange={setCash}
                  onFocus={handleFocus(setCash)}
                  onBlur={handleBlur(setCash)}
                />
              )}
              {reportSettings.card?.isActive && (
                <Custominput
                  title1={t('Card')}
                  value={card.toString()}
                  onChange={setCard}
                  onFocus={handleFocus(setCard)}
                  onBlur={handleBlur(setCard)}
                />
              )}
              {reportSettings.prePaid?.isActive && (
                <Custominput
                  title1={t('PrePaid')}
                  value={prePaid.toString()}
                  onChange={setPrePaid}
                  onFocus={handleFocus(setPrePaid)}
                  onBlur={handleBlur(setPrePaid)}
                />
              )}
              {reportSettings.FTJ?.isActive && (
                <Custominput
                  title1={t('FTJ')}
                  value={ftj.toString()}
                  onChange={setFtj}
                  onFocus={handleFocus(setFtj)}
                  onBlur={handleBlur(setFtj)}
                />
              )}
              {reportSettings.SKOL?.isActive && (
                <Custominput
                  title1={t('SKOL')}
                  value={skol.toString()}
                  onChange={setSkol}
                  onFocus={handleFocus(setSkol)}
                  onBlur={handleBlur(setSkol)}
                />
              )}
              {reportSettings.tip?.isActive && (
                <Custominput
                  title1={t('Tip')}
                  value={tip.toString()}
                  onChange={setTip}
                  onFocus={handleFocus(setTip)}
                  onBlur={handleBlur(setTip)}
                />
              )}
              {reportSettings.kastrupAirport?.isActive && (
                <Custominput
                  title1={t('Kastrup')}
                  value={kastrupAirport.toString()}
                  onChange={setKastrupAirport}
                  onFocus={handleFocus(setKastrupAirport)}
                  onBlur={handleBlur(setKastrupAirport)}
                />
              )}
              {reportSettings.stationCH?.isActive && (
                <Custominput
                  title1={t('Station')}
                  value={stationCH.toString()}
                  onChange={setStationCH}
                  onFocus={handleFocus(setStationCH)}
                  onBlur={handleBlur(setStationCH)}
                />
              )}
              {reportSettings.sturupAirport?.isActive && (
                <Custominput
                  title1={t('Sturup')}
                  value={sturupAirport.toString()}
                  onChange={setSturupAirport}
                  onFocus={handleFocus(setSturupAirport)}
                  onBlur={handleBlur(setSturupAirport)}
                />
              )}
              {reportSettings.extraBill?.isActive && (
                <Custominput
                  title1={t('Extra')}
                  value={extraBill.toString()}
                  onChange={setExtraBill}
                  onFocus={handleFocus(setExtraBill)}
                  onBlur={handleBlur(setExtraBill)}
                />
              )}
              <Custominput
                title1={t('Before')}
                value={beforeDetaction.toString()}
                onChange={setBeforeDetaction}
              />
              <Custominput
                title1={t('After')}
                value={afterDetaction.toString()}
                onChange={setAfterDetaction}
              />
              <Custominput
                title1={t('Note')}
                value={notes}
                onChange={setNotes}
                Input={input1}
                keyboardType="default"
                textAlignVertical="top"
                multiline={true}
              />
            </ScrollView>
          )}
          <View style={styles.modalButtons}>
            <CustomButton title="Update" onPress={handleUpdate} />
            <CustomButton
              title="Delete"
              onPress={handleDelete}
              style={styles.deleteBtn}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    width: '85%',
    maxHeight: '80%',
    padding: 20,
    backgroundColor: '#e6e8e3',
    borderRadius: 10,
    position: 'relative',
  },
  topIcon: {
    backgroundColor: '#A9A9A9',
    width: 25,
    height: 25,
    borderRadius: 20,
    position: 'absolute',
    top: 10,
    right: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitle: {
    marginTop: 25,
    marginBottom: 15,
    textAlign: 'center',
    color: '#333',
    fontSize: 18,
    fontWeight: '700',
  },
  modalButtons: {
    flexDirection: 'row',
    alignSelf: 'center',
    gap: 15,
    marginTop: 20,
  },
  deleteBtn: {
    backgroundColor: '#DC143C',
  },
});

export default ReportModal;
