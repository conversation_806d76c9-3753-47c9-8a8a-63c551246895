import {View, Text, SafeAreaView, StyleSheet} from 'react-native';
import {React} from 'react';
import {
  CodeField,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field';
import {buttoncolor} from '../styles/style';
import {colors} from 'react-native-elements';
const OTPinput = ({value, setValue}) => {
  const CELL_COUNT = 4;
  // const [value, setValue] = useState('');
  const ref = useBlurOnFulfill({value, cellCount: CELL_COUNT});
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });
  return (
    <SafeAreaView style={styles.root}>
      <CodeField
        ref={ref}
        {...props}
        value={value}
        onChangeText={setValue}
        cellCount={CELL_COUNT}
        rootStyle={styles.codeFieldRoot}
        keyboardType="number-pad"
        textContentType="oneTimeCode"
        autoComplete={Platform.select({
          android: 'sms-otp',
          default: 'one-time-code',
        })}
        testID="my-code-input"
        renderCell={({index, symbol, isFocused}) => (
          <Text
            key={index}
            style={[styles.cell, isFocused && styles.focusCell]}
            onLayout={getCellOnLayoutHandler(index)}>
            {symbol || (isFocused ? <Cursor /> : null)}
          </Text>
        )}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  root: {
    alignSelf: 'center',
  },
  codeFieldRoot: {
    marginTop: 10,
  },
  cell: {
    width: 50,
    height: 50,
    fontSize: 25,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
    justifyContent: 'space-between',
    padding: 8,
    backgroundColor: buttoncolor.bgcolor,
    borderRadius: 50,
    marginLeft: 15,
    fontFamily: 'Roboto-Bold',
  },
});
export default OTPinput;
