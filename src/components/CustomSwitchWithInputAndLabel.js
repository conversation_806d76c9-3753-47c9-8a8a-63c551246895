import React, {useState, useEffect} from 'react';
import {View, Text, Switch, TextInput, StyleSheet} from 'react-native';
import {buttoncolor, textcolor} from '../styles/style';

const CustomSwitchWithInputAndLabel = ({
  label,
  onValueChange,
  keyboardType,
  value,
  inputValue,
  isEnabledDefault = false, // Default value for isEnabled
}) => {
  const [isEnabled, setIsEnabled] = useState(isEnabledDefault);
  const [currentInputValue, setCurrentInputValue] = useState(inputValue || '0');
  const [isFocused, setIsFocused] = useState(false);
  // Update state when value prop changes
  useEffect(() => {
    setIsEnabled(value);
  }, [value]);

  // Update input value when prop changes
  useEffect(() => {
    if (isEnabled) {
      setCurrentInputValue(inputValue); // Only set if enabled
    } else {
      setCurrentInputValue('0'); // Reset to '0' if disabled
    }
  }, [inputValue, isEnabled]);

  const toggleSwitch = () => {
    const newValue = !isEnabled;
    setIsEnabled(newValue);
    if (!newValue) {
      setCurrentInputValue('0'); // Reset input when toggled off
    }
    onValueChange && onValueChange(newValue, currentInputValue); // Pass updated value
  };

  const handleInputChange = text => {
    setCurrentInputValue(text);
    onValueChange && onValueChange(isEnabled, text); // Pass updated inputValue
  };

  const handleFocus = () => {
    if (currentInputValue === '0') {
      setCurrentInputValue(''); // Clear value when focused if it is '0'
    }
    setIsFocused(true);
  };

  const handleBlur = () => {
    if (currentInputValue === '') {
      setCurrentInputValue('0'); // Reset to '0' when input is empty after blur
    }
    setIsFocused(false);
  };
  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}:</Text>
      <View style={styles.switchInputWrapper}>
        <Switch
          trackColor={{false: buttoncolor.color, true: buttoncolor.bgcolor}}
          thumbColor={isEnabled ? buttoncolor.bgcolor : 'grey'}
          onValueChange={toggleSwitch}
          value={isEnabled}
        />
        {isEnabled && (
          <TextInput
            style={styles.input}
            value={currentInputValue} // Use the state variable
            keyboardType={keyboardType}
            onChangeText={handleInputChange}
            placeholder="Enter value"
            onFocus={handleFocus}
            onBlur={handleBlur}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 15,
  },
  label: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: '1%',
    color: textcolor.color1,
    fontFamily: 'Roboto-Medium',
    flex: 1,
  },
  switchInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 0.9,
  },
  input: {
    height: 39,
    backgroundColor: buttoncolor.color,
    marginRight: '30%',
    paddingHorizontal: 6,
    borderRadius: 5,
    borderColor: '#000',
    borderWidth: 1,
    width: '40%',
  },
});

export default CustomSwitchWithInputAndLabel;
