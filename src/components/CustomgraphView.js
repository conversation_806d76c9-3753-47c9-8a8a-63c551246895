import React, {useState, useEffect, useCallback, forwardRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {config} from '../../config';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faCaretLeft, faCaretRight} from '@fortawesome/free-solid-svg-icons';
import {useFocusEffect} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import Svg, {G, Path, Text as SvgText} from 'react-native-svg';

const {width, height: screenHeight} = Dimensions.get('window');

// Calculate the size of the graph as a percentage of the screen width
const graphSize = width * 0.85; // 70% of the screen width
const radius = graphSize / 2; // Radius is half of the graph size

const CustomgraphView = forwardRef(
  ({_id, selectedYear, setSelectedYear}, ref) => {
    const {t} = useTranslation();
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);

    const monthColors = [
      '#c98a75',
      '#440180',
      '#0000FF',
      '#4682B4',
      '#000000',
      '#2E8B57',
      '#16646e',
      '#dbba04',
      '#FF8C00',
      '#FF6347',
      '#32CD32',
      '#6A5ACD',
    ];

    useFocusEffect(
      useCallback(() => {
        const fetchReports = async () => {
          if (!_id) return;

          setLoading(true);
          try {
            const response = await fetch(`${config.baseUrl}/reports/${_id}`);
            const reports = await response.json();

            if (Array.isArray(reports)) {
              const aggregatedData = Array.from({length: 12}, (_, month) => {
                const filteredReports = reports.filter(report => {
                  const [day, reportMonth, year] = report.date
                    .split('/')
                    .map(Number);
                  return year === selectedYear && reportMonth === month + 1;
                });

                const totalBeforeDetection = filteredReports.reduce(
                  (sum, report) => sum + report.beforeDetaction,
                  0,
                );

                return {
                  beforeDetection: totalBeforeDetection,
                  month: month + 1,
                };
              });

              const completeData = aggregatedData.map(item => {
                if (item.beforeDetection === 0) {
                  return {...item, beforeDetection: 0, isDataAvailable: false};
                }
                return {...item, isDataAvailable: true};
              });

              setData(completeData);
            }
          } catch (error) {
            console.error('Error fetching report data:', error);
          } finally {
            setLoading(false);
          }
        };

        fetchReports();
      }, [_id, selectedYear]),
    );

    const incrementYear = () => {
      setSelectedYear(prevYear => prevYear + 1);
    };

    const decrementYear = () => {
      setSelectedYear(prevYear => prevYear - 1);
    };

    const totalBeforeDetection = data.reduce(
      (sum, item) => sum + item.beforeDetection,
      0,
    );

    const monthsWithData = data.filter(item => item.isDataAvailable).length;

    const pieData =
      totalBeforeDetection > 0
        ? data.map(item => ({
            value: item.beforeDetection,
            angle: (item.beforeDetection / totalBeforeDetection) * 360,
            label: [
              'Jan',
              'Feb',
              'Mar',
              'Apr',
              'May',
              'Jun',
              'Jul',
              'Aug',
              'Sep',
              'Oct',
              'Nov',
              'Dec',
            ][item.month - 1],
            beforeDetection: item.beforeDetection,
            isDataAvailable: item.isDataAvailable,
            color: monthColors[item.month - 1],
          }))
        : [];

    const isSingleMonthDataAvailable = monthsWithData === 1;

    const createPieChart = () => {
      if (pieData.length === 0) {
        return (
          <SvgText
            x={graphSize / 2}
            y={graphSize / 2}
            fill="gray"
            fontSize={graphSize * 0.05} // Dynamic font size
            fontWeight="bold"
            textAnchor="middle">
            No data available
          </SvgText>
        );
      }

      let startAngle = 0;

      if (isSingleMonthDataAvailable) {
        const singleMonth = pieData.find(item => item.isDataAvailable);
        const monthIndex = singleMonth?.month - 1;
        const singleColor = monthColors[monthIndex] || '#16646e';

        const totalValue = singleMonth?.beforeDetection || 0;
        const totalAngle = (totalValue / totalBeforeDetection) * 360;

        const textAngle = totalAngle / 2;
        const textX =
          radius + (radius / 2) * Math.cos((Math.PI * textAngle) / 180);
        const textY =
          radius + (radius / 2) * Math.sin((Math.PI * textAngle) / 180);

        return (
          <G>
            <Path
              d={`M${radius},${radius} L${radius},0 A${radius},${radius} 1 1,0 ${radius},${
                2 * radius
              } A${radius},${radius} 1 1,0 ${radius},0 Z`}
              fill={singleColor}
            />
            <SvgText
              x={textX}
              y={textY}
              fill="white"
              fontSize={graphSize * 0.04} // Dynamic font size
              fontWeight="bold"
              textAnchor="middle"
              alignmentBaseline="middle">
              {`${singleMonth?.label}: ${totalValue} KR`}
            </SvgText>
          </G>
        );
      }

      return pieData.map((segment, index) => {
        const endAngle = startAngle + segment.angle;
        const largeArcFlag = segment.angle > 180 ? 1 : 0;

        const startX = radius + radius * Math.cos((Math.PI * startAngle) / 180);
        const startY = radius + radius * Math.sin((Math.PI * startAngle) / 180);
        const endX = radius + radius * Math.cos((Math.PI * endAngle) / 180);
        const endY = radius + radius * Math.sin((Math.PI * endAngle) / 180);

        const pathData = [
          `M${radius},${radius}`,
          `L${startX},${startY}`,
          `A${radius},${radius} 0 ${largeArcFlag} 1 ${endX},${endY}`,
          'Z',
        ].join(' ');

        startAngle = endAngle;

        const textAngle = startAngle - segment.angle / 2;
        const textX =
          radius + (radius / 1.4) * Math.cos((Math.PI * textAngle) / 180);
        const textY =
          radius + (radius / 1.4) * Math.sin((Math.PI * textAngle) / 180);

        const valueX =
          radius + (radius / 2) * Math.cos((Math.PI * textAngle) / 180);
        const valueY =
          radius + (radius / 2) * Math.sin((Math.PI * textAngle) / 180);

        const rotateAngle = textAngle + 180;
        const rotateAngle1 = textAngle + 90;

        return (
          <G key={index}>
            <Path d={pathData} fill={segment.color} />
            {segment.isDataAvailable && (
              <SvgText
                x={textX}
                y={textY}
                fill="yellow"
                fontSize={graphSize * 0.04} // Dynamic font size
                fontWeight="bold"
                textAnchor="middle"
                transform={`rotate(${rotateAngle1}, ${textX}, ${textY})`}>
                {segment.label}
              </SvgText>
            )}

            {segment.isDataAvailable && (
              <SvgText
                x={valueX}
                y={valueY}
                fill="white"
                fontSize={graphSize * 0.04} // Dynamic font size
                fontWeight="bold"
                textAnchor="middle"
                transform={`rotate(${rotateAngle}, ${valueX}, ${valueY})`}>
                {`${segment.beforeDetection} KR`}
              </SvgText>
            )}
          </G>
        );
      });
    };

    return (
      <View style={styles.container} ref={ref}>
        <View style={styles.yearContainer}>
          <TouchableOpacity
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
            onPress={decrementYear}>
            <FontAwesomeIcon icon={faCaretLeft} size={20} color={'#42b6f5'} />
          </TouchableOpacity>
          <Text style={styles.year}>{selectedYear}</Text>
          <TouchableOpacity
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
            onPress={incrementYear}>
            <FontAwesomeIcon icon={faCaretRight} size={20} color={'#42b6f5'} />
          </TouchableOpacity>
        </View>

        {loading ? (
          <View style={styles.noDataContainer}>
            <ActivityIndicator size="large" color="#42b6f5" />
          </View>
        ) : data.length === 0 ? (
          <View style={styles.noDataContainer}>
            <Text style={styles.noDataText}>
              No data available for this year
            </Text>
          </View>
        ) : (
          <View style={styles.graphContainer}>
            <Svg height={graphSize} width={graphSize}>
              {createPieChart()}
            </Svg>
          </View>
        )}
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // paddingTop: 10,
    // borderColor: '#42b6f5',
    // borderWidth: 1,
    backgroundColor: 'white',
  },
  yearContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    // borderColor: '#42b6f5',
    // borderWidth: 1,
    alignItems: 'center',
    // marginBottom: 5,
  },
  year: {
    color: 'black',
    marginHorizontal: 15,
    fontSize: 18,
  },
  graphContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 15,
    // borderColor: 'red',
    // borderWidth: 1,
  },
  noDataContainer: {
    // flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: screenHeight * 0.3,
  },
  noDataText: {
    fontSize: 18,
    color: 'gray',
    textAlign: 'center',
  },
});

export default CustomgraphView;
