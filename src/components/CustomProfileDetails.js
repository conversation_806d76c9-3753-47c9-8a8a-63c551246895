import React, {useEffect, useState} from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const {width, height} = Dimensions.get('window');

const CustomProfileDetails = () => {
  const navigation = useNavigation();
  const [name, setName] = useState(null);
  const [taxiNumber, setTaxiNumber] = useState(null);
  const [companyLogo, setCompanyLogo] = useState(null);
  const [profilePic, setProfilePic] = useState(null); // New state for profile image

  // Refetch user data
  const fetchUserData = async () => {
    const user = await AsyncStorage.getItem('User');
    const parsedUser = user ? JSON.parse(user) : null;
    const userName = parsedUser ? parsedUser.name : null;
    const userTaxiNumber = parsedUser ? parsedUser.taxiNumber : null;
    const userCompanyLogo = parsedUser
      ? parsedUser.taxiCompany.companyLogo
      : null;
    const userProfilePic = parsedUser ? parsedUser.profilePic : null; // Fetch profile picture
    setName(userName);
    setTaxiNumber(userTaxiNumber);
    setCompanyLogo(userCompanyLogo);
    setProfilePic(userProfilePic); // Set the profile picture state
  };

  // Refetch data when component is focused
  useFocusEffect(
    React.useCallback(() => {
      fetchUserData();
    }, []),
  );

  return (
    <View style={styles.container}>
      {/* Profile Button */}
      <TouchableOpacity
        style={styles.profile}
        onPress={() => navigation.navigate('Profilescreen')}>
        <Image
          source={
            profilePic && profilePic.startsWith('http')
              ? {uri: profilePic}
              : require('../assets/default/defaultPerson.jpeg')
          }
          style={styles.profilePic}
        />
      </TouchableOpacity>
      {/* Profile Name */}
      <Text style={styles.text}>{name}</Text>

      {/* Company Logo */}
      <Image
        source={
          companyLogo
            ? {uri: companyLogo}
            : require('../assets/Image/Menu/Upload.png')
        }
        style={styles.companyLogo}
      />

      <Text style={styles.text}>{taxiNumber}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingTop: 15,
  },
  text: {
    textAlign: 'center',
    color: 'black',
    width: width * 0.325,
    fontSize: 12,
  },

  profilePic: {
    width: width * 0.125,
    height: height * 0.059,
    borderRadius: 30,
    //marginTop: 10,
    backgroundColor: 'black',
  },
  companyLogo: {
    width: width * 0.115,
    height: height * 0.035,
    resizeMode: 'contain',
    //marginTop: 10,
  },
});

export default CustomProfileDetails;
