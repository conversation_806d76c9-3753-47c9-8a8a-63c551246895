import {
  StyleSheet,
  Text,
  View,
  PixelRatio,
  Dimensions,
  TextInput,
  ScrollView,
} from 'react-native';
import React from 'react';
import {buttoncolor, textcolor} from '../styles/style';

const {width} = Dimensions.get('window');
const scale = width / 320;
const normalizeFontSize = size => {
  const newSize = size * scale;
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
};
// Helper function to format the date and remove the year
const formatDate = date => {
  const [day, month] = date.split('/'); // Extracting month and day
  return `${day}/${month}`; // Format as day/month
};
const CustomNote = ({title, notes = []}) => {
  // Filter out empty notes and join the notes with their dates into a single string
  const notesText = notes
    .filter(note => note.note && note.note.length > 0) // Exclude empty notes
    .map(note => `${formatDate(note.date)}: ${note.note}`)
    .join('\n\n'); // Separate each note by a new line

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}:</Text>
      <ScrollView style={styles.scrollView} nestedScrollEnabled={true}>
        <TextInput
          style={styles.input}
          multiline={true}
          textAlignVertical="top"
          editable={false} // Make input read-only
          value={notesText} // Display combined notes text
        />
      </ScrollView>
    </View>
  );
};

export default CustomNote;

const styles = StyleSheet.create({
  container: {
    marginTop: 10,
    marginHorizontal: 10,
  },
  title: {
    fontSize: normalizeFontSize(8.4),
    fontWeight: '700',
    color: textcolor.color1,
    marginBottom: 5,
  },
  scrollView: {
    width: '95%',
    maxWidth: '90%',
    height: 150, // Height for scrollable area
  },
  input: {
    backgroundColor: buttoncolor.color,
    padding: 8,
    borderRadius: 5,    
    color: 'black',
    fontSize: 11,
    maxWidth: '100%',
    fontWeight: 'bold',
    // borderColor: '#000',
    // borderWidth: 1,
  },
});
