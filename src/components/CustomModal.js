import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, ScrollView } from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { textcolor } from '../styles/style'; 

const CustomModal = ({ visible, onClose, navigation, t }) => {
  const [currentStep, setCurrentStep] = useState('terms'); 
  const [isAgreed, setIsAgreed] = useState(false); 

  const handleContinue = () => {
    setCurrentStep('welcome');
  };

  const handleClose = () => {
    setCurrentStep('terms'); 
    setIsAgreed(false); 
    onClose();
  };

  const toggleAgreement = () => {
    setIsAgreed(!isAgreed);
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={handleClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          
        <View style={{marginTop:-10}}>
        <TouchableOpacity style={styles.topIcon} onPress={handleClose}>
            <AntDesign name="close" size={20} color={'#ffffff'} />
          </TouchableOpacity>
        </View>
          {currentStep === 'terms' ? (
            <>
              <Text style={styles.modalTitle}>{t('Terms and Conditions')}</Text>
              <ScrollView style={styles.termsContainer}>
                <Text style={styles.text1}>
                  {`Terms and Conditions set out the terms that apply to the relationship, and the conditions that must be met by both parties, such as what the user must not do when using the site or service, and what rights the business maintains.\n\n`}
                  {`You must not engage in the following activities:\n`}
                  {`1. Unauthorized access to systems.\n`}
                  {`2. Distribution of malicious software.\n`}
                  {`3. Any form of harassment or abusive behavior.\n`}
                  {`4. Misrepresentation of your identity or intent.\n`}
                  {`5. Any other activities that violate the law or these terms.\n`}
                </Text>
              </ScrollView>
              
              {/* Agreement Checkbox */}
              <View style={styles.agreementContainer}>
                <TouchableOpacity onPress={toggleAgreement} style={styles.checkbox}>
                  {isAgreed && <AntDesign name="check" size={20} color="#0C568A" />}
                </TouchableOpacity>
                <Text style={styles.agreementText}>
                  {`I have read and agree to the Terms and Conditions and Privacy Policy`}
                </Text>
              </View>

              {/* Continue Button */}
              <TouchableOpacity 
                style={[styles.button, { backgroundColor: isAgreed ? '#0C568A' : '#A9A9A9' }]} 
                onPress={handleContinue}
                disabled={!isAgreed} 
              >
                <Text style={styles.btnText}>{t('Continue')}</Text>
              </TouchableOpacity>
            </>
          ) : (
            <>
              <Text style={styles.modalTitle}>{t('Welcome')}</Text>
              <Text style={styles.text1}>
                {`Welcome to our application! We're excited to have you on board. Let's get started by setting up your profile and preferences your profile and preferences.\n`}
                 {`Welcome to our application! We're excited to have you on board. Let's get started by setting up your profile and preferences.`}
              </Text>

              {/* Footer Section */}
              <View style={styles.foter}>
                
                <View style={styles.footerRow}>
                  <Text style={styles.text1}>If you're already a user, go to 
                  <TouchableOpacity
                    onPress={handleClose}
                    accessibilityLabel="Navigate to Home"
                  >
                    <Text style={styles.text2}>Home</Text>
                  </TouchableOpacity>

                  <Text style={styles.text1}>If this is your first login, go to </Text>
                  <TouchableOpacity
                    onPress={() => navigation.navigate('Rapportsetting')}
                    accessibilityLabel="Navigate to Add Report Setting"
                  >
                    <Text style={styles.text2}>Add Report Setting</Text>
                  </TouchableOpacity>

                  </Text>           
                </View>
              </View>
              <TouchableOpacity
                style={styles.button}
                onPress={() => navigation.navigate('Rapportsetting')}
              >
                <Text style={styles.btnText}>{t('Add Report')}</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', 
  },
  modalContent: {
    width: '85%',
    maxHeight: '80%',
    padding: 20,
    backgroundColor: '#e6e8e3',
    borderRadius: 10,
    
    position: 'relative', 
  },
  topIcon: {
    backgroundColor: "#A9A9A9",
    width: 25,
    height: 25,
    borderRadius: 20,
    position: 'absolute',
   // top: -20, 
    alignSelf: 'flex-end',
    justifyContent: 'center',
    alignItems: 'center',
  
  },
  modalTitle: {
    marginTop: 25,
    marginBottom: 15,
    textAlign: 'center',
    color: textcolor.color1,
    fontSize: 18,
    fontWeight: '700',
  },
  termsContainer: {
    width: '100%',
    marginBottom: 20,
    
  },
  text1: {
    fontSize: 14,
    fontWeight: '400',
    color: '#333',
    textAlign: 'left',
  },
  agreementContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#0C568A',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  agreementText: {
    fontSize: 14,
    fontWeight: '400',
    color: '#333',
    flex: 1,
    flexWrap: 'wrap',
  },
  button: {
    width: '100%',
    height: 45,
    backgroundColor: '#0C568A',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
   
  },
  btnText: {
    fontSize: 16,
    fontWeight: '700',
    color: 'white',
  },
  foter:{
    flexDirection:"row",
    paddingVertical:15,
   //  paddingHorizontal:10,
   //  flexWrap:'wrap'
 
   },
     footerRow: {
     flexDirection: 'row',
     alignItems: 'center',
     marginBottom: 10,
    // marginHorizontal:10,
     flexWrap:'wrap',
     justifyContent: 'center', // Center the content horizontally within the row
   },
   text1:{
    fontSize:12,
    fontWeight:'400',
    color: '#333',
   },
   text2:{
     fontSize: 14,
         fontWeight: '700',
         textAlign:'center',
         marginBottom:-5,
         color: textcolor.color2,
         marginHorizontal: 5, 
         textDecorationLine:'underline'
   },
});

export default CustomModal;



