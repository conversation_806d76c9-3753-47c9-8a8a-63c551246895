import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { Image } from 'react-native-elements';
import { useTranslation } from 'react-i18next';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import LanguageSelector from '../Language/LanguageSelector';
import { buttoncolor } from '../styles/style';
import { userEndpoints } from '../services/endpoints.service';
import authService from '../services/features/auth/auth.service';

const CustomMenu = () => {
  const navigation = useNavigation();
  const { t } = useTranslation();
  const [name, setName] = useState(null);
  const [taxiNumber, setTaxiNumber] = useState(null);
  const [companyLogo, setCompanyLogo] = useState('');
  const [profilePic, setProfilePic] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);

  const closeModal = () => setModalVisible(false);

  // Fetch user data from AsyncStorage
  const fetchUserData = async () => {
    try {
      const user = await AsyncStorage.getItem('User');
      const parsedUser = user ? JSON.parse(user) : null;
      setName(parsedUser?.name || null);
      setTaxiNumber(parsedUser?.taxiNumber || null);
      setCompanyLogo(parsedUser?.taxiCompany?.companyLogo || null);
      setProfilePic(parsedUser?.profilePic || null);
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };

  // Use useFocusEffect to refresh data when the screen is focused
  useFocusEffect(
    React.useCallback(() => {
      fetchUserData();
    }, []),
  );

const deleteAccount = async () => {
  try {
    const userJson = await AsyncStorage.getItem('User');
    const user = userJson ? JSON.parse(userJson) : null;

    if (!user || !user._id) {
      Alert.alert('Error', 'User not found');
      return;
    }
    Alert.alert(
      'Confirm Delete',
      'Do you really want to delete your account?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await authService.deleteAccount(user._id);
              console.log('✅ Delete Response:', response);

              if (response.message === "Your account has been successfully deleted.") {
                await AsyncStorage.clear();
                Alert.alert('Deleted', 'Your account has been deleted.');
                navigation.replace('SignIn'); 
              } else {
                Alert.alert('Failed', response.message || 'Something went wrong');
              }
            } catch (error) {
              console.error('❌ Delete Error:', error);
              Alert.alert('Error', error.message || 'Server Error');
            }
          },
        },
      ]
    );
  } catch (error) {
    console.error('❌ Outer Delete Error:', error);
    Alert.alert('Error', 'Something went wrong');
  }
};



  return (
    <View style={styles.container}>
      {/* Three-dot button */}
      <TouchableOpacity
        style={styles.menuButton}
        hitSlop={{ top: 40, bottom: 40, left: 40, right: 40 }}
        onPress={() => setModalVisible(true)}>
        <View style={styles.dotYellow} />
        <View style={styles.dotBlue} />
        <View style={styles.dotBlack} />
      </TouchableOpacity>
      <Modal
        transparent={true}
        animationType="fade"
        visible={modalVisible}
        onRequestClose={closeModal}>
        <TouchableWithoutFeedback onPress={closeModal}>
          <View style={styles.modalOverlay}>
            <View
              style={styles.modalContainer}
              onTouchStart={e => e.stopPropagation()}>
              <View style={styles.menuHeader}>
                <View style={styles.userDetails}>
                  <Image
                    source={
                      profilePic
                        ? { uri: profilePic }
                        : require('../assets/default/defaultPerson.jpeg')
                    }
                    style={styles.profilePic}
                    PlaceholderContent={<Text>Loading...</Text>}
                  />
                  <Text style={styles.userDetailsText}>{name}</Text>
                </View>
                <View style={styles.companyDetails}>
                  <Image
                    source={
                      companyLogo
                        ? { uri: companyLogo }
                        : require('../assets/Image/Menu/Upload.png')
                    }
                    style={styles.companyLogo}
                    PlaceholderContent={<Text>Loading...</Text>}
                  />
                  <Text style={styles.companyDetailsText}>{taxiNumber}</Text>
                </View>
              </View>

              <View style={styles.menuContent}>
                <View style={styles.modalMenuItem}>
                  <Icon
                    name="globe"
                    size={24}
                    color={buttoncolor.bgcolor}
                    style={{ marginTop: 3 }}
                  />
                  <View style={styles.modalMenuText1}>
                    <LanguageSelector />
                  </View>
                </View>
                <TouchableOpacity
                  style={styles.modalMenuItem}
                  onPress={() => navigation.navigate('Profilescreen')}>
                  <Icon name="user" size={24} color={buttoncolor.bgcolor} />
                  <Text style={styles.modalMenuText}>{t('Profile')}</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.modalMenuItem}
                  onPress={() => navigation.navigate('Rapportsetting')}>
                  <Icon name="settings" size={24} color={buttoncolor.bgcolor} />
                  <Text style={styles.modalMenuText}>{t('Setting')}</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.modalMenuItem}>
                  <Icon name="upload" size={24} color={buttoncolor.bgcolor} />
                  <Text style={styles.modalMenuText}>{t('Document')}</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.modalMenuItem}>
                  <Icon name="archive" size={24} color={buttoncolor.bgcolor} />
                  <Text style={styles.modalMenuText}>{t('Archive')}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.modalMenuItem}
                  onPress={() => navigation.navigate('Contect')}>
                  <Icon name="phone" size={24} color={buttoncolor.bgcolor} />
                  <Text style={styles.modalMenuText}>{t('Important')}</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.modalMenuItem}>
                  <Icon name="edit-3" size={24} color={buttoncolor.bgcolor} />
                  <Text style={styles.modalMenuText}>{t('Notes')}</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.modalMenuItem}>
                  <Icon name="lock" size={24} color={buttoncolor.bgcolor} />
                  <Text style={styles.modalMenuText}>{t('Logs')}</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.modalMenuItem}
                  onPress={deleteAccount}>
                  <AntDesign name="deleteuser" size={24} color={buttoncolor.bgcolor} />
                  <Text style={styles.modalMenuText}>{t('deleteaccount')}</Text>
                </TouchableOpacity>



              </View>
              <View style={styles.logoutContainer}>
                <TouchableOpacity
                  style={styles.modalMenuItem}
                  onPress={async () => {
                    try {
                      // Clear AsyncStorage
                      await AsyncStorage.removeItem('User');
                      await AsyncStorage.removeItem('reportSettings');
                      // await AsyncStorage.clear();
                      // Navigate to the SignIn screen
                      navigation.navigate('SignIn');
                    } catch (error) {
                      console.error('Error clearing AsyncStorage:', error);
                    } finally {
                      closeModal(); // Close the modal
                    }
                  }}>
                  <Icon name="log-out" size={24} color={buttoncolor.bgcolor} />
                  <Text style={styles.modalMenuText}>{t('Logout')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  dotYellow: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#F1C50D',
  },
  dotBlue: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: buttoncolor.bgcolor,
    marginHorizontal: 3,
  },
  dotBlack: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: 'black',
  },
  menuButton: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    width: '22%',
    padding: 15,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  modalContainer: {
    justifyContent: 'space-between',
    backgroundColor: '#C9C9CD',
    borderRadius: 8,
    padding: 10,
    width: '45%',
    maxWidth: '80%',
    height: '80%',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 5,
  },

  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  closeButton: {
    marginBottom: 30,
  },
  menuHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingVertical: 10,
  },

  userDetails: {
    alignItems: 'flex-start',
    flex: 1,
    marginRight: 4,
  },
  userDetailsText: {
    fontSize: 12,
    color: 'black',
    paddingVertical: 5,
    flexWrap: 'nowrap',
    width: '100%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  companyDetailsText: {
    fontSize: 12,
    color: 'black',
    paddingVertical: 10,
    alignSelf: 'center',
  },
  menuContent: {
    marginTop: '-10%',
  },
  modalMenuItem: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    paddingVertical: 5.8,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  modalMenuText: {
    fontSize: 12,
    fontWeight: '400',
    alignSelf: 'center',
    paddingHorizontal: 15,
    color: 'black',
  },
  modalMenuText1: {
    width: '98%',
    fontSize: 12,
    fontWeight: '400',
    alignSelf: 'flex-start',
    marginLeft: -24,
    color: 'black',
  },
  modalMenuItem1: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    paddingVertical: 6,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  companyDetails: {
    width: '30%',
    //alignItems:'flex-end'
  },
  profilePic: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  companyLogo: {
    width: '100%',
    height: 30,
    resizeMode: 'contain',
  },
});

export default CustomMenu;
