import React, {useState, useEffect} from 'react';
import {View, Text, Switch, StyleSheet} from 'react-native';
import {buttoncolor, textcolor} from '../styles/style';

const CustomSwitchWithLabel = ({
  label,
  onValueChange,
  initialEnabled,
  value,
}) => {
  const [isEnabled, setIsEnabled] = useState(initialEnabled || false); // Default toggle state

  // Update state when initialEnabled prop changes
  useEffect(() => {
    setIsEnabled(initialEnabled);
  }, [initialEnabled]);

  const toggleSwitch = () => {
    setIsEnabled(previousState => !previousState);
    onValueChange && onValueChange(!isEnabled);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}:</Text>
      <View style={styles.switchInputWrapper}>
        <Switch
          trackColor={{false: buttoncolor.color, true: buttoncolor.bgcolor}}
          thumbColor={isEnabled ? buttoncolor.bgcolor : 'grey'}
          ios_backgroundColor={buttoncolor.color}
          onValueChange={toggleSwitch}
          value={isEnabled}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
  },
  label: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: '1%',
    color: textcolor.color1,
    fontFamily: 'Roboto-Medium',
    flex: 1,
  },
  switchInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 0.9,
  },
});

export default CustomSwitchWithLabel;
