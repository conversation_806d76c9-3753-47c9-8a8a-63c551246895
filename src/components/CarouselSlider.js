import {Image, StyleSheet, View} from 'react-native';
import React from 'react';
import Swiper from 'react-native-swiper';

const CarouselSlider = ({images}) => {
  return (
    <View style={styles.offerslider}>
      <Swiper
        // showsButtons={true}
        dotColor="#F1C50D"
        autoplayTimeout={5}
        activeDotColor="#31A8F7"
        autoplay={true}>
        {images.map((image, index) => (
          <View key={index} style={styles.slide}>
            <Image source={image} style={styles.image} />
          </View>
        ))}
      </Swiper>
    </View>
  );
};

export default CarouselSlider;

const styles = StyleSheet.create({
  offerslider: {
    width: '100%',
    height: 240,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: -4,
    marginTop:-20
  },
  slide: {
    width: '100%',
  
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
   height: '100%',
    resizeMode: 'contain',
    borderRadius: 20,
  },
});
