import React, { useEffect, useState } from 'react';
import { Text, View, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Dropdown } from 'react-native-element-dropdown';
import { buttoncolor, textcolor } from '../styles/style';

const languages = [
  { label: 'English', value: 'en' },
  { label: 'Svenska', value: 'sv' },
  { label: 'Arabic', value: 'ar' },
  { label: 'Danish', value: 'de' },
  { label: 'Persian', value: 'ps' },
  { label: 'Kurdish', value: 'ka' }
];

const LanguageSelector = ({ userId }) => {
  const { t, i18n } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);

  useEffect(() => {
    const loadLanguage = async () => {
      try {
        // Get the saved language for the specific userId
        const savedLanguage = await AsyncStorage.getItem(`${userId}-language`);
        if (savedLanguage) {
          // Change the language using i18n
          i18n.changeLanguage(savedLanguage);
          setCurrentLanguage(savedLanguage); // Update the state
        }
      } catch (error) {
        console.error('Error loading language from AsyncStorage:', error);
      }
    };

    loadLanguage();
  }, [i18n, userId]); // Re-run when userId or i18n changes

  const changeLanguage = async (language) => {
    try {
      // Change language in i18n
      await i18n.changeLanguage(language);
      // Store the selected language with userId in AsyncStorage
      await AsyncStorage.setItem(`${userId}-language`, language);
      setCurrentLanguage(language); // Update the local state
    } catch (error) {
      console.error('Error changing language:', error);
    }
  };

  return (
    <View style={styles.container}>
      <Dropdown
        style={styles.dropdown}
        placeholderStyle={styles.placeholderStyle}
        selectedTextStyle={styles.selectedTextStyle}
        inputSearchStyle={styles.inputSearchStyle}
        iconStyle={styles.iconStyle}
        data={languages}
        maxHeight={300}
        labelField="label"
        valueField="value"
        placeholder={t('selectLanguage')}
        value={currentLanguage} 
        renderItem={(item) => (
          <View style={[styles.item, { backgroundColor: buttoncolor.color }]}>
            <Text style={styles.itemText}>{item.label}</Text>
          </View>
        )}
        onChange={(item) => changeLanguage(item.value)} 
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  dropdown: {
    height: 20,
    borderRadius: 10,
    paddingHorizontal: 15,
    width: '80%',
    backgroundColor: buttoncolor.color,
    alignSelf: 'flex-end',
    marginHorizontal: 5,
    marginTop: 5,
  },
  placeholderStyle: {
    color: textcolor.color1,
    fontSize: 14,
  },
  selectedTextStyle: {
    fontSize: 12,
    fontWeight: '400',
    color: textcolor.color1,
    fontFamily: 'Roboto-Bold',
  },
  iconStyle: {
    width: 25,
    height: 25,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
    color: textcolor.color1,
    backgroundColor: buttoncolor.color,
  },
  item: {
    padding: 10,
  },
  itemText: {
    fontSize: 16,
    color: textcolor.color1,
  },
});

export default LanguageSelector;