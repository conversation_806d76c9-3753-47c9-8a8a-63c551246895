import 'intl';
import 'intl-pluralrules';
import 'intl/locale-data/jsonp/en';
import 'intl/locale-data/jsonp/es';
import 'intl/locale-data/jsonp/ur';
import 'intl/locale-data/jsonp/ar';
import 'intl/locale-data/jsonp/sv';
import 'intl/locale-data/jsonp/de';
import 'intl/locale-data/jsonp/ps';
import 'intl/locale-data/jsonp/ka';
import i18n from 'i18next';
import {initReactI18next} from 'react-i18next';
import HttpBackend from 'i18next-http-backend';
import AsyncStorage from '@react-native-async-storage/async-storage';
// Custom language detector to use AsyncStorage
const languageDetector = {
  type: 'languageDetector',
  async: true,
  detect: async callback => {
    try {
      // Retrieve saved language from AsyncStorage
      const savedLanguage = await AsyncStorage.getItem('selectedLanguage');
      // If no language is found in storage, default to system language
      const language = savedLanguage || 'sv'; // Default to 'en' if nothing is stored
      callback(language);
    } catch (e) {
      // If there is an error retrieving, fallback to 'en'
      callback('sv');
    }
  },
  init: () => {},
  cacheUserLanguage: async lng => {
    try {
      await AsyncStorage.setItem('selectedLanguage', lng); // Cache the selected language
    } catch (e) {
      console.error('Failed to cache language', e);
    }
  },
};

const resources = {
  en: {
    translation: {
      account: 'Account',
      changeLanguage: 'Change Language',
      createAccount: 'Create An Account',
      fullName: 'Full Name',
      enterFullName: 'Enter your Full Name',
      email: 'Email',
      enterEmail: 'Enter your Email',
      password: 'Password',
      enterPassword: 'Enter your Password',
      selectTaxiCompany: 'Taxi Company',
      selectCompany: 'Select Company',
      taxiNumber: 'Taxi Number',
      enterTaxiNumber: 'Enter Taxi Number',
      signup: 'Sign up',
      alreadyHaveAccount: 'Already have an account?',
      loginHere: 'Sign In Here',
      letsgetsStarted: 'Let Get Started',
      signIn: 'Sign In',
      forgotPass: 'Forgot Password?',
      titleforgotPass: 'Forgot Password',
      checkEmail: 'Check Your Email',
      send: 'Send',
      areNew: 'Are you New?',
      backtoSignin: 'Back To Sign in',
      verification: 'Verification',
      enterVerification: 'Enter Verification Code',
      ifyounotRecieve: "If you didn't recieve the code?",
      resend: 'Resend',
      newpassword: 'New Password',
      enternewPass: 'Enter New Password',
      confirmPass: 'Confirm Password',
      enterconfirmPass: 'Enter Confirm Password',
      mobileno: 'Mobile Number',
      entermobileno: 'Enter Your Mobile Number',
      home: 'Home',
      addreport: 'Add Report',
      viewreport: 'View Report',
      graph: 'Monthly Graph',
      Date: 'Date',
      Cash: 'Cash',
      Card: 'Card',
      PrePaid: 'Pre-Paid',
      Tip: 'Tip',
      Kastrup: 'Kastrup Airport',
      Station: 'Station C/H',
      Sturup: 'Sturup Airport',
      Extra: 'Extra Bill',
      Note: 'Note',
      Before: 'Before Detection',
      After: 'After Detection',
      Submit: 'Submit',
      Report: 'Report', /// View Report
      TotalBefore: 'Total Before Detection',
      TotalAfter: 'Total After Detection',
      TotalTip: 'Total Tip',
      TotalExtra: 'Total Extra Bills',
      TotalCash: 'Total Cash',
      TotalPrepaid: 'Total Prepaid',
      TotalCard: 'Total Card',
      Pec: 'Pec 40%',
      Holiday: 'Holiday Pay',
      Tax: 'Tax',
      TotalKastrup: 'Total Kastrup Airport',
      TotalStation: 'Total Station C/H',
      TotalSturup: 'Total Sturup Airport',
      Final: 'Final Pay',
      ViewGrap: 'View Graph',
      Profile: 'Profile', // menu
      Setting: 'Setting',
      Document: 'Doucument',
      Archive: 'Archive',
      Important: 'Important Contacts',
      Notes: 'Notes',
      Logs: 'Log',
      Logout: 'Logout',
      Edit: 'Edit Profile',
      Save: 'Save',
      Reportsetting: 'Report Setting',
      Total: 'Total',
      TotalAfter6: 'Total After 6%',
      OwnerPercentage: 'Owner Percentage',
      DriverPercentage: 'Driver Percentage',
      FinalCash: 'Final Cash',
      OwnerIncome: 'Owner Income',
      DriverIncome: 'Driver Income',
      BeforeTaxTotal: 'Before Tax Total',
      DeleteReport: 'Delete Report',
      Addreport: 'Add Report',
      Ftj: 'FTJ',
      Skol: 'SKOL',
      TotalSkol: 'Total Skol',
      Aftertax: 'After TaxTotal',
      View: 'View',
      Share: 'Share',
      Graph: 'Graph',
      day: 'Day',
      bdet: 'B-Det',
      Adet: 'A-det',
      Dper: 'Driver-%',
      Next:"Next",
      days: {
        Sun: 'Sun',
        Mon: 'Mon',
        Tue: 'Tue',
        Wed: 'Wed',
        Thu: 'Thu',
        Fri: 'Fri',
        Sat: 'Sat',
      },
      months: {
        jan: 'January',
        feb: 'February',
        mar: 'March',
        apr: 'April',
        may: 'May',
        jun: 'June',
        jul: 'July',
        aug: 'August',
        sep: 'September',
        oct: 'October',
        nov: 'November',
        dec: 'December',
      },
      deleteaccount:'Delete Account',
      graphAndYearlyData: 'Graph and Yearly Data',
      graphData: 'Graph Data',
      yearlyData: 'Yearly Data',
      successfullyregistered:'You have successfully registered!',
      description:' Your registration for Taxi Rapport has been received. The admin will review your details and send a confirmation upon approval. Stay tuned!'
      
    },
  },
  ar: {
    translation: {
      // arabic
      welcome: 'أهلاً وسهلاً',
      changeLanguage: 'تغيير اللغة',
      account: 'الحساب',
      // changeLanguage: 'تغيير اللغة',
      createAccount: 'إنشاء حساب',
      fullName: 'الاسم الكامل',
      enterFullName: 'أدخل اسمك الكامل',
      email: 'البريد الإلكتروني',
      enterEmail: 'أدخل بريدك الإلكتروني',
      password: 'كلمة المرور',
      enterPassword: 'أدخل كلمة المرور',
      selectTaxiCompany: 'شركة التاكسي',
      selectCompany: 'اختر الشركة',
      taxiNumber: 'رقم التاكسي',
      enterTaxiNumber: 'أدخل رقم التاكسي',
      signup: 'اشترك',
      alreadyHaveAccount: 'هل لديك حساب بالفعل؟',
      loginHere: 'تسجيل الدخول هنا',
      letsgetsStarted: 'لنبدأ',
      signIn: 'تسجيل الدخول',
      forgotPass: 'هل نسيت كلمة المرور؟',
      titleforgotPass: 'نسيت كلمة المرور',
      checkEmail: 'تحقق من بريدك الإلكتروني',
      send: 'إرسال',
      areNew: 'هل أنت جديد؟',
      backtoSignin: 'العودة إلى تسجيل الدخول',
      verification: 'التحقق',
      enterVerification: 'أدخل رمز التحقق',
      ifyounotRecieve: 'إذا لم تستلم الرمز؟',
      resend: 'إعادة الإرسال',
      newpassword: 'كلمة مرور جديدة',
      enternewPass: 'أدخل كلمة المرور الجديدة',
      confirmPass: 'تأكيد كلمة المرور',
      enterconfirmPass: 'أدخل تأكيد كلمة المرور',
      mobileno: 'رقم الهاتف المحمول',
      entermobileno: 'أدخل رقم هاتفك المحمول',
      home: 'الرئيسية',
      addreport: 'إضافة تقرير',
      viewreport: 'عرض التقرير',
      graph: 'الرسم البياني الشهري',
      Date: 'التاريخ',
      Cash: 'نقدًا',
      Card: 'بطاقة',
      PrePaid: 'مدفوع مسبقًا',
      Tip: 'البقشيش',
      Kastrup: 'مطار كاستروب',
      Station: 'محطة C/H',
      Sturup: 'مطار ستوروب',
      Extra: 'فاتورة إضافية',
      Note: 'ملاحظة',
      Before: 'قبل الكشف',
      After: 'بعد الكشف',
      Submit: 'إرسال',
      Report: 'تقرير',
      TotalBefore: 'الإجمالي قبل الكشف',
      TotalAfter: 'الإجمالي بعد الكشف',
      TotalTip: 'إجمالي البقشيش',
      TotalExtra: 'إجمالي الفواتير الإضافية',
      TotalCash: 'إجمالي النقد',
      TotalPrepaid: 'إجمالي المدفوع مسبقًا',
      TotalCard: 'إجمالي بطاقة',
      Pec: 'بيك %40',
      Holiday: 'إجازة مدفوعة',
      Tax: 'الضريبة',
      TotalKastrup: 'إجمالي مطار كاستروب',
      TotalStation: 'إجمالي محطة C/H',
      TotalSturup: 'إجمالي مطار ستوروب',
      Final: 'الدفعة النهائية',
      ViewGrap: 'عرض الرسم البياني',
      Profile: 'المستخدم',
      Setting: 'الإعدادات',
      Document: 'الوثيقة',
      Archive: 'الأرشيف',
      Important: 'جهات اتصال هامة',
      Notes: 'ملاحظات',
      Logs: 'السجلات',
      Logout: 'تسجيل الخروج',
      Edit: 'تعديل الملف الشخصي',
      Save: 'حفظ',
      Reportsetting: 'إعداد التقرير',
      Total: 'الإجمالي',
      TotalAfter6: 'الإجمالي بعد 6%',
      OwnerPercentage: 'نسبة المالك',
      DriverPercentage: 'نسبة السائق',
      FinalCash: 'النقد النهائي',
      OwnerIncome: 'دخل المالك',
      DriverIncome: 'دخل السائق',
      BeforeTaxTotal: 'الإجمالي قبل الضريبة',
      DeleteReport: 'حذف التقرير',
      Addreport: 'إضافة تقرير',
      Ftj: 'FTJ',
      Skol: 'مدرسة',
      TotalSkol: 'إجمالي المدرسة',
      Aftertax: 'إجمالي بعد الضريبة',
      View: 'عرض',
      Share: 'مشاركة',
      Graph: 'رسم بياني',
      day: 'يوم',
      bdet: 'خصم مسبق',
      Adet: 'خصم بعدي',
      Dper: 'سائق-%',
      Next: "التالي",
      months: {
        jan: 'يناير',
        feb: 'فبراير',
        mar: 'مارس',
        apr: 'أبريل',
        may: 'مايو',
        jun: 'يونيو',
        jul: 'يوليو',
        aug: 'أغسطس',
        sep: 'سبتمبر',
        oct: 'أكتوبر',
        nov: 'نوفمبر',
        dec: 'ديسمبر',
      },
      deleteaccount:'حذف الحساب',
      graphAndYearlyData: 'الرسم البياني والبيانات السنوية',
      graphData: 'بيانات الرسم البياني',
      yearlyData: 'البيانات السنوية',
      successfullyregistered: "لقد قمت بالتسجيل بنجاح!",
     description: "تم استلام تسجيلك في Taxi Rapport. سيقوم المسؤول بمراجعة تفاصيلك وإرسال تأكيد عند الموافقة. ترقب المزيد!"
    },
  },
  sv: {
    // swedish
    translation: {
      welcome: 'Välkommen',
      changeLanguage: 'Ändra Språk',
      createAccount: 'Skapa Ett Konto',
      fullName: 'Fullständigt Namn',
      enterFullName: 'Ange ditt Fullständiga Namn',
      email: 'E-post',
      enterEmail: 'Ange din E-post',
      password: 'Lösenord',
      enterPassword: 'Ange ditt Lösenord',
      selectTaxiCompany: 'Taxi Företag',
      selectCompany: 'Välj Företag',
      taxiNumber: 'Taxi Nummer',
      enterTaxiNumber: 'Ange Taxi Nummer',
      signup: 'Registrera',
      alreadyHaveAccount: 'Har du redan ett konto?',
      loginHere: 'Logga In Här',
      letsgetsStarted: 'Låt oss komma igång',
      signIn: 'Logga In',
      forgotPass: 'Glömt Lösenord?',
      titleforgotPass: 'Glömt Lösenord',
      checkEmail: 'Kontrollera din E-post',
      send: 'Skicka',
      areNew: 'Är du Ny?',
      backtoSignin: 'Tillbaka till Inloggning',
      verification: 'Verifiering',
      enterVerification: 'Ange Verifieringskod',
      ifyounotRecieve: 'Om du inte mottagit koden?',
      resend: 'Skicka igen',
      newpassword: 'Nytt Lösenord',
      enternewPass: 'Ange Nytt Lösenord',
      confirmPass: 'Bekräfta Lösenord',
      enterconfirmPass: 'Ange Bekräfta Lösenord',
      mobileno: 'Mobilnummer',
      entermobileno: 'Ange ditt Mobilnummer',
      home: 'Hem',
      addreport: 'Lägg till Rapport',
      viewreport: 'Visa Rapport',
      graph: 'Månatlig Graf',
      Date: 'Datum',
      Cash: 'Kontant',
      Card: 'Kort',
      PrePaid: 'Konto',
      Tip: 'Dricks',
      Kastrup: 'Kastrup Flygplats',
      Station: 'Station C/H',
      Sturup: 'Sturup Flygplats',
      Extra: 'Extra Avgift',
      Note: 'Anteckning',
      Before: 'Innan Avdrag',
      After: 'Efter Avdrag',
      Submit: 'Skicka in',
      Report: 'Rapport',
      TotalBefore: 'Totalt Innan-Avdrag',
      TotalAfter: 'Totalt Efter-Avdrag',
      TotalTip: 'Totalt Dricks',
      TotalExtra: 'Totalt Extra Avgifter',
      TotalCash: 'Totalt Kontanter',
      TotalPrepaid: 'Totalt Konto',
      TotalCard: 'Totalt Kort',
      Pec: 'Pec 40%',
      Holiday: 'Semesterersättning',
      Tax: 'Skatt',
      TotalKastrup: 'Totalt Kastrup Flygplats',
      TotalStation: 'Totalt Station C/H',
      TotalSturup: 'Totalt Sturup Flygplats',
      Final: 'Slutlig Lön',
      ViewGrap: 'Visa Graf',
      Profile: 'Användare',
      Setting: 'Inställningar',
      Document: 'Dokument',
      Archive: 'Arkiv',
      Important: 'Viktiga Kontakter',
      Notes: 'Anteckningar',
      Logs: 'Logg',
      Logout: 'Logga Ut',
      Edit: 'Redigera Profil',
      Save: 'Spara',
      Reportsetting: 'Rapportinställning',
      Total: 'Totalt',
      TotalAfter6: 'Totalt efter 6%',
      OwnerPercentage: 'Ägaransdel',
      DriverPercentage: 'Förarensprovision',
      FinalCash: 'Slutlig Kontanter',
      OwnerIncome: 'Ägarens inkomst',
      DriverIncome: 'Förarens inkomst',
      BeforeTaxTotal: 'Totalt Före Skatt',
      DeleteReport: 'Ta bort rapport',
      Addreport: 'Lägg till rapport',
      Ftj: 'FTJ',
      Skol: 'Skola',
      TotalSkol: 'Total Skola',
      Aftertax: 'Efter skatt, total',
      View: 'Tabort',
      Share: 'Dela',
      Graph: 'graf',
      day: 'Dag',
      bdet: 'In-Avdg',
      Adet: 'Eft-Avdg',
      Dper: 'Förare-%',
      Next: "Nästa",
      days: {
        Sun: 'Sön',
        Mon: 'Mån',
        Tue: 'Tis',
        Wed: 'Ons',
        Thu: 'Tors',
        Fri: 'Fre',
        Sat: 'Lör',
      },
      months: {
        jan: 'Januari',
        feb: 'Februari',
        mar: 'Mars',
        apr: 'April',
        may: 'Maj',
        jun: 'Juni',
        jul: 'Juli',
        aug: 'Augusti',
        sep: 'September',
        oct: 'Oktober',
        nov: 'November',
        dec: 'December',
      },
      deleteaccount:'Radera konto',
      graphAndYearlyData: 'Graf och Årliga Data',
      graphData: 'Graf Data',
      yearlyData: 'Årsdata',
      successfullyregistered: "Du har registrerat dig framgångsrikt!",
      description: "Din registrering för Taxi Rapport har mottagits. Administratören kommer att granska dina uppgifter och skicka en bekräftelse vid godkännande. Håll utkik!"
    },
  },
  de: {
    // denish
    translation: {
      account: 'Konto',
      changeLanguage: 'Skift Sprog',
      createAccount: 'Opret En Konto',
      fullName: 'Fulde Navn',
      enterFullName: 'Indtast dit fulde navn',
      email: 'Email',
      enterEmail: 'Indtast din email',
      password: 'Adgangskode',
      enterPassword: 'Indtast din adgangskode',
      selectTaxiCompany: 'Taxafirma',
      selectCompany: 'Vælg Firma',
      taxiNumber: 'Taxinummer',
      enterTaxiNumber: 'Indtast taxinummer',
      signup: 'Tilmeld',
      alreadyHaveAccount: 'Har du allerede en konto?',
      loginHere: 'Log ind her',
      letsgetsStarted: 'Lad os komme i gang',
      signIn: 'Log ind',
      forgotPass: 'Glemt adgangskode?',
      titleforgotPass: 'Glemt Adgangskode',
      checkEmail: 'Tjek din email',
      send: 'sende',
      areNew: 'Er du ny?',
      backtoSignin: 'Tilbage til login',
      verification: 'Verifikation',
      enterVerification: 'Indtast verifikationskode',
      ifyounotRecieve: 'Hvis du ikke modtager koden?',
      resend: 'Send igen',
      newpassword: 'Ny Adgangskode',
      enternewPass: 'Indtast ny adgangskode',
      confirmPass: 'Bekræft Adgangskode',
      enterconfirmPass: 'Indtast bekræftet adgangskode',
      mobileno: 'Mobilnummer',
      entermobileno: 'Indtast ditmobilnummer',
      home: 'Hjem',
      addreport: 'Tilføj Rapport',
      viewreport: 'Se Rapport',
      graph: 'Månedlig Graf',
      Date: 'Dato',
      Cash: 'Kontanter',
      Card: 'Kort',
      PrePaid: 'Forudbetalt',
      Tip: 'tips',
      Kastrup: 'Kastrup Lufthavn',
      Station: 'Statione C/H',
      Sturup: 'Sturup Lufthavn',
      Extra: 'Ekstra Regning',
      Note: 'Bemærkning',
      Before: 'Før Detektion',
      After: 'Efter Detektion',
      Submit: 'Indsend',
      Report: 'Rapport',
      TotalBefore: 'Total FørDetektion',
      TotalAfter: 'Total EfterDetektion',
      TotalTip: 'samlede tips',
      TotalExtra: 'Total EkstraRegninger',
      TotalCash: 'Total Kontanter',
      Pec: 'Pec 40%',
      Holiday: 'Feriepenge',
      Tax: 'Skat',
      TotalKastrup: 'Total Kastrup Lufthavn',
      TotalStation: 'Total statione C/H',
      TotalSturup: 'Total Sturup Lufthavn',
      TotalPrepaid: 'Samlet Forudbetalt',
      TotalCard: 'Samlet Kort',
      Final: 'Endelig Betaling',
      ViewGrap: 'Se Graf',
      Profile: 'Bruger',
      Setting: 'Indstillinger',
      Document: 'Dokument',
      Archive: 'Arkiv',
      Important: 'Vigtige Kontakter',
      Notes: 'Noter',
      Logs: 'Log',
      Logout: 'Log ud',
      Edit: 'Rediger Profil',
      Save: 'Gem',
      Reportsetting: 'Rapportindstillinger',
      Total: 'Total',
      TotalAfter6: 'Total efter 6%',
      OwnerPercentage: 'Ejerandel',
      DriverPercentage: 'Chaufførprocent',
      FinalCash: 'Endelig kontant',
      OwnerIncome: ' Ejerens indkomst',
      DriverIncome: 'Chaufførens indkomst',
      BeforeTaxTotal: 'Samlet før skat',
      DeleteReport: 'Slet Rapport',
      Addreport: 'Tilføj rapport',
      Ftj: 'FTJ',
      Skol: 'SKOL',
      TotalSkol: 'Total Skole',
      Aftertax: 'Efter skat, total',
      View: 'Vis',
      Share: 'Del',
      day: 'Dag',
      bdet: 'Inden-træk',
      Adet: 'Efter-træk',
      Dper: 'Fører-%',
       Next: "Næste",
      months: {
        jan: 'Januar',
        feb: 'Februar',
        mar: 'Marts',
        apr: 'April',
        may: 'Maj',
        jun: 'Juni',
        jul: 'Juli',
        aug: 'August',
        sep: 'September',
        oct: 'Oktober',
        nov: 'November',
        dec: 'December',
      },
      deleteaccount:'Slet konto',
      graphAndYearlyData: 'Graf og Årlige Data',
      graphData: 'Graf Data',
      yearlyData: 'Årlige Data',
      successfullyregistered: "Du har registreret dig succesfuldt!",
      description: "Din registrering til Taxi Rapport er modtaget. Admin vil gennemgå dine oplysninger og sende en bekræftelse ved godkendelse. Følg med!"
    },
  },
  ps: {
    // parision
    translation: {
      account: 'حساب',
      changeLanguage: 'تغییر زبان',
      createAccount: 'ایجاد حساب',
      fullName: 'نام کامل',
      enterFullName: 'نام کامل خود را وارد کنید',
      email: 'ایمیل',
      enterEmail: 'ایمیل خود را وارد کنید',
      password: 'رمز عبور',
      enterPassword: 'رمز عبور خود را وارد کنید',
      selectTaxiCompany: 'شرکت تاکسی',
      selectCompany: 'انتخاب شرکت',
      taxiNumber: 'شماره تاکسی',
      enterTaxiNumber: 'شماره تاکسی را وارد کنید',
      signup: 'ثبت نام',
      alreadyHaveAccount: 'قبلاً حساب دارید؟',
      loginHere: 'وارد شوید',
      letsgetsStarted: 'بیایید شروع کنیم',
      signIn: 'ورود',
      forgotPass: 'رمز عبور را فراموش کرده‌اید؟',
      titleforgotPass: 'فراموشی رمز عبور',
      checkEmail: 'ایمیل خود را بررسی کنید',
      send: 'ارسال',
      areNew: 'آیا تازه‌وارد هستید؟',
      backtoSignin: 'بازگشت به ورود',
      verification: 'تأیید',
      enterVerification: 'کدتأیید را وارد کنید',
      ifyounotRecieve: 'اگر کدرا دریافت نکردید؟',
      resend: 'دوباره ارسال کنید',
      newpassword: 'رمز عبور جدید',
      enternewPass: 'رمزعبورجدید را وارد کنید',
      confirmPass: 'تأیید رمز عبور',
      enterconfirmPass: 'رمزعبور تأیید شده وارد کنید',
      mobileno: 'شماره موبایل',
      entermobileno: 'شماره موبایل خود را کنید',
      home: 'خانه',
      addreport: 'اضافه کردن گزارش',
      viewreport: 'مشاهده گزارش',
      graph: 'گراف ماهیانه',
      Date: 'تاریخ',
      Cash: 'نقد',
      Card: 'کارت',
      PrePaid: 'پیش پرداخت',
      Tip: 'انعام',
      Kastrup: 'فرودگاه کستروپ',
      Station: 'ایستگاه C/H',
      Sturup: 'فرودگاه استورپ',
      Extra: 'صورتحساب اضافی',
      Note: 'یادداشت',
      Before: 'قبل از شناسایی',
      After: 'بعد از شناسایی',
      Submit: 'ارسال',
      Report: 'گزارش',
      TotalBefore: 'مجموع قبل از شناسایی',
      TotalAfter: 'مجموع بعد از شناسایی',
      TotalTip: 'مجموع انعام',
      TotalExtra: 'مجموع صورتحساب‌اضافی',
      TotalCash: 'مجموع نقد',
      TotalPrepaid: 'کل پیش‌پرداخت',
      TotalCard: 'کل کارت',
      Pec: 'پیک 40%',
      Holiday: 'حقوق تعطیلات',
      Tax: 'مالیات',
      TotalKastrup: 'مجموع فرودگاه کستروپ',
      TotalStation: 'مجموع ایستگاه C/H',
      TotalSturup: 'مجموع فرودگاه استورپ',
      Final: 'پرداخت نهایی',
      ViewGrap: 'مشاهده گراف',
      Profile: 'کاربر',
      Setting: 'تنظیمات',
      Document: 'مدرک',
      Archive: 'آرشیو',
      Important: 'مخاطبین مهم',
      Notes: 'یادداشت‌ها',
      Logs: 'ثبت',
      Logout: 'خروج',
      Edit: 'ویرایش پروفایل',
      Save: 'ذخیره',
      Reportsetting: 'تنظیمات گزارش',
      Total: 'مجموع',
      TotalAfter6: 'مجموع بعد از ۶%',
      OwnerPercentage: 'درصد مالک',
      DriverPercentage: 'درصد راننده',
      FinalCash: 'نقد نهایی ',
      OwnerIncome: 'درآمد مالک',
      DriverIncome: 'درآمد راننده',
      BeforeTaxTotal: 'جمع کل قبل از مالیات',
      DeleteReport: 'حذف گزارش',
      Addreport: 'اضافه کردن گزارش',
      Ftj: 'FTJ',
      Skol: 'مدرسه',
      TotalSkol: 'مجموع مدرسه',
      Aftertax: 'مجموع بعد از مالیات',
      View: 'مشاهده',
      Share: 'اشتراک گذاری',
      day: 'روز',
      bdet: 'تخفیف مقدماتی',
      Adet: 'تخفیف بعدی',
      Dper: 'راننده-%',
      Next: "بعدی",
      months: {
        jan: 'ژانویه',
        feb: 'فوریه',
        mar: 'مارس',
        apr: 'آوریل',
        may: 'مه',
        jun: 'ژوئن',
        jul: 'ژوئیه',
        aug: 'اوت',
        sep: 'سپتامبر',
        oct: 'اکتبر',
        nov: 'نوامبر',
        dec: 'دسامبر',
      },
      deleteaccount:'حذف حساب',
      graphAndYearlyData: 'نمودار و داده‌های سالانه',
      graphData: 'داده‌های نمودار',
      yearlyData: 'داده‌های سالانه',
      successfullyregistered: "شما با موفقیت ثبت‌نام کرده‌اید!",
     description: "ثبت‌نام شما برای Taxi Rapport دریافت شده است. مدیر اطلاعات شما را بررسی خواهد کرد و پس از تایید، یک تاییدیه ارسال خواهد شد. با ما همراه باشید!"
    },
  },
  ka: {
    // kurdish
    translation: {
      account: 'Hesab',
      changeLanguage: 'Gorînî Ziman',
      createAccount: 'Çêkirina Hesabekê',
      fullName: 'Navê Têwaw',
      enterFullName: 'Navê Têwawê Binivîse',
      email: 'E-mail',
      enterEmail: 'E-mailê xwe Binivîse',
      password: 'Şîfre',
      enterPassword: 'Şîfreya xwe Binivîse',
      selectTaxiCompany: 'Şîrketa Taxî',
      selectCompany: 'Şîrketê Hilbijêre',
      taxiNumber: 'Hejmara Taxî',
      enterTaxiNumber: 'Hejmara Taxî Binivîse',
      signup: 'Tomar bibe',
      alreadyHaveAccount: 'Hesabê te heye?',
      loginHere: 'Li vir têkeve',
      letsgetsStarted: 'Dest pê bike',
      signIn: 'Têkeve',
      forgotPass: 'Şîfre ji bîr kirî?',
      titleforgotPass: 'Şîfre ji bîr kirî',
      checkEmail: 'E-maîlê xwe kontrol bike',
      send: 'Şandin',
      areNew: 'Te nû yî?',
      backtoSignin: 'Vegere Têkeve',
      verification: 'Pîştrastin',
      enterVerification: 'Koda Pîştrastinê Binivîse',
      ifyounotRecieve: 'Heger koda nedigelî?',
      resend: 'Dîsa bişîne',
      newpassword: 'Şîfreya Nû',
      enternewPass: 'Şîfreya Nû Binivîse',
      confirmPass: 'Piştrastkirina Şîfre',
      enterconfirmPass: 'Şîfreya piştrastkirinê Binivîse',
      mobileno: 'Hejmara Mobîl',
      entermobileno: 'Hejmara Mobîla xwe Binivîse',
      home: 'Mal',
      addreport: 'Raport Zêde bike',
      viewreport: 'Raporta Binêre',
      graph: 'Grafîka Mehanî',
      Date: 'Rojek',
      Cash: 'Perê Destan',
      Card: 'Kart',
      PrePaid: 'Pêşxerandî',
      Tip: 'Şîn',
      Kastrup: 'Banderê Kastrup',
      Station: 'Station C/H',
      Sturup: 'Banderê Sturup',
      Extra: 'Billê Zêde',
      Note: 'Nîşan',
      Before: 'Berî Fêhmkirinê',
      After: 'Paşî Fêhmkirinê',
      Submit: 'Nêrî',
      Report: 'Raport',
      TotalBefore: 'Hemî Berî Fêhmkirinê',
      TotalAfter: 'Hemî Paşî Fêhmkirinê',
      TotalTip: 'Hemî Şîn',
      TotalExtra: 'Hemî Billên Zêde',
      TotalCash: 'Hemî Perê Destan',
      Pec: 'Pec 40%',
      Holiday: 'Maweya Bihevalî',
      Tax: 'Bac',
      TotalKastrup: 'Hemî Banderê Kastrup',
      TotalStation: 'Hemî Station C/H',
      TotalSturup: 'Hemî Banderê Sturup',
      Final: 'Pardana Dîrokî',
      ViewGrap: 'Grafîkê Binêre',
      Profile: 'Bikarhêner',
      Setting: 'Daneyên',
      Document: 'Belge',
      Archive: 'Arşîv',
      Important: 'Paqijî Pêwendîyan',
      Notes: 'Nîşanan',
      Logs: 'Log',
      Logout: 'Derkeve',
      Edit: 'Profîla Biguherîne',
      Save: 'Tomar bike',
      Reportsetting: 'Sazkirina Raporê',
      Total: 'Gehan',
      TotalAfter6: 'Gehan piştî 6%',
      OwnerPercentage: 'Reja Xawenkarî in Latin script',
      DriverPercentage: 'Reja Şofêr in Latin script',
      TotalPrepaid: 'Tevlî Pêşpêdî',
      TotalCard: 'Tevlî Karta',
      FinalCash: 'Pêşînîya Dawî',
      OwnerIncome: 'Hatinê Xwedîkar',
      DriverIncome: 'Hatinê Şofêr',
      BeforeTaxTotal: 'Hemû berê bacê ',
      DeleteReport: 'Jêbirin raporek',
      Addreport: 'Bernameya Zêdekirinê',
      Ftj: 'FTJ',
      Skol: 'Mekteb',
      TotalSkol: 'Hemû Mekteb',
      Aftertax: 'Piştî Bac, hemû',
      View: 'Dîtin',
      Share: 'Parvekirin',
      day: 'Roj',
      bdet: 'Pêşewext',
      Adet: 'Pash Pêşewext',
      Dper: 'Seya-%',
      months: {
        jan: 'Çileyê',
        feb: 'Rebendan',
        mar: 'Adar',
        apr: 'Nîsan',
        may: 'Gulan',
        jun: 'Hezîran',
        jul: 'Tîrmeh',
        aug: 'Tebax',
        sep: 'Îlon',
        oct: 'Teşrîna Pêşîn',
        nov: 'Teşrîna Duhem',
        dec: 'Kanûnê Pêşîn',
        Next: "Piştî",
      },
      deleteaccount:"Hesabê jê bibe",
      graphAndYearlyData: 'şemal û daneyên salane',
      graphData: 'daneyên şemalê',
      yearlyData: 'daneyên salane',
      successfullyregistered: "To be serkeftoyî tomâr kirdûe!",
      description: "Tomarekêt bo Taxi Rapport werbo. Fermiyarekê em zanîyariyane peywendîdekânt debîne û pesendkrdnewey taybetmendekanêt îşî teyîd dîkat. Hewl bide be hewlêkan!"
    },
  },
};

i18n
  .use(HttpBackend)
  .use(languageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'sv', // Fallback to 'en' if no language is set
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;
