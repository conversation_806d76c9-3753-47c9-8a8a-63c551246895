import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Dimensions,
  Keyboard,
} from 'react-native';
import {useTranslation} from 'react-i18next';

// Import your images correctly
import Img1 from '../assets/Image/Img2.png';
import Img3 from '../assets/Image/Img3.png';
import Img4 from '../assets/Image/Img4.png';
import Img5 from '../assets/Image/Img5.png';

const CustomBottomTab = ({state, descriptors, navigation}) => {
  const {t} = useTranslation();
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      },
    );
    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  const tabs = [
    {name: 'home', icon: Img1, route: 'home'},
    {name: 'AddReport', icon: Img3, route: 'Addreport'},
    {name: 'ViewReport', icon: Img4, route: 'Viewreport'},
    {name: 'Graph', icon: Img5, route: 'Graph'},
  ];

  const currentRoute = state.routes[state.index].name;
  if (isKeyboardVisible) {
    return null;
  }

  return (
    <View style={styles.tabBar}>
      {tabs.map((tab, index) => {
        const isActive = currentRoute === tab.route;
        return (
          <TouchableOpacity
            key={index}
            style={styles.tabItem}
            onPress={() => navigation.navigate(tab.route)}>
            <Image
              source={tab.icon}
              style={{
                width: 21,
                height: 21,
                tintColor: isActive ? '#F1C50D' : '#ffff',
              }}
            />
            <Text
              style={[
                styles.tabLabel,
                {color: isActive ? '#F1C50D' : '#ffff'},
              ]}>
              {t(tab.name.toLowerCase())}{' '}
              {/* Assuming your translation keys are lowercase */}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const {width} = Dimensions.get('window');
const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    height: 60,
    paddingVertical: 10,
    backgroundColor: '#42b6f5',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    justifyContent: 'space-around',
    alignItems: 'center',
    width: width,
  },
  tabItem: {
    alignItems: 'center',
  },
  tabLabel: {
    fontSize: 10,
    marginTop: 4,
  },
});

export default CustomBottomTab;
