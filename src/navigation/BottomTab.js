import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Image} from 'react-native';

import Img1 from '../assets/Image/Img2.png';
import Img3 from '../../src/assets/Image/Img3.png';
import Img4 from '../../src/assets/Image/Img4.png';
import Img5 from '../../src/assets/Image/Img5.png';
import {useTranslation} from 'react-i18next';

const BottomTabbar = () => {
  const navigation = useNavigation();
  const {t} = useTranslation();

  return (
    <View style={styles.tabBar}>
      <TouchableOpacity
        style={styles.tabItem}
        onPress={() => navigation.navigate('BottomTab')}>
        <Image
          source={Img1}
          style={{width: 21, height: 21, tintColor: '#ffff'}}
        />
        <Text style={[styles.tabLabel, {color: '#fff'}]}>{t('home')}</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.tabItem}
        onPress={() => navigation.navigate('BottomTab')}>
        <Image
          source={Img3}
          style={{width: 21, height: 21, tintColor: '#ffff'}}
        />
        <Text style={[styles.tabLabel, {color: '#fff'}]}>{t('addreport')}</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.tabItem}
        onPress={() => navigation.navigate('BottomTab')}>
        <Image
          source={Img4}
          style={{width: 21, height: 21, tintColor: '#ffff'}}
        />
        <Text style={[styles.tabLabel, {color: '#fff'}]}>
          {t('viewreport')}
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.tabItem}
        onPress={() => navigation.navigate('BottomTab')}>
        <Image
          source={Img5}
          style={{width: 21, height: 21, tintColor: '#ffff'}}
        />
        <Text style={[styles.tabLabel, {color: '#fff'}]}>{t('graph')}</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    height: 60,
    paddingVertical: 20,
    backgroundColor: '#42b6f5',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  tabItem: {
    alignItems: 'center',
  },
  tabLabel: {
    fontSize: 9,
  },
});

export default BottomTabbar;
