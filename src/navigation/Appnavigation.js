import React from 'react';
import {StyleSheet} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import Signup from '../screens/Signup';
import SignIn from '../screens/SignIn';
import Forgetpassword from '../screens/Forgetpassword';
import Newpassword from '../screens/Newpassword';
import Splashscreen from '../screens/Splashscreen';
import ViewGraph from '../screens/GraphScreen';
import ViewReportScreen from '../screens/ViewReportScreen';
import ProfileScreen from '../screens/ProfileScreen';
import EditProfile from '../screens/EditProfile';
import Home from '../screens/Home';
import CustomBottomTab from './CustomBottomTab';
import AddRapport from '../screens/AddRapport';
import Verification from '../screens/Verification';
import RapportSetting from '../screens/ReportSetting';
import ContactScreen from '../screens/ContectScreen';
import { AppProvider } from '../context/ContextProvider';
import GraphView from '../screens/GraphView';
import RegisterSuccessful from '../screens/RegisterSuccessful';

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

const BottomTabs = () => {
  return (
    <Tab.Navigator
      initialRouteName="home"
      tabBar={props => <CustomBottomTab {...props} />}
      screenOptions={{headerShown: false}}>
      <Tab.Screen name="home" component={Home} />
      <Tab.Screen name="Addreport" component={AddRapport} />
      <Tab.Screen name="Viewreport" component={ViewReportScreen} />
      <Tab.Screen name="Graph" component={ViewGraph} />
    </Tab.Navigator>
  );
};

const Appnavigation = () => {
  return (
    <AppProvider>
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{headerShown: false}} // Hide headers in stack
        initialRouteName="Splashscreen">
        {/* Authentication and Other Stack Screens */}
        <Stack.Screen name="Splashscreen" component={Splashscreen} />
        <Stack.Screen name="Signup" component={Signup} />
        <Stack.Screen name="registersuccessful" component={RegisterSuccessful}/>
        <Stack.Screen name="SignIn" component={SignIn} />
        <Stack.Screen name="Forgetpassword" component={Forgetpassword} />
        <Stack.Screen name="Verification" component={Verification} />
        <Stack.Screen name="Newpassword" component={Newpassword} />
               
        {/* Main App with Bottom Tabs */}
        <Stack.Screen name="Bottomtab" component={BottomTabs} />

        {/* Other Stack Screens */}
        <Stack.Screen name="Rapportsetting" component={RapportSetting} />
        <Stack.Screen name="Profilescreen" component={ProfileScreen} />
        <Stack.Screen name="Editprofile" component={EditProfile} />
        <Stack.Screen name="Contect" component={ContactScreen} />
        <Stack.Screen name="Graphview" component={GraphView}/>
      </Stack.Navigator>
    </NavigationContainer>
    </AppProvider>
  );
};

export default Appnavigation;

const styles = StyleSheet.create({});
