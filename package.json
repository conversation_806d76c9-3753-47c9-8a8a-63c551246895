{"name": "TaxiRapport", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-native-fontawesome": "^0.3.2", "@react-native-async-storage/async-storage": "^2.0.0", "@react-native-community/datetimepicker": "^8.2.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/elements": "^1.3.31", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.11.0", "axios": "^1.7.7", "d3-shape": "^3.2.0", "i18next": "^23.16.4", "i18next-http-backend": "^2.5.2", "intl": "^1.2.5", "intl-pluralrules": "^2.0.1", "react": "18.2.0", "react-i18next": "^14.1.3", "react-native": "0.74.3", "react-native-calendars": "^1.1305.0", "react-native-chart-kit": "^6.12.0", "react-native-confirmation-code-field": "^7.4.0", "react-native-element-dropdown": "^2.12.1", "react-native-elements": "^3.4.3", "react-native-fontawesome": "^7.0.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.23.1", "react-native-google-mobile-ads": "^14.5.0", "react-native-html-to-pdf": "^0.12.0", "react-native-image-picker": "^7.1.2", "react-native-localize": "^3.2.1", "react-native-pie": "^1.1.2", "react-native-pie-chart": "^4.0.0", "react-native-reanimated": "^3.16.7", "react-native-safe-area-context": "^4.10.9", "react-native-safe-area-view": "^1.1.1", "react-native-screens": "^3.32.0", "react-native-share": "^12.0.3", "react-native-svg": "^15.11.1", "react-native-swiper": "^1.6.0", "react-native-table-component": "^1.2.2", "react-native-vector-icons": "^10.1.0", "react-native-view-shot": "^4.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.85", "@react-native/eslint-config": "0.74.85", "@react-native/metro-config": "0.74.85", "@react-native/typescript-config": "0.74.85", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@3.6.4"}