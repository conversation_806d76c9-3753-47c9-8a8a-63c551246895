const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const exclusionList = require('metro-config/src/defaults/exclusionList');
const path = require('path');

const config = {
  resolver: {
    blacklistRE: exclusionList([
      /.*\/__fixtures__\/.*/,
      /.*\/\.git\/.*/,
      /.*\/node_modules\/.*\/node_modules\/react-native\/.*/,
    ]),
  },
  watchFolders: [
    // Only watch what you need (e.g., custom workspaces or local modules)
    path.resolve(__dirname),
  ],
  server: {
    // This helps reduce watching overhead
    enhanceMiddleware: (middleware) => middleware,
  },
  transformer: {
    // Optional: avoid minification during debug
    minifierPath: 'metro-minify-terser',
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
